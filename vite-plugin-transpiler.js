import { createFilter } from '@rollup/pluginutils';

function customTranspilerPlugin(options = {}) {
  const filter = createFilter(options.include, options.exclude);

  return {
    name: 'custom-transpiler',

    transform(code, id) {
      if (filter(id)) {
        // Apply your custom transpiler to the code
        const transformedCode = transpilePythonListComprehension(code);
        return {
          code: transformedCode,
          map: null // Provide source map if needed
        };
      }
      return null;
    }
  };
}

// function transpilePythonListComprehension(code) {
//   // Example transpiler function
//   const regex = /\[(.+?) for (.+?) in range\((\d+)\)\]/g;
//   return code.replace(regex, (match, expr, iter, range) => {
//     return `Array.from({ length: ${range} }, (_, ${iter}) => ${expr})`;
//   });
// }



function transpilePythonListComprehension(code) {
  const listComprehensionRegex = /\[(.+?) for (.+?) in range\((\d+)\)\]/g;
  const stringSlicingRegex = /(\w+)\[(\d*):(\d*)\]/g;

  return code.replace(listComprehensionRegex, (match, expr, iter, range) => {
    const rangeValue = parseInt(range, 10);
    return `Array.from({ length: ${rangeValue} }, (_, ${iter}) => ${expr})`;
  }).replace(stringSlicingRegex, (match, varName, start, end) => {
    if (start === '') start = 0;
    if (end === '') end = varName.length;
    return `${varName}.slice(${start}, ${end})`;
  });
}

export default customTranspilerPlugin;
