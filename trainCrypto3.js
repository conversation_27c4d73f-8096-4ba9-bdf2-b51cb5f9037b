function trainCrypto(crypto, predictionDays) {

    const brain = require('brain.js');
    const { Utils } = require('./utils');


    const PrismaClient = require('@prisma/client').PrismaClient;
    const prisma = new PrismaClient();

    async function retrieveManipulatedData(name) {
        const file = await prisma.trainingData.findMany({
            where: { crypto: name },
        });
        if (file) {
            return file;
        }
        else {
            return new error('no file found')
        }
    }

    async function trainedModelSave(crypto, net, predictionSave) {
        net = JSON.stringify(net)
        if (predictionSave == 10) {
            await prisma.trainedModel.upsert({
                where: { crypto: crypto },
                update: {
                    crypto: crypto,
                    fileContent10: net,
                },
                create: {
                    crypto: crypto,
                    fileContent10: net,
                },
            })}

            if (predictionSave == 1) {
                await prisma.trainedModel.upsert({
                    where: { crypto: crypto },
                    update: {
                        crypto: crypto,
                        fileContent1: net,
                    },
                    create: {
                        crypto: crypto,
                        fileContent1: net,
                    },
                })
            }
        }
let results = null

        retrieveManipulatedData(crypto).then((res) => {
            // console.log(res[0].fileContent10)
            console.log('res', res)
            if (predictionDays == 10) {
                console.log('predict 10 executed')
                let sendit = JSON.parse(res[0].fileContent10)
                results = trainNeuralnet(crypto, predictionDays, sendit).then((net)=>{
                console.log('crypto',crypto),
                console.log(net),
                trainedModelSave(crypto, net, predictionDays)
                }
                )
            }
            if (predictionDays == 1) {
                console.log('predict 1 executed')
                let sendit = JSON.parse(res[0].fileContent1)
                results = trainNeuralnet(crypto, predictionDays, sendit).then((net)=>{
                console.log('crypto',crypto),
                console.log(net),
                trainedModelSave(crypto, net, predictionDays)
                }
                )
            }
        })
        // .then(
        //     console.log('crypto',crypto),
        //     trainedModelSave(crypto, results, predictionDays)
        // )

        // const { getHistoricalData } = require('./getHistoricalData');
        // const { dataManipulation } = require('./dataManipulation');

        const fs = require('fs');
        // console.log(typeof predictionDays)
        // trainNeuralnet(crypto,predictionDays)

        async function trainNeuralnet(crypto, predictionDays, trainingdata) {
            // await getHistoricalData(crypto);
            // dataManipulation(crypto,predictionDays);
            // let trainingdata = require(`./TrainingData/${crypto}TrainingData_${predictionDays}DayPrediction.js`);
            const cryptoFunctions = new Utils(crypto)

            const net = new brain.NeuralNetworkGPU({
                hiddenLayers: [200, 200],
                activation: 'leaky-relu'
            });

            net.train(trainingdata, {
                // learningRate: 0.05,
                iterations: 200,
                //iterations: 5,
                errorThresh: 0.0000000000000000001,
                log: true,
                logPeriod: 1,
            });
            const json = net.toJSON();
            fs.writeFileSync(`./CryptoModels/trained${crypto}_${predictionDays}DayPrediction.json`, JSON.stringify(json));
            console.log(trainingdata[trainingdata.length - 1].input)
            // Test the trained neural network
            const output1 = net.run(
                trainingdata[trainingdata.length - 1].input
            );
            // Display the results
            console.log('First test', output1);
            console.log('First test denormalized', cryptoFunctions.denormalize(output1.Result));
            return json

        }
    }


// trainCrypto('polkadot', 10);
console.log('what happend')

module.exports = {
    trainCrypto
};
