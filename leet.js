
const input1 = "()"
const input2 = "()[]{}"
const input3 = "(]"
const input4 = "]()"
const input5 = "([)]"
const input6 = "{[]}"
const input7 = '['
const input8 = '(){}}{'
const input9 = '"({{{{}}}))"'
const input10 = '[([]])'
const input11 = "[([]])"
const input12 = "[{()}]"




function isValidParentheses(input) {
    
    let occurances = {
        '(': 0,
        ')': 0,
        '{': 0,
        '}': 0,
        '[': 0,
        ']': 0
    }
    let leftSymmetry = {
        '(': 0,
        ')': 0,
        '{': 0,
        '}': 0,
        '[': 0,
        ']': 0
    }
    let rightSymmetry = {
        '(': 0,
        ')': 0,
        '{': 0,
        '}': 0,
        '[': 0,
        ']': 0
    }
    let inputToArray = input.split('')
    console.log(inputToArray)

    let stopPoint = 0
    let evenpoint = (input.length) / 2
    let arrayEvenPoint = evenpoint -1
    if ((input.length) % 3 == 0 && evenpoint % 3 == 0) {
    //  console.log('stopPoint', evenpoint)
        for (let i = 0; i < inputToArray.length; i++) {
            if (i < arrayEvenPoint) {
                leftSymmetry[inputToArray[i]]++
            }
            if (i >= arrayEvenPoint) {
                rightSymmetry[inputToArray[i]]++
            }
        if(leftSymmetry['('] != rightSymmetry[')']
        || leftSymmetry['{'] != rightSymmetry['}']
        || leftSymmetry['['] != rightSymmetry[']']
        ) {
            if((inputToArray[arrayEvenPoint]=='(' && inputToArray[arrayEvenPoint+1] == ')') ||
            (inputToArray[arrayEvenPoint]=='{' && inputToArray[arrayEvenPoint+1] == '}') ||
            (inputToArray[arrayEvenPoint]=='[' && inputToArray[arrayEvenPoint+1] == ']')
            ){
                if((inputToArray[arrayEvenPoint-1]=='(' && inputToArray[arrayEvenPoint] == ')') ||
                (inputToArray[arrayEvenPoint-1]=='{' && inputToArray[arrayEvenPoint] == '}') ||
                (inputToArray[arrayEvenPoint-1]=='[' && inputToArray[arrayEvenPoint] == ']')
                ){
                   return false // 
                } 
            }
            else{ return false}
        }
    }
}


    let previousValue = ''

    // if the last character is an open parenthesis, return false
    if (inputToArray[inputToArray.length-1] == '(' || inputToArray[inputToArray.length-1] == '{' || inputToArray[inputToArray.length-1] == '[') {
        return false
    }
    let lastInput = inputToArray[inputToArray.length-1]

    if((inputToArray[0] == '(' && lastInput != ')') && (inputToArray[1] != ')')) {
        return false
    }
    if((inputToArray[0] == '{' && lastInput != '}') && (inputToArray[1] != '}')) {
        return false
    }
    if((inputToArray[0] == '[' && lastInput != ']') && (inputToArray[1] != ']')) {
        return false
    }

    for (let i = 0; i < inputToArray.length; i++) {
        occurances[inputToArray[i]]++


        if (previousValue == '' && (inputToArray[i] == ')' || inputToArray[i] == ']' || inputToArray[i] == '}')) {
            return false
        }
        if (previousValue == '(' && (inputToArray[i] == '}' || inputToArray[i] == ']')) {
            return false
        }
        if (previousValue == '{' && (inputToArray[i] == ')' || inputToArray[i] == ']')) {
            return false
        }
        if (previousValue == '[' && (inputToArray[i] == '}' || inputToArray[i] == ')')) {
            return false
        }
        // for (let j = i; j < inputToArray.length; j++) {
        //     console.log(j)
        // }
        previousValue = inputToArray[i]
        if (previousValue == '(' && !inputToArray.includes(')')) {
            return false
        }
        if (previousValue == '[' && !inputToArray.includes(']')) {
            return false
        }
        if (previousValue == '{' && !inputToArray.includes('}')) {
            return false
        }
    }
    // let occurances = {
    //     '(': 0,
    //     ')': 0,
    //     '{': 0,
    //     '}': 0,
    //     '[': 0,
    //     ']': 0
    // }
    if (occurances['('] != occurances[')'] || occurances['{'] != occurances['}'] || occurances['['] != occurances[']']) {
        return false
    }
    return true
}

console.log(isValidParentheses(input1), 'true') // true
console.log('next')
console.log(isValidParentheses(input2),'true') // true
console.log('next')

console.log(isValidParentheses(input3),'false') // false
console.log('next')

console.log(isValidParentheses(input4),'false') // false
console.log('next')

console.log(isValidParentheses(input5),'false') // false
console.log('next')

console.log(isValidParentheses(input6),'true') // true
console.log('next')

console.log(isValidParentheses(input7),'false') // false
console.log('next')

console.log(isValidParentheses(input8),'false') // false
console.log('next')

console.log(isValidParentheses(input9),'false') // false
console.log('next')

console.log(isValidParentheses(input10),'false') // false
console.log('next')

console.log(isValidParentheses(input11),'false') // false
console.log('next')

console.log(isValidParentheses(input12),'true') // true

