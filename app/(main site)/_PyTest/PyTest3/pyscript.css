/* src/styles/pyscript_base.css */
py-config {
    display: none;
  }
  py-script:not(:defined) {
    display: none;
  }
  py-repl:not(:defined) {
    display: none;
  }
  py-title:not(:defined) {
    display: none;
  }
  py-inputbox:not(:defined) {
    display: none;
  }
  py-button:not(:defined) {
    display: none;
  }
  py-box:not(:defined) {
    display: none;
  }
  html {
    font-family:
      ui-sans-serif,
      system-ui,
      -apple-system,
      BlinkMacSystemFont,
      "Segoe UI",
      Robot<PERSON>,
      "Helvetica Neue",
      <PERSON><PERSON>,
      "Noto Sans",
      sans-serif,
      "Apple Color Emoji",
      "Segoe UI Emoji",
      "Segoe UI Symbol",
      "Noto Color Emoji";
    line-height: 1.5;
  }
  .spinner::after {
    content: "";
    box-sizing: border-box;
    width: 40px;
    height: 40px;
    position: absolute;
    top: calc(40% - 20px);
    left: calc(50% - 20px);
    border-radius: 50%;
  }
  .spinner.smooth::after {
    border-top: 4px solid rgba(255, 255, 255, 1);
    border-left: 4px solid rgba(255, 255, 255, 1);
    border-right: 4px solid rgba(255, 255, 255, 0);
    animation: spinner 0.6s linear infinite;
  }
  @keyframes spinner {
    to {
      transform: rotate(360deg);
    }
  }
  .label {
    text-align: center;
    width: 100%;
    display: block;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.8rem;
    margin-top: 6rem;
  }
  .py-overlay {
    position: fixed;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.5);
    transition: opacity 500ms;
    visibility: hidden;
    color: visible;
    opacity: 1;
  }
  .py-overlay {
    visibility: visible;
    opacity: 1;
  }
  .py-pop-up {
    text-align: center;
    width: 600px;
  }
  .py-pop-up p {
    margin: 5px;
  }
  .py-pop-up a {
    position: absolute;
    color: white;
    text-decoration: none;
    font-size: 200%;
    top: 3.5%;
    right: 5%;
  }
  .alert-banner {
    position: relative;
    padding: 0.5rem 1.5rem 0.5rem 0.5rem;
    margin: 5px 0;
  }
  .alert-banner p {
    margin: 0;
  }
  .py-error {
    background-color: #ffe9e8;
    border: solid;
    border-color: #f0625f;
    color: #9d041c;
  }
  .py-warning {
    background-color: rgb(255, 244, 229);
    border: solid;
    border-color: #ffa016;
    color: #794700;
  }
  .alert-banner.py-error > #alert-close-button {
    color: #9d041c;
  }
  .alert-banner.py-warning > #alert-close-button {
    color: #794700;
  }
  #alert-close-button {
    position: absolute;
    right: 0.5rem;
    top: 0.5rem;
    cursor: pointer;
    background: transparent;
    border: none;
  }
  .py-box {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
  }
  .py-box div.py-box-child * {
    max-width: 100%;
  }
  .py-repl-box {
    flex-direction: column;
  }
  .py-repl-editor {
    --tw-border-opacity: 1;
    border-color: rgba(209, 213, 219, var(--tw-border-opacity));
    border-width: 1px;
    position: relative;
    --tw-ring-inset: var(--tw-empty, );
    --tw-ring-offset-width: 0px;
    --tw-ring-offset-color: #fff;
    --tw-ring-color: rgba(59, 130, 246, 0.5);
    --tw-ring-offset-shadow: 0 0 #0000;
    --tw-ring-shadow: 0 0 #0000;
    --tw-shadow: 0 0 #0000;
    position: relative;
    box-sizing: border-box;
    border-width: 1px;
    border-style: solid;
    border-color: rgb(209, 213, 219);
  }
  .editor-box:hover button {
    opacity: 1;
  }
  .py-repl-run-button {
    opacity: 0;
    bottom: 0.25rem;
    right: 0.25rem;
    position: absolute;
    padding: 0;
    line-height: inherit;
    color: inherit;
    cursor: pointer;
    background-color: transparent;
    background-image: none;
    -webkit-appearance: button;
    text-transform: none;
    font-family: inherit;
    font-size: 100%;
    margin: 0;
    text-rendering: auto;
    letter-spacing: normal;
    word-spacing: normal;
    line-height: normal;
    text-transform: none;
    text-indent: 0px;
    text-shadow: none;
    display: inline-block;
    text-align: center;
    align-items: flex-start;
    cursor: default;
    box-sizing: border-box;
    background-color: -internal-light-dark(rgb(239, 239, 239), rgb(59, 59, 59));
    margin: 0em;
    padding: 1px 6px;
    border: 0;
  }
  .py-repl-run-button:hover {
    opacity: 1;
  }
  .py-title {
    text-transform: uppercase;
    text-align: center;
  }
  .py-title h1 {
    font-weight: 700;
    font-size: 1.875rem;
  }
  .py-input {
    padding: 0.5rem;
    --tw-border-opacity: 1;
    border-color: rgba(209, 213, 219, var(--tw-border-opacity));
    border-width: 1px;
    border-radius: 0.25rem;
    margin-right: 0.75rem;
    border-style: solid;
    width: auto;
  }
  .py-box input.py-input {
    width: -webkit-fill-available;
  }
  .central-content {
    max-width: 20rem;
    margin-left: auto;
    margin-right: auto;
  }
  input {
    text-rendering: auto;
    color: -internal-light-dark(black, white);
    letter-spacing: normal;
    word-spacing: normal;
    line-height: normal;
    text-transform: none;
    text-indent: 0px;
    text-shadow: none;
    display: inline-block;
    text-align: start;
    appearance: auto;
    -webkit-rtl-ordering: logical;
    background-color: -internal-light-dark(rgb(255, 255, 255), rgb(59, 59, 59));
    margin: 0em;
    padding: 1px 2px;
    border-width: 2px;
    border-style: inset;
    border-color: -internal-light-dark(rgb(118, 118, 118), rgb(133, 133, 133));
    border-image: initial;
  }
  .py-button {
    --tw-text-opacity: 1;
    color: rgba(255, 255, 255, var(--tw-text-opacity));
    padding: 0.5rem;
    --tw-bg-opacity: 1;
    background-color: rgba(37, 99, 235, var(--tw-bg-opacity));
    --tw-border-opacity: 1;
    border-color: rgba(37, 99, 235, var(--tw-border-opacity));
    border-width: 1px;
    border-radius: 0.25rem;
    cursor: pointer;
  }
  .py-li-element p {
    margin: 5px;
  }
  .py-li-element p {
    display: inline;
  }
  button,
  input,
  optgroup,
  select,
  textarea {
    font-family: inherit;
    font-size: 100%;
    line-height: 1.15;
    margin: 0;
  }
  .line-through {
    text-decoration: line-through;
  }
  .py-terminal {
    min-height: 10em;
    background-color: black;
    color: white;
    padding: 0.5rem;
    overflow: auto;
  }
  .py-terminal-hidden {
    display: none;
  }
  html:has(py-terminal[docked]:not(py-terminal[docked].py-terminal-hidden)) {
    padding-bottom: 40vh;
  }
  py-terminal[docked] {
    position: fixed;
    bottom: 0;
    width: 100vw;
    max-height: 40vh;
    overflow: auto;
  }
  py-terminal[docked] .py-terminal {
    margin: 0;
  }
  /*# sourceMappingURL=pyscript.css.map */