@tailwind base;
@tailwind components;
@tailwind utilities;




strong::before {
    content: '\A';
    white-space: pre;
  }
  .mom{
    position:relative;
    z-index: 1;
  }

  .child {
    top:0;
    left:0;
    position: absolute;
    z-index: 2;
  }



  #granim-canvas {
    width: 100%;
    height: 80px;
  }

  .granim-relative {
    position: relative;
    width: 100%;
    height: 100%;
    top:0;
    left: 0;
    z-index: 1;
    
  }
.overlay {
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.9)
}
.Text-Div {
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
 
}

  .destinymom {
    position:relative;
    z-index: 3;    
  }

  .destinychild {
    top:0;
    left:0;
    position: absolute;
    z-index: -2;
    width: 100%;
    height: 100%;
  }

  #canvas-image-blending{
    height: 100%;
    width: 100%; 
    /* width: 93rem; */
  }


 #canvas-image {
    position: absolute;
    display: block;
    width: 100%;
    height: 100%;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
}



