import React from 'react';
import { useState } from 'react';



type loading = "on" | "off" | "Successful" | "error"




/**
 * @typedef {"on" | "off" | "Successful" | "error"} LoadingState - Represents the possible states of the loading process.
 */
type LoadingState = "on" | "off" | "Successful" | "error";

/**
 * Custom hook to manage loading, success, and error states for asynchronous operations.
 *
 * It provides helper components (`LoadingWrapper`, `LoadSuccess`, `LoadError`)
 * to conditionally render UI based on the current loading state. The primary way
 * to trigger the loading sequence is by wrapping the trigger element (e.g., a button)
 * with the `LoadingWrapper` component and providing the asynchronous function
 * to its `callback` prop.
 *
 * @returns {Array} An array containing:
 *   - `setLoading`: {function(LoadingState): void} - A function to manually set the loading state.
 *       While available, direct use is less common than using `LoadingWrapper`.
 *   - `LoadingWrapper`: {React.ComponentType<{children: React.ReactNode, callback: () => Promise<void>}>} -
 *       A component wrapper. It renders its children normally when the state is "off".
 *       When clicked, it executes the provided `callback` function, sets the state to "on"
 *       (displaying a loading spinner), and then sets the state to "off" on successful
 *       completion or "error" if the callback throws an error. On error, it displays
 *       an error message along with the original children (to allow retrying).
 *   - `LoadSuccess`: {React.ComponentType<{children: React.ReactNode}>} - A component that
 *       renders its children only when the loading state is "Successful".
 *       *Note: The current implementation of the hook's internal `executeLoad` function
 *       does not set the state to "Successful". This component might be for future use
 *       or requires manual state setting via `setLoading`.*
 *   - `LoadError`: {React.ComponentType<{children: React.ReactNode}>} - A component that
 *       renders its children only when the loading state is "error". Can be used to
 *       display specific error UI elements elsewhere on the page.
 *
 * @example
 * // Inside your React component
 * import React from 'react';
 * import useLoading from './useLoading'; // Adjust path as needed
 *
 * function MyComponent() {
 *   const [setLoading, LoadingWrapper, LoadSuccess, LoadError] = useLoading();
 *   const [data, setData] = React.useState(null);
 *
 *   // An example asynchronous function to simulate fetching data
 *   const fetchData = async () => {
 *     console.log("Fetching started...");
 *     // Simulate network delay
 *     await new Promise(resolve => setTimeout(resolve, 1500));
 *
 *     // Simulate potential error
 *     if (Math.random() > 0.7) {
 *       console.error("Simulated fetch error!");
 *       throw new Error("Failed to fetch data. Please try again.");
 *     }
 *
 *     // Simulate successful fetch
 *     const result = { message: "Data fetched successfully!" };
 *     setData(result);
 *     console.log("Fetching successful!");
 *     // Note: To use LoadSuccess, you would manually call setLoading("Successful") here
 *     // setLoading("Successful");
 *   };
 *
 *   return (
 *     <div>
 *       <h1>Data Fetcher</h1>
 *
 *       <LoadingWrapper callback={fetchData}>
 *         <button className="btn btn-primary">
 *           Fetch Data
 *         </button>
 *       </LoadingWrapper>
 *
 *       { // Optional: Show specific content on success (if state is manually set)
 *         <LoadSuccess>
 *           <div className="alert alert-success mt-4">
 *             Operation was successful!
 *           </div>
 *         </LoadSuccess>
 *       }
 *
 *       { // Optional: Show specific content on error elsewhere
 *         <LoadError>
 *            <div className="alert alert-warning mt-4">
 *              An error occurred during the last attempt. Check console/try again.
 *            </div>
 *         </LoadError>
 *       }
 *
 *       {data && (
 *         <div className="mt-4 p-4 border rounded bg-gray-100">
 *           <h2>Received Data:</h2>
 *           <pre>{JSON.stringify(data, null, 2)}</pre>
 *         </div>
 *       )}
 *     </div>
 *   );
 * }
 *
 * export default MyComponent;
 */

export default function useLoading(): [any,any,any,any] {
    // const isLoading = useStore((state) => state.isLoading)
    const [isLoading, setLoading] = useState<loading>("off")
    // const setLoading = useStore((state) => state.setLoading)

    const [errorMessage, setErrorMessage] = useState<string>("")

    async function executeLoad(callback: () => Promise<void>) {
        setLoading("on")
        try{
        await callback()
        }
        catch (error) {
            console.log(error)
            setLoading("error")
            setErrorMessage(error.message)
            return
        }
        setLoading("off")
    }

    function LoadingWrapper({ children, callback }: { children: React.ReactNode, callback: () => Promise<void> }) {
        if (isLoading == "off") {
            return (
                <div onClick ={() => {executeLoad(callback)}} className=''>
                    {children}
                </div>
            )
        }
        if (isLoading == "on") {
            return (
                <>
                    <span className="loading loading-lg loading-spinner text-success"></span>

                </>
            )
        }
        if (isLoading == "error") {
            return (
                <>
                    <div onClick ={() => {executeLoad(callback)}} className='flex flex-col items-center justify-center'>
                        <p className='text-red-500'>{errorMessage}</p>
                        <div><p className="text-center">Something went wrong</p></div>
                        {children}
                    </div>
                </>
            )
        }
        else {
            return (
                <>
                </>
            )
        }
    }



    function LoadSuccess({ children }: { children: React.ReactNode }) {
        if (isLoading == "Successful") {
            return (
                <>
                    {children}

                </>
            )
        }
        else {
            return (
                <>
                </>
            )
        }
    }

    function LoadError({ children }: { children: React.ReactNode }) {
   
        if (isLoading == "error") {
            return (
                <>
                    {children}

                </>
            )
        }
        else {
            return (
                <>
                </>
            )
        }
    }

    return [setLoading, LoadingWrapper, LoadSuccess, LoadError];
}