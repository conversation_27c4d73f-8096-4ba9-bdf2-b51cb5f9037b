import React from 'react'
import { useReducer } from 'react';
import Chat from './Chat.js';
import ContactList from './ContactList.js';
import { initialState, messengerReducer } from './messengerReducer';


export default function example() {
    const [state, dispatch] = useReducer(messengerReducer, initialState);
    const message = state.messages[state.selectedId];
    const contact = contacts.find((c) => c.id === state.selectedId);

    return (
        <div className='bg-white  max-w-7xl mx-auto prose-2xl '>
            <h1>Statemachine Example</h1>
            <p>example</p>
            <div>
                <ContactList
                    contacts={contacts}
                    selectedId={state.selectedId}
                    dispatch={dispatch}
                />
                <Chat
                    key={contact!.id}
                    message={message}
                    contact={contact}
                    dispatch={dispatch}
                />
            </div>
        </div>

    )
}

const contacts = [
    { id: 0, name: '<PERSON>', email: '<EMAIL>' },
    { id: 1, name: '<PERSON>', email: '<EMAIL>' },
    { id: 2, name: '<PERSON>', email: '<EMAIL>' },
];