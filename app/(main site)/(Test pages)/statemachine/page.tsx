
'use client'
import React from 'react'
import TestProgram from './example'
import ZustandReducer from './zustand/ZustandReducer'

// Simple implementation of create function
function create(stateCreator) {
  let state;
  
  const set = (updater) => {
    console.log("set called with:", updater); 
    
    if (typeof updater === 'function') {
      // Handle function updates like: (state) => ({ bears: state.bears + 1 })
      const newPartialState = updater(state);
      state = { ...state, ...newPartialState };
      console.log(state)
    } else {
      // Handle object updates like: { bears: 0 }
      state = { ...state, ...updater };
    } 
    
    console.log("New state:", state);


  };

    state = stateCreator(set);

  
  // Initialize state by calling the creator function
  // const initialState = stateCreator(set);
  // state = initialState;  
  
  // Return the state object (with methods)
   //return state;
      return {...state, get bears() { return state.bears; }} 
  // Return an object that dynamically accesses current state

  // return {
  //   get bears() { return state.bears; }, // Dynamic getter
  //   increasePopulation: initialState.increasePopulation,
  //   removeAllBears: initialState.removeAllBears,
  //   updateBears: initialState.updateBears,
  // };
}

// Your exact pattern
const useStore = create((set) => ({
  bears: 0,
  increasePopulation: () => set((state) => ({ bears: state.bears + 1 })),
  removeAllBears: () => set({ bears: 0 }),
  updateBears: (newBears) => set({ bears: newBears }),
}));

// Now you can use it exactly like Zustand:
console.log("Initial:", useStore.bears); // 0

useStore.increasePopulation(); // Logs the function call and new state
console.log("After increase:", useStore.bears); // 1

useStore.increasePopulation(); // Logs the function call and new state
console.log("After increase:", useStore.bears); // 2 

useStore.updateBears(5); // Logs the object update and new state  
console.log("After update:", useStore.bears); // 5

useStore.removeAllBears(); // Logs the object update and new state
console.log("After remove:", useStore.bears); // 0  




function page() {


  return (
    <div className='bg-white mx-auto max-w-screen-2xl'>
    <TestProgram/> 
    <ZustandReducer/>
    </div>
  )
}

export default page