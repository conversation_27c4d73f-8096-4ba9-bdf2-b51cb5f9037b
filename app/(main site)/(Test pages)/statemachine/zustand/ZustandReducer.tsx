
// --- Usage in a React Component ---
import React from 'react';
import { useBoundStore } from './yourStoreFile'; // Assuming the above code is in 'yourStoreFile.js'

function MyComponent() {
  const { count, increment, decrement, reset, isAuthenticated, user, login, logout } = useBoundStore();

  return (
    <div>
      <h2>Counter</h2>
      <p>Count: {count}</p>
      <button className='btn' onClick={increment}>Increment</button>
      <button className='btn' onClick={decrement}>Decrement</button>
      <button className='btn' onClick={reset}>Reset</button>

      <h2>Authentication</h2>
      {isAuthenticated ? (
        <>
          <p>Welcome, {user.username}!</p>
          <button className='btn' onClick={logout}>Log Out</button>
        </>
      ) : (
        <>
          <p>Please log in.</p>
          <button className='btn' onClick={() => login({ username: '<PERSON>ustand<PERSON><PERSON>' })}>Log In</button>
        </>
      )}
    </div>
  );
}

export default MyComponent;