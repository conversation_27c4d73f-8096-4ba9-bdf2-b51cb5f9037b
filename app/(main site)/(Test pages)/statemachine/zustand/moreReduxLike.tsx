import { create } from 'zustand';

// For most Zustand use cases, the first approach (simple "Reducer-like" logic within actions) is preferred. It's more concise, idiomatic for Zustand, and often sufficient for organizing your state logic. Zustand's set function naturally lends itself to this pattern.

// The second approach (explicit "Reducer" function) is useful if:

// You are migrating from Redux and want to reuse existing reducer logic.

// You have very complex state transitions that benefit from the strict purity and testability of separate reducer functions.

// You simply prefer the mental model of "dispatching actions to reducers."


// --- Pure Reducer Functions ---
const counterReducer = (state, action) => {
  switch (action.type) {
    case 'INCREMENT':
      return { count: state.count + 1 };
    case 'DECREMENT':
      return { count: state.count - 1 };
    case 'RESET':
      return { count: 0 };
    default:
      return state;
  }
};

const authReducer = (state, action) => {
  switch (action.type) {
    case 'LOGIN':
      return { isAuthenticated: true, user: action.payload };
    case 'LOGOUT':
      return { isAuthenticated: false, user: null };
    default:
      return state;
  }
};

// --- Zustand Store using these reducers ---
export const useStoreWithReducers = create((set, get) => ({
  // Initial State for slices
  counter: { count: 0 },
  auth: { isAuthenticated: false, user: null },

  // Actions that use the reducer functions
  dispatchCounter: (action) =>
    set((state) => ({
      counter: counterReducer(state.counter, action),
    })),

  dispatchAuth: (action) =>
    set((state) => ({
      auth: authReducer(state.auth, action),
    })),
}));

// --- Usage in a React Component ---
import React from 'react';
import { useStoreWithReducers } from './yourStoreFile';

function MyComponentWithReducers() {
  const { counter, auth, dispatchCounter, dispatchAuth } = useStoreWithReducers();

  return (
    <div>
      <h2>Counter (with explicit reducer)</h2>
      <p>Count: {counter.count}</p>
      <button onClick={() => dispatchCounter({ type: 'INCREMENT' })}>Increment</button>
      <button onClick={() => dispatchCounter({ type: 'DECREMENT' })}>Decrement</button>
      <button onClick={() => dispatchCounter({ type: 'RESET' })}>Reset</button>

      <h2>Authentication (with explicit reducer)</h2>
      {auth.isAuthenticated ? (
        <>
          <p>Welcome, {auth.user.username}!</p>
          <button onClick={() => dispatchAuth({ type: 'LOGOUT' })}>Log Out</button>
        </>
      ) : (
        <>
          <p>Please log in.</p>
          <button onClick={() => dispatchAuth({ type: 'LOGIN', payload: { username: 'ReduxLover' } })}>Log In</button>
        </>
      )}
    </div>
  );
}

export default MyComponentWithReducers;