import { create } from 'zustand';
// This is the most common and idiomatic way to handle state updates in Zustand. The set function is your "dispatch" and the logic inside the function you pass to set is your "reducer."
//https://medium.com/better-programming/simplify-your-react-components-state-with-a-state-machine-8e9c9a4ee1f6
// Zustand allows global access to the store, which means multiple components could share the same button state unless scoped.

// If you want per-instance state like useReducer, you'd need to scope the store to the component instance (e.g. using createStore() instead of a singleton with create()).




// Think of this as your "reducer" logic for the counter
const createCounterSlice = (set) => ({
  count: 0,
  increment: () => set((state) => ({ count: state.count + 1 })),
  decrement: () => set((state) => ({ count: state.count - 1 })),
  reset: () => set({ count: 0 }),
});

// Think of this as your "reducer" logic for the authentication
const createAuthSlice = (set) => ({
  isAuthenticated: false,
  user: null,
  login: (userData) => set({ isAuthenticated: true, user: userData }),
  logout: () => set({ isAuthenticated: false, user: null }),
});

// Combine slices (optional, but good for larger stores)
export const useBoundStore = create((...a) => ({
  ...createCounterSlice(...a),
  ...createAuthSlice(...a),
}));

