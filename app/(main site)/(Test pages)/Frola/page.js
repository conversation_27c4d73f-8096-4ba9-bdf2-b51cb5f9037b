'use client'
//uninstall these dependencies when done 
// npm uninstall font-awesom
//npm uninstall react-froala-wysiwyg

// import 'froala-editor/css/froala_style.min.css';
// import 'froala-editor/css/froala_editor.pkgd.min.css';
            
// import FroalaEditorComponent from 'react-froala-wysiwyg';

export default function FroalaEditor() {
    return(
    <div>
    {/* <FroalaEditorComponent tag='textarea'/> */}
    </div>)
}