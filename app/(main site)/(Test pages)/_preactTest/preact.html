<body>
<script type="module">
    import { h, render } from 'https://esm.sh/preact';
    import { useState } from 'https://esm.sh/preact/hooks';
    import htm from 'https://esm.sh/htm';  
    import { signal } from 'https://esm.sh/@preact/signals';

  
    // Initialize htm with Preact
    const html = htm.bind(h);
  
    function App(props) {
      const [count, setCount] = useState(0);
  
      return html`
        <div>
          <h1>Hello ${props.name}!</h1>
          <p>Count: ${count}</p>
          <button onClick=${() => setCount(count + 1)}>Increment</button>
          <button onClick=${() => setCount(count - 1)}>Decrement</button>
          <p>Start editing to see some magic happen :)</p>
        </div>
      `;
    }

//     const count = signal(0);
  
//   function App(props) {
//     return html`
//       <div>
//         <h1>Hello ${props.name}!</h1>
//         <p>Count: ${count.value}</p> <!-- Access signal's value explicitly -->
//         <button onClick=${() => count.value++}>Increment</button>
//         <button onClick=${() => count.value--}>Decrement</button>
//       </div>
//     `;
//   }

  // Manually rerender when the signal changes
//   count.subscribe(() => render(html`<${App} name="World" />`, document.body));

    render(html`<${App} name="World" />`, document.body);
  </script>
  </body>