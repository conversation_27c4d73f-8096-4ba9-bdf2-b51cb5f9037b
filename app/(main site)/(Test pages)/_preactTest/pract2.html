<!DOCTYPE html>
<html>
  <head>
    <script type="importmap">
        {
          "imports": {
            "preact": "https://esm.sh/preact@10.23.1",
            "preact/": "https://esm.sh/preact@10.23.1/",
            "@preact/signals": "https://esm.sh/@preact/signals@1.3.0?external=preact",
            "htm/preact": "https://esm.sh/htm@3.1.1/preact?external=preact"
          }
        }
      </script>
  </head>
  <body>
    <div id="app"></div>

    <script type="module">

      import { render } from 'preact';
      import { html } from 'htm/preact';
      import { signal } from "@preact/signals";


      const count = signal(0);

      export function App() {

        const value = count.value;

  const increment = () => {
    // A signal is updated by assigning to the `.value` property:
    count.value++;
  }
        return html`
         <p>Count: ${value}</p>
          <button onClick=${increment}>click me</button>
          <h1>Hello, World!</h1>
        `;
      }

      render(html`<${App} />`, document.getElementById('app'));
    </script>
  </body>
</html>