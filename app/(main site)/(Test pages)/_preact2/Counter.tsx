// "use client";

// import { signal } from "@preact-signals/safe-react";

// const count = signal(0);

// export const Counter = () => {
//   return (
//     <div className="flex flex-col items-center mb-20">
//       <h1>Signals counter</h1>
//       <p>Current count: {count.value}</p>
//       <button className="btn" onClick={() => count.value++}>
//         Increment
//       </button>
//     </div>
//   );
// };
