import * as React from 'react';
import Box from '@mui/material/Box';
import Button from '@mui/material/Button';
import Typography from '@mui/material/Typography';
import Modal from '@mui/material/Modal';

const style = {
  position: 'absolute',
  top: '50%',
  left: '50%',
  transform: 'translate(-50%, -50%)',
  width: 400,
  bgcolor: 'background.paper',
  border: '2px solid #000',
  boxShadow: 24,
  p: 4,
};

export default function BasicModal() {
  const [open, setOpen] = React.useState(false);
  const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);

  return (
    <span>
      <Button onClick={handleOpen}><b className="text-blue-600">JamStack Site</b></Button>
      <Modal
        open={open}
        onClose={handleClose}
        aria-labelledby="modal-modal-title"
        aria-describedby="modal-modal-description"
      >
        <Box sx={style}>
          <Typography id="modal-modal-title" variant="h6" component="h2">
            JamStack
          </Typography>
          <Typography id="modal-modal-description" sx={{ mt: 2 }}>
          <p >I'm subscribed to the <a className="text-red-500" href = "https://jamstack.org/headless-cms/">JamStack</a> philosophy of separating back-end conserns from the front-end, so content on this site was implemented with <a href = "https://strapi.io/">Strapi Headless CMS.</a> User storage is handled with PostgreSQL and Prisma  </p>
          </Typography>
        </Box>
      </Modal>
    </span>
  );
}