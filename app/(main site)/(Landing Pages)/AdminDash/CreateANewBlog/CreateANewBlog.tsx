'use client';


import MainContentTemplate from '@/app/(main site)/Components/ui/MainContentTemplate';
import Link from 'next/link';
import useStore from "./ZustandAdmin";
import { EditMarkdown } from '../EditMarkdown';
import { addMongoDBblog2 } from '@/public/utils/MongoDBfunctions';
import useUserContentSettings from '@/app/(main site)/(Landing Pages)/AdminDash/MongoDbTester/useUserContentSettings';
import useAdvancedTextInput3 from '@/app/hooks/ui/useAdvancedTextInput3';

import { usePathname } from 'next/navigation';
import { useRouter } from 'next/navigation';
import useLoading from '@/app/(main site)/Components/ui/Loading';
import { getUserID } from "@/app/services/userServices"


export default function CreateANewBlog() {

    const isCreateBlogActive = useStore((state) => state.isCreateBlogActive)
    const setBlogActive = useStore((state) => state.setCreateBlogActive)
    const blogContent = useStore((state) => state.blogContent)
    const setBlogContent = useStore((state) => state.setblogContent)
    // const isLoading = useStore((state) => state.isLoading)
    // const isSubmitted = useStore((state) => state.isSubmitted)
    // const setSubmitted = useStore((state) => state.setSubmitted)
    const [title, TitleText] = useAdvancedTextInput3({ prompt: "Enter A Title" })
    const [CategorySelectELM, ContentSelectELM, selectedCategory, selectedContent] = useUserContentSettings()
    const isCreateNewBlogPage = usePathname()?.includes('CreateANewBlog');

    const [setLoading, LoadingWrapper, LoadSuccess, LoadError] = useLoading()
    // const setLoading = useStore((state) => state.setLoading)
    const router = useRouter()


    async function handleAddToMongo(title: string, content: string, category: string | number, contentType: string | number) {
        try {
            setLoading("on");
            const userID = await getUserID();

            if (userID) { // Only proceed if userID exists
                if (category === 'Select A Category' || contentType === 'Select A Content Type') return setLoading("error");
                const isAddedSuccessfully = await addMongoDBblog2(title, content, userID, contentType, category);
                setLoading(isAddedSuccessfully ? "Successful" : "error");
            } else {
                console.warn("User ID not found");
                setLoading("error"); // Handle the case where userID doesn't exist
            }
        } catch (error) {
            console.log(error)
            console.error("Error adding to MongoDB:", error);
            setLoading("error");
        }
    }

    if (isCreateNewBlogPage && isCreateBlogActive) {

        return (
            <>
                <div className=" bg-white mx-auto container max-w-[1480px]  flex flex-col justify-center items-center gap-2">
                    <div className='min-w-full'>
                        <EditMarkdown Content={blogContent} onChange={setBlogContent} />

                    </div>
                    <TitleText />
                    <CategorySelectELM />
                    <ContentSelectELM />
                    <LoadingWrapper>
                        <button className='btn bg-green-700 text-white w-full max-w-xs' onClick={() => handleAddToMongo(title(), blogContent, selectedCategory, selectedContent)}>Save Content</button>
                    </LoadingWrapper>
                    <LoadSuccess>
                        <Link href={'/AdminDash'}>
                            <button className='btn bg-blue-700 text-white w-full max-w-xs' onClick={() => setLoading("off")}>Return To Admin Dash</button></Link>
                        <p className="text-green-600">Content Successfully Uploaded</p>
                    </LoadSuccess>
                    <LoadError>
                        <button className='btn bg-red-700 text-white w-full max-w-xs' onClick={() => handleAddToMongo(title(), blogContent, selectedCategory, selectedContent)}>Problem With Server, Contact Support Or Click To Try Again.</button>
                    </LoadError>


                </div >
            </>
        )

    }

    return (
        <MainContentTemplate title={"Upload New Content"}>
            <>
                <div className='flex flex-col justify-center items-center'>
                    <div className='flex'>
                        <Link href={'/AdminDash/CreateANewBlog'}><button
                            onClick={() => setBlogActive(true)}
                            className="btn">Upload/Create</button></Link>
                    </div>
                </div>
            </>
        </MainContentTemplate>

    )
}
