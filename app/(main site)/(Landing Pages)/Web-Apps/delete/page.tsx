'use client'

import Container from '@mui/material/Container';
import AIChatBox from "@/app/components/ai-chat/AIChatBox"; 
import { startConversationAtom } from "@/app/components/ai-chat/AIChatBox";
import { useAtom } from 'jotai';
import { useEffect } from 'react';

export default function GirlxAi() { 
    const [startConversation, setStartConversation] = useAtom(startConversationAtom);
    
    useEffect(() => {
        setStartConversation(["Ask AI about your recording..."]);
    }, []);

    return (
        <div >
            <Container maxWidth="xl"  >
                <div className="flex flex-col gap-4 overflow-x-hidden">
                    <div>
                        <AIChatBox
                            aiPrompt='Ask AI Questions about your recording...'
                            theme = "dark"
                            disableChatsRemaining = {true}
                            disableUserTracking = {true}
                            setting_CloseButton={true}
                            hide_settings_button={true}
                            hide_save_button={true}
                            AI_Select_Setting={false}
                            AI_Bot_Setting="openai/gpt-oss-120b"
                            show_user_text={true}
                            purchaseLink="/Web-Apps/girlfriend-ai-chat-new"
                            disableCreditBuying={false}
                            submitButtonText="Submit"
                            buy_more_AI_credit_Message="We are In Beta. Wait 24 hours to get 12 more Credits!"
                        />   
                    </div>
                </div>
            </Container>
        </div>
    );
}
