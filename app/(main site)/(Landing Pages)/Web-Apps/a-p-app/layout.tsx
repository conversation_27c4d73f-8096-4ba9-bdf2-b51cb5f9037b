'use server'

import { headers } from "next/headers";
import {getOneContent} from '@/app/(main site)/Components/db_services/mongo'

export async function generateMetadata() {  

    // const header = await headers()
    // const xurl = header.get('x-url');

    // console.log(xurl)
    // const path = xurl!.split('/').pop()

    // const data = await getOneContent(path,"meta")
     
    return {
      // title: data.Title,
      // description: data.Description
      title: "A&P Exam Prep (Airframe and Powerplant)",
      description: "Are you an aspiring aircraft mechanic aiming to conquer the FAA Airframe and Powerplant exams? Look no further! Our A&P Exam Prep App is a comprehensive, feature-rich study tool meticulously engineered to guide you from student to certified professional."
    }
  }

  export default async function BlogLayout({ children }: { children: React.ReactNode }) {
    return (
      <>
        {children}
      </>
    )
  }