import Container from '@mui/material/Container';
import Hero from '@/app/(main site)/Components/Hero';
import Link from 'next/link';

import { Suspense } from "react";
import Collasible from "@/app/(main site)/Components/ui/collapsible";
import { findByBlogUrlAndType } from "@/app/(main site)/Components/db_services/mongo";
import BlogDisplayPage from "@/app/(main site)/A&PBlogs/page"


export default async function APAppLandingPage() {

  // let dropdownData = [];
  // try {
  //   dropdownData = await findByBlogUrlAndType("a-p-app", "dropdown");
  //   console.log(dropdownData);
  // } catch (error) {
  //   console.error("Failed to fetch dropdown data:", error);
  //   // dropdownData is already initialized to [], so Collasible will receive an empty array
  // }

  const androidAppLink = "https://play.google.com/store/apps/details?id=com.faamalc.twa&hl=en_US";

  const webAppLink = process.env.NODE_ENV === 'development'
    ? 'http://faa-app.localhost:3000/'
    : 'https://faa-app.malcmind.com/';

  return (
    <div>
      <Container maxWidth="xl">
        <Hero contentNeeded={"a-p-app"} disableStyling={true} displayTitle={"A&P Exam Prep App"}>
          {/* The Hero component now wraps the description and buttons */}
          <div className="flex flex-col sm:flex-row gap-4 mt-6 justify-center items-center">
            <Link href={androidAppLink} target="_blank" >
              <button className="btn btn-primary w-64">
                Download for Android
              </button>
            </Link>
            <Link href={webAppLink} target="_blank" >
              <button className="btn btn-secondary w-64">
                Open Web Version
              </button>
            </Link>
          </div>
        </Hero>
      </Container>
      <BlogDisplayPage searchParams={{}} />
      {/* <Container maxWidth="xl">
        <Suspense fallback={<div className="text-black">Loading...</div>}>
          <div className="my-2 min-h-screen">
            <Collasible dropdownData={dropdownData} />
          </div>
        </Suspense>
      </Container> */}
    </div>
  );
}
