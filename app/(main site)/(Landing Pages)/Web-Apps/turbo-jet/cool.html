<!DOCTYPE html>
<html lang="en">
<head>
<meta charset="UTF-8">
<title>Turbo‑Jet Engine – Pilot Instruments</title>
<link rel="stylesheet" href="style.css">
</head>
<body>

<div id="controlPanel">
  <h2>Turbo‑Jet Controls</h2>

  <!-- APU Controls Section -->
  <div class="apu-section">
    <h3>APU (Auxiliary Power Unit)</h3>
    <button id="btnAPUStart">▶️ Start APU</button>
    <div class="apu-status">
      Status: <span id="apuStatus">OFF</span>
    </div>
  </div>

  <!-- Main Engine Controls -->
  <div class="apu-section">
    <h3>Main Engine</h3>
    <button id="btnRun">▶️ Start Engine</button>
    <div class="apu-status">
      Status: <span id="engineStatus">OFF</span>
    </div>
    <div class="apu-status">
      <small>⚠️ Requires APU electrical power & bleed air</small>
    </div>
  </div>

  <!-- Fuel System Controls -->
  <div class="fuel-section">
    <h3>Fuel System</h3>
    <button id="btnFuelPump">⛽ Fuel Pump ON</button>
    <button id="btnFuelValve">밸브 Fuel Valve OPEN</button>
    <div class="fuel-status">
      Fuel Quantity: <span id="fuelQuantity">100</span>%
    </div>
  </div>

  <button id="btnSnap">📸 Snapshot</button>

  <label>Throttle <span id="thrVal">0.90</span>
    <input id="throttle" type="range" min="0.2" max="1.5" step="0.01" value="0.9">
  </label>

  <label>Engine size <span id="sizeVal">1.00</span>
    <input id="scale" type="range" min="0.6" max="1.5" step="0.01" value="1">
  </label>

  <label>Ambient pressure (kPa) <span id="presVal">101.3</span>
    <input id="pressure" type="range" min="80" max="130" step="0.1" value="101.3">
  </label>

  <label>Ambient temperature (K) <span id="tempVal">288</span>
    <input id="ambientTemp" type="range" min="250" max="330" step="1" value="288">
  </label>

  <label><input type="checkbox" id="showHPComp" checked> High‑Pressure Compressor</label>
  <label><input type="checkbox" id="showLPComp" checked> Low‑Pressure Compressor</label>
  <label><input type="checkbox" id="showHPTur"  checked> High‑Pressure Turbine</label>
  <label><input type="checkbox" id="showLPTur"  checked> Low‑Pressure Turbine</label>
  <label><input type="checkbox" id="showAfterburner"> After‑Burner (extra turbine)</label>
  <label><input type="checkbox" id="xrayMode"> X‑Ray view</label>
  <label><input type="checkbox" id="muteSound"> Mute sound</label>

  <button id="btnReset">↺ Reset Particles</button>

  <!-- Primary read‑out (already in your original) -->
  <div class="readout" id="readout">
    Mass‑Flow: <span id="mf">–</span> kg/s<br>
    Temp: <span id="temp">–</span> K<br>
    Thrust: <span id="thrust">–</span> N<br>
    EGT: <span id="egt">–</span> °C<br>
    ITT: <span id="itt">–</span> °C<br>
    N1: <span id="n1">–</span> %<br>
    N2: <span id="n2">–</span> %<br>
    Fuel‑Flow: <span id="ff">–</span> lb/h<br>
    Oil‑Pressure: <span id="op">–</span> psi
  </div>

  <!-- *** NEW – pilot‑style read‑out *** -->
  <div class="readout" id="readout2">
    EGT: <span id="egt">–</span> °C<br>
    ITT: <span id="itt">–</span> °C<br>
    N1: <span id="n1">–</span> %<br>
    N2: <span id="n2">–</span> %<br>
    Fuel‑Flow: <span id="ff">–</span> lb/h<br>
    Oil‑Pressure: <span id="op">–</span> psi
  </div>
</div>

<div id="fps">FPS: –</div>
<canvas id="jetCanvas"></canvas>
<canvas id="graph"></canvas>
<script src="script.js"></script>

</body>
</html>
