/* ==============================================================
   1️⃣ GLOBAL SETTINGS & STATE
   ============================================================== */
const canvas = document.getElementById('jetCanvas');
const ctx = canvas.getContext('2d');
const graphCanvas = document.getElementById('graph');
const gCtx = graphCanvas.getContext('2d');

let W, H, G;               // width, height, graph height
function resize(){
  canvas.width = window.innerWidth;
  canvas.height = window.innerHeight;
  graphCanvas.width = window.innerWidth;
  graphCanvas.height = 120;
  W = canvas.width; H = canvas.height; G = graphCanvas.height;
}
window.addEventListener('resize', resize);
resize();

/* UI state --------------------------------------------------- */
let running = false;
let engineStarting = false;
let engineStartTimer = 0;
let throttle = 0.9;
let scale = 1.0;
let ambientPressure = 101.3;   // kPa (display only)
let ambientTemp = 288;         // K (display only)

let fuelPumpOn = false;
let fuelValveOpen = false;
let fuelQuantity = 100; // percentage

let config = {
  hpComp:true, lpComp:true,
  hpTur:true,  lpTur:true,
  afterburner:false,
  xray:false,
  mute:false
};

document.getElementById('btnFuelPump').onclick = () => {
  fuelPumpOn = !fuelPumpOn;
  document.getElementById('btnFuelPump').textContent = fuelPumpOn ? '⛽ Fuel Pump OFF' : '⛽ Fuel Pump ON';
};

document.getElementById('btnFuelValve').onclick = () => {
  fuelValveOpen = !fuelValveOpen;
  document.getElementById('btnFuelValve').textContent = fuelValveOpen ? '밸브 Fuel Valve CLOSE' : '밸브 Fuel Valve OPEN';
};

document.getElementById('btnRun').onclick = () => {
  // If engine is running, stop it
  if (running) {
    running = false;
    engineStarting = false;
    engineStartTimer = 0;
    document.getElementById('btnRun').textContent = '▶️ Start Engine';
    document.getElementById('engineStatus').textContent = 'OFF';
    engineAudio.pause();
    return;
  }

  // Check if APU is available for engine start
  if (!apu.electrical) {
    alert('APU must be running to provide electrical power for engine start!');
    return;
  }

  if (!apu.bleedAir) {
    alert('APU bleed air is required for engine start!');
    return;
  }

  if (!fuelPumpOn) {
    alert('Fuel pump must be on to start the engine!');
    return;
  }

  if (!fuelValveOpen) {
    alert('Fuel valve must be open to start the engine!');
    return;
  }

  // Start engine startup sequence
  if (!engineStarting) {
    startEngineSequence();
  }
};
document.getElementById('btnReset').onclick = () => {
  for(const key in particles) particles[key] = [];
};
document.getElementById('btnSnap').onclick = () => {
  const link = document.createElement('a');
  link.download = `turbo-jet-${Date.now()}.png`;
  link.href = canvas.toDataURL();
  link.click();
};
document.getElementById('throttle').oninput = e => {
  throttle = +e.target.value;
  document.getElementById('thrVal').textContent = throttle.toFixed(2);
};
document.getElementById('scale').oninput = e => {
  scale = +e.target.value;
  document.getElementById('sizeVal').textContent = scale.toFixed(2);
};
document.getElementById('pressure').oninput = e => {
  ambientPressure = +e.target.value;
  document.getElementById('presVal').textContent = ambientPressure.toFixed(1);
};
document.getElementById('ambientTemp').oninput = e => {
  ambientTemp = +e.target.value;
  document.getElementById('tempVal').textContent = ambientTemp;
};

/* ---------- CHECK‑BOX → config mapping ---------- */
const checkboxMap = {
  showHPComp:      'hpComp',
  showLPComp:      'lpComp',
  showHPTur:       'hpTur',
  showLPTur:       'lpTur',
  showAfterburner: 'afterburner',
  xrayMode:        'xray',
  muteSound:       'mute'
};

Object.keys(checkboxMap).forEach(id => {
  const cb = document.getElementById(id);
  cb.checked = config[checkboxMap[id]];
  cb.onchange = e => {
    const prop = checkboxMap[id];
    config[prop] = e.target.checked;
    
    // Handle mute immediately when toggled
    if (prop === 'mute') {
      updateEngineSound();
    }
  };
});

/* ==============================================================
   2️⃣ NOISE (simple perlin for turbulence)
   ============================================================== */
function perlin(x){
  return Math.sin(x*12.9898 + 78.233) * 43758.5453 % 1;
}

/* ==============================================================
   3️⃣ GEOMETRY – scaled percentages
   ============================================================== */
function geometry(){
  const cx = W*0.5, cy = H*0.5;
  const baseLen = Math.min(W,H)*0.65;
  const len = baseLen*scale;                // engine length changes with scale
  const rad = len*0.11;                     // half‑height of duct

  const x = f => cx + (f-0.5)*len;          // map 0‑1 → screen‑coords

  return {
    cx, cy, len, rad,
    inletX:        x(0.10),
    lpCompInX:     x(0.20),
    lpCompOutX:    x(0.30),
    hpCompInX:     x(0.30),
    hpCompOutX:    x(0.40),
    bypassX:       x(0.40),
    combustorX1:   x(0.40),
    combustorX2:   x(0.60),
    hpTurX1:       x(0.60),
    hpTurX2:       x(0.68),
    lpTurX1:       x(0.68),
    lpTurX2:       x(0.78),
    afterburnerX1: x(0.78),
    afterburnerX2: x(0.84),
    nozzleX1:      x(0.84),
    outletX:       x(0.95)
  };
}

/* ==============================================================
   4️⃣ PARTICLE CLASS
   ============================================================== */
class Particle{
  constructor(x,y,vx,vy,life,size,color,spin=0){
    this.x=x; this.y=y; this.vx=vx; this.vy=vy;
    this.life=life; this.maxLife=life;
    this.size=size; this.baseColor=color; this.spin=spin;
    this.angle=0;
  }
  update(dt){
    const t = performance.now()*0.001;
    const turb = perlin(this.x*0.02 + t)*15;
    this.vx += turb*dt;
    this.vy += (perlin(this.y*0.02 + t)-0.5)*5*dt;

    this.x+=this.vx*dt;
    this.y+=this.vy*dt;
    this.life-=dt;
    this.angle+=this.spin*dt;
  }
  draw(ctx){
    const alpha = Math.max(this.life/this.maxLife,0);
    ctx.save();
    ctx.translate(this.x,this.y);
    ctx.rotate(this.angle);
    ctx.fillStyle = this.baseColor.replace('ALPHA',alpha);
    ctx.beginPath();
    ctx.arc(0,0,this.size,0,Math.PI*2);
    ctx.fill();
    ctx.restore();
  }
}

/* Buckets – each stream can be tuned independently */
const particles = {
  air: [],          // main airflow
  fuel: [],         // fuel droplets
  cool: [],         // bypass cooling air
  exhaust: []       // nozzle plume
};

/* --------------------------------------------------------------
   Emit helpers – they push a *new* particle into the proper bucket
   -------------------------------------------------------------- */
function emitAir(zone,count,speed,hue){
  const g = geometry();
  for(let i=0;i<count;i++){
    const y = g.cy + (Math.random()-0.5)*g.rad*1.4;
    const x = zone.x1 + Math.random()*(zone.x2-zone.x1);
    const vx = speed*(0.9+Math.random()*0.2);
    const vy = (Math.random()-0.5)*speed*0.08;
    particles.air.push(new Particle(x,y,vx,vy,1.6,2,
      `hsla(${hue},100%,55%,ALPHA)`,0));
  }
}
function emitFuel(zone,count,speed){
  const g = geometry();
  for(let i=0;i<count;i++){
    const y = g.cy + g.rad*0.5 + Math.random()*g.rad*0.2;
    const x = zone.x1 + Math.random()*(zone.x2-zone.x1);
    const vx = speed*(0.9+Math.random()*0.1);
    const vy = (Math.random()-0.5)*speed*0.05;
    particles.fuel.push(new Particle(x,y,vx,vy,1.2,2.5,
      `hsla(25,100%,55%,ALPHA)`,0));
  }
}
function emitCool(zone,count,speed){
  const g = geometry();
  for(let i=0;i<count;i++){
    const y = g.cy - g.rad*0.9 + Math.random()*g.rad*0.3;
    const x = zone.x1 + Math.random()*(zone.x2-zone.x1);
    const vx = speed*(0.9+Math.random()*0.1);
    const vy = (Math.random()-0.5)*speed*0.02;
    particles.cool.push(new Particle(x,y,vx,vy,1.4,1.8,
      `hsla(190,100%,70%,ALPHA)`,0));
  }
}
function emitExhaust(zone,count,speed,hue){
  const g = geometry();
  for(let i=0;i<count;i++){
    const y = g.cy + (Math.random()-0.5)*g.rad*0.5;
    const x = zone.x1 + Math.random()*(zone.x2-zone.x1);
    const vx = speed*(0.95+Math.random()*0.07);
    const vy = (Math.random()-0.5)*speed*0.07;
    const spin = (Math.random()>0.5?1:-1)*Math.PI*0.5;
    particles.exhaust.push(new Particle(x,y,vx,vy,2.2,2.2,
      `hsla(${hue},100%,60%,ALPHA)`,spin));
  }
}

/* ==============================================================
   5️⃣ DRAW ENGINE – static geometry + optional sections
   ============================================================== */
function drawEngine(){
  const g = geometry();
  ctx.lineWidth = 4;
  ctx.strokeStyle = '#666';
  ctx.fillStyle = config.xray ? 'rgba(20,20,20,0.4)' : 'rgba(0,0,0,0)';

  // ---- outer duct (transparent in X‑Ray mode) ----
  ctx.beginPath();
  ctx.moveTo(g.inletX, g.cy-g.rad);
  ctx.lineTo(g.outletX, g.cy-g.rad);
  ctx.lineTo(g.outletX, g.cy+g.rad);
  ctx.lineTo(g.inletX, g.cy+g.rad);
  ctx.closePath();
  ctx.fill();
  ctx.stroke();

  // ---- Low‑Pressure Compressor ----
  if(config.lpComp){
    ctx.beginPath();
    ctx.moveTo(g.lpCompInX, g.cy-g.rad);
    ctx.lineTo(g.lpCompOutX, g.cy-g.rad*0.75);
    ctx.lineTo(g.lpCompOutX, g.cy+g.rad*0.75);
    ctx.lineTo(g.lpCompInX, g.cy+g.rad);
    ctx.closePath();
    ctx.strokeStyle = '#88f';
    ctx.stroke();
  }

  // ---- High‑Pressure Compressor ----
  if(config.hpComp){
    ctx.beginPath();
    ctx.moveTo(g.hpCompInX, g.cy-g.rad*0.75);
    ctx.lineTo(g.hpCompOutX, g.cy-g.rad*0.45);
    ctx.lineTo(g.hpCompOutX, g.cy+g.rad*0.45);
    ctx.lineTo(g.hpCompInX, g.cy+g.rad*0.75);
    ctx.closePath();
    ctx.strokeStyle = '#5af';
    ctx.stroke();
  }

  // ---- Cooling‑air bypass (dashed) ----
  ctx.setLineDash([8,4]);
  ctx.strokeStyle = '#0dd';
  ctx.lineWidth = 2;
  ctx.beginPath();
  ctx.moveTo(g.bypassX, g.cy-g.rad*0.9);
  ctx.lineTo(g.combustorX1, g.cy-g.rad*0.9);
  ctx.stroke();
  ctx.setLineDash([]);

  // ---- Combustor walls ----
  ctx.strokeStyle = '#aaa';
  ctx.lineWidth = 4;
  ctx.beginPath();
  ctx.moveTo(g.combustorX1, g.cy-g.rad*0.45);
  ctx.lineTo(g.combustorX2, g.cy-g.rad*0.45);
  ctx.moveTo(g.combustorX1, g.cy+g.rad*0.45);
  ctx.lineTo(g.combustorX2, g.cy+g.rad*0.45);
  ctx.stroke();

  // ---- High‑Pressure Turbine ----
  if(config.hpTur){
    ctx.beginPath();
    ctx.moveTo(g.hpTurX1, g.cy-g.rad*0.45);
    ctx.lineTo(g.hpTurX2, g.cy-g.rad*0.3);
    ctx.lineTo(g.hpTurX2, g.cy+g.rad*0.3);
    ctx.lineTo(g.hpTurX1, g.cy+g.rad*0.45);
    ctx.closePath();
    ctx.strokeStyle = '#fa0';
    ctx.stroke();
  }

  // ---- Low‑Pressure Turbine ----
  if(config.lpTur){
    ctx.beginPath();
    ctx.moveTo(g.lpTurX1, g.cy-g.rad*0.3);
    ctx.lineTo(g.lpTurX2, g.cy-g.rad*0.15);
    ctx.lineTo(g.lpTurX2, g.cy+g.rad*0.15);
    ctx.lineTo(g.lpTurX1, g.cy+g.rad*0.3);
    ctx.closePath();
    ctx.strokeStyle = '#ff8';
    ctx.stroke();
  }

  // ---- After‑burner (optional) ----
  if(config.afterburner){
    ctx.beginPath();
    ctx.moveTo(g.afterburnerX1, g.cy-g.rad*0.15);
    ctx.lineTo(g.afterburnerX2, g.cy-g.rad*0.08);
    ctx.lineTo(g.afterburnerX2, g.cy+g.rad*0.08);
    ctx.lineTo(g.afterburnerX1, g.cy+g.rad*0.15);
    ctx.closePath();
    ctx.strokeStyle = '#ff4';
    ctx.stroke();
  }

  // ---- Nozzle (convergent‑divergent) ----
  ctx.beginPath();
  ctx.moveTo(g.nozzleX1, g.cy-g.rad*0.2);
  ctx.lineTo(g.outletX, g.cy-g.rad*0.07);
  ctx.lineTo(g.outletX, g.cy+g.rad*0.07);
  ctx.lineTo(g.nozzleX1, g.cy+g.rad*0.2);
  ctx.closePath();
  ctx.strokeStyle = '#aaa';
  ctx.stroke();

  // ---- Fuel line (orange) ----
  ctx.strokeStyle = '#b84';
  ctx.lineWidth = 3;
  ctx.beginPath();
  const fuelX = g.combustorX2 + 6;
  ctx.moveTo(fuelX, g.cy+g.rad*0.8);
  ctx.lineTo(fuelX, g.cy+g.rad*0.45);
  ctx.lineTo(g.combustorX2, g.cy+g.rad*0.45);
  ctx.stroke();

  /* ---- G A U G E S (N1, N2, ITT, Oil‑Pressure) ---- */
  drawGauges(g);
}

/* ==============================================================
   6️⃣ GAUGES – simple semi‑circular dials drawn on top of the engine
   ============================================================== */
function drawArcGauge(cx,cy,radius,startDeg,endDeg,perc,colour,label){
  const start = (Math.PI/180)*(startDeg);
  const end   = (Math.PI/180)*(endDeg);
  const angle = start + (end-start)*perc;

  // background arc
  ctx.beginPath();
  ctx.arc(cx,cy,radius,start,end,false);
  ctx.strokeStyle = '#444';
  ctx.lineWidth = 6;
  ctx.stroke();

  // value arc
  ctx.beginPath();
  ctx.arc(cx,cy,radius,start,angle,false);
  ctx.strokeStyle = colour;
  ctx.lineWidth = 6;
  ctx.stroke();

  // needle tip (tiny circle)
  const nx = cx + Math.cos(angle)*radius;
  const ny = cy + Math.sin(angle)*radius;
  ctx.beginPath();
  ctx.arc(nx,ny,4,0,Math.PI*2);
  ctx.fillStyle = colour;
  ctx.fill();

  // label underneath
  ctx.fillStyle = '#ddd';
  ctx.font = '12px Arial';
  ctx.textAlign = 'center';
  ctx.fillText(label,cx,cy+radius+14);
}

/* Draw the four cockpit‑style gauges */
function drawGauges(g) {
  const gaugeR = g.rad * 0.6;
  const baseX = g.inletX + g.rad * 0.4;
  const baseY = g.cy - g.rad * 2.2; // moved higher from -1.1 to -2.2

  // N1 (blue)
  drawArcGauge(baseX, baseY, gaugeR, 135, 45,
               n1Perc, '#6cf', `N1 ${Math.round(n1Val)}%`);

  // N2 (light‑blue)
  drawArcGauge(baseX + g.rad * 1.4, baseY, gaugeR, 135, 45,
               n2Perc, '#9cf', `N2 ${Math.round(n2Val)}%`);

  // ITT (orange)
  drawArcGauge(baseX + g.rad * 2.8, baseY, gaugeR, 135, 45,
               ittPerc, '#f96', `ITT ${Math.round(ittVal)}°C`);

  // Oil‑Pressure (green)
  drawArcGauge(baseX + g.rad * 4.2, baseY, gaugeR, 135, 45,
               opPerc, '#6f6', `OP ${Math.round(opVal)}psi`);

  // Add APU gauges
  drawAPUGauges(g);
}

function drawAPUGauges(g) {
  const gaugeR = g.rad * 0.5;
  const apuX = g.inletX - g.rad * 2;
  const apuY = g.cy - g.rad * 2.2;
  
  // APU RPM gauge
  const rpmPerc = apu.rpm / 1000;
  drawArcGauge(apuX, apuY, gaugeR, 135, 45, rpmPerc, '#f84', `APU ${Math.round(apu.rpm)} RPM`);
  
  // APU EGT gauge
  const egtPerc = Math.max(0, (apu.egt - 20) / 680);
  drawArcGauge(apuX, apuY + g.rad * 1.4, gaugeR, 135, 45, egtPerc, '#f44', `EGT ${Math.round(apu.egt)}°C`);
  
  // Status indicators
  ctx.fillStyle = apu.electrical ? '#0f0' : '#444';
  ctx.fillRect(apuX - 30, apuY + g.rad * 2.5, 20, 10);
  ctx.fillStyle = '#fff';
  ctx.font = '12px monospace';
  ctx.fillText('ELEC', apuX - 25, apuY + g.rad * 2.8);
  
  ctx.fillStyle = apu.bleedAir ? '#0f0' : '#444';
  ctx.fillRect(apuX + 10, apuY + g.rad * 2.5, 20, 10);
  ctx.fillText('BLEED', apuX + 15, apuY + g.rad * 2.8);
}

/* ==============================================================
   7️⃣ ENGINE METRICS (toy physics) + Graph handling
   ============================================================== */
let thrustHistory = [], massFlowHistory = [], egtHistory = [], ffHistory = [];
const MAX_HISTORY = 300; // ~5 seconds at 60 fps

function pushMetric(arr,val){
  arr.push(val);
  if(arr.length>MAX_HISTORY) arr.shift();
}
function drawGraph(){
  const w = graphCanvas.width, h = graphCanvas.height;
  gCtx.clearRect(0,0,w,h);
  gCtx.fillStyle = '#111';
  gCtx.fillRect(0,0,w,h);

  const drawSeries = (arr,color,scaleY,offsetY)=> {
    gCtx.strokeStyle = color;
    gCtx.lineWidth = 2;
    gCtx.beginPath();
    const len = arr.length;
    for(let i=0;i<len;i++){
      const x = w * i / (MAX_HISTORY-1);
      const y = h - (arr[i]*scaleY + offsetY);
      if(i===0) gCtx.moveTo(x,y);
      else gCtx.lineTo(x,y);
    }
    gCtx.stroke();
  };
  // Thrust (greenish)
  drawSeries(thrustHistory,'#ff6',0.04,10);
  // EGT (red‑orange)
  drawSeries(egtHistory,'#f44',0.05,10);
  // Fuel‑Flow (cyan)
  drawSeries(ffHistory,'#0ff',0.03,20);
}

/* ==============================================================
   8️⃣ AUDIO – simple engine roar that follows throttle
   ============================================================== */
const AudioContext = window.AudioContext||window.webkitAudioContext;
const audioCtx = new AudioContext();
let engineOsc = null;
let gainNode = audioCtx.createGain();
gainNode.connect(audioCtx.destination);

function createEngineSound(){
  engineOsc = audioCtx.createOscillator();
  engineOsc.type = 'sawtooth';
  engineOsc.frequency.setValueAtTime(30, audioCtx.currentTime);
  engineOsc.connect(gainNode);
  engineOsc.start();
}
function updateEngineSound(){
  if(!engineOsc) return;
  
  if (config.mute) {
    gainNode.gain.setTargetAtTime(0, audioCtx.currentTime, 0.01);
    return;
  }
  
  if (!running) {
    gainNode.gain.setTargetAtTime(0, audioCtx.currentTime, 0.01);
    return;
  }
  
  const freq = 30 + throttle*170;
  engineOsc.frequency.linearRampToValueAtTime(freq, audioCtx.currentTime+0.05);
  const vol = 0.08 + throttle*0.12;
  gainNode.gain.linearRampToValueAtTime(vol, audioCtx.currentTime+0.05);
}
function startAudio(){
  if(audioCtx.state === 'suspended') audioCtx.resume();
  if(!engineOsc) createEngineSound();
}
function stopAudio(){
  if(engineOsc){ engineOsc.disconnect(); engineOsc = null; }
}
const engineAudio = {play:startAudio, pause:stopAudio};

/* APU System State */
let apu = {
  running: false,
  startupPhase: 'off', // 'off', 'starting', 'running', 'shutdown'
  rpm: 0,
  egt: 0,
  startupTimer: 0,
  bleedAir: false,
  electrical: false
};

/* APU Controls */
document.getElementById('btnAPUStart').onclick = () => {
  if (apu.startupPhase === 'off') {
    startAPUSequence();
  } else if (apu.running) {
    shutdownAPU();
  }
};

function startAPUSequence() {
  apu.startupPhase = 'starting';
  apu.startupTimer = 0;
  document.getElementById('btnAPUStart').textContent = 'Starting...';
  document.getElementById('apuStatus').textContent = 'STARTING';
  
  // APU startup audio
  if (!config.mute) {
    playAPUStartupSound();
  }
}

function updateAPUSequence(dt) {
  if (apu.startupPhase === 'starting') {
    apu.startupTimer += dt;
    
    // Phase 1: Starter motor (0-10s)
    if (apu.startupTimer < 10) {
      apu.rpm = Math.min(apu.startupTimer * 15, 150); // Ramp to 150 RPM
      apu.egt = 20 + apu.startupTimer * 5; // Gradual temp rise
    }
    // Phase 2: Ignition and acceleration (10-25s)
    else if (apu.startupTimer < 25) {
      const phase2Time = apu.startupTimer - 10;
      apu.rpm = 150 + phase2Time * 50; // Accelerate to 900 RPM
      apu.egt = 70 + phase2Time * 40; // EGT rises to 670°C
    }
    // Phase 3: Stabilization (25-30s)
    else if (apu.startupTimer < 30) {
      apu.rpm = Math.min(900 + (apu.startupTimer - 25) * 20, 1000);
      apu.egt = Math.max(670 - (apu.startupTimer - 25) * 10, 620);
    }
    // Phase 4: Ready
    else {
      apu.running = true;
      apu.startupPhase = 'running';
      apu.rpm = 1000;
      apu.egt = 620;
      apu.electrical = true;
      apu.bleedAir = true;
      document.getElementById('btnAPUStart').textContent = '⏹ Stop APU';
      document.getElementById('apuStatus').textContent = 'RUNNING';
    }
  }
}

function shutdownAPU() {
  // Don't allow APU shutdown if main engine is running
  if (running || engineStarting) {
    alert('Cannot shutdown APU while main engine is running or starting!');
    return;
  }

  apu.running = false;
  apu.startupPhase = 'shutdown';
  apu.electrical = false;
  apu.bleedAir = false;

  // Gradual spindown
  const spindownInterval = setInterval(() => {
    apu.rpm = Math.max(apu.rpm - 50, 0);
    apu.egt = Math.max(apu.egt - 30, 20);

    if (apu.rpm <= 0) {
      apu.startupPhase = 'off';
      document.getElementById('btnAPUStart').textContent = '▶️ Start APU';
      document.getElementById('apuStatus').textContent = 'OFF';
      clearInterval(spindownInterval);
    }
  }, 100);
}

function startEngineSequence() {
  engineStarting = true;
  engineStartTimer = 0;
  document.getElementById('btnRun').textContent = 'Starting Engine...';
  document.getElementById('engineStatus').textContent = 'STARTING';

  console.log('Engine start initiated using APU power and bleed air');

  // Engine startup audio
  if (!config.mute) {
    playEngineStartupSound();
  }
}

function updateEngineStartSequence(dt) {
  if (engineStarting) {
    engineStartTimer += dt;

    // Show startup progress
    const progress = Math.min(engineStartTimer / 20, 1);
    const progressPercent = Math.round(progress * 100);
    document.getElementById('engineStatus').textContent = `STARTING ${progressPercent}%`;

    // Engine startup takes about 20 seconds
    if (engineStartTimer >= 20) {
      // Engine successfully started
      engineStarting = false;
      running = true;
      document.getElementById('btnRun').textContent = '⏸ Stop Engine';
      document.getElementById('engineStatus').textContent = 'RUNNING';
      engineAudio.play();
      console.log('Engine started successfully!');
    }
  } else if (!running) {
    document.getElementById('engineStatus').textContent = 'OFF';
  }
}

function playEngineStartupSound() {
  // Create engine startup sound sequence
  const startOsc = audioCtx.createOscillator();
  const startGain = audioCtx.createGain();

  startOsc.type = 'sawtooth';
  startOsc.frequency.setValueAtTime(15, audioCtx.currentTime);
  startOsc.frequency.linearRampToValueAtTime(200, audioCtx.currentTime + 20);

  startGain.gain.setValueAtTime(0, audioCtx.currentTime);
  startGain.gain.linearRampToValueAtTime(0.03, audioCtx.currentTime + 3);
  startGain.gain.setValueAtTime(0.03, audioCtx.currentTime + 17);
  startGain.gain.linearRampToValueAtTime(0, audioCtx.currentTime + 20);

  startOsc.connect(startGain);
  startGain.connect(audioCtx.destination);
  startOsc.start();
  startOsc.stop(audioCtx.currentTime + 20);
}

function playAPUStartupSound() {
  // Create APU startup sound sequence
  const apuOsc = audioCtx.createOscillator();
  const apuGain = audioCtx.createGain();
  
  apuOsc.type = 'sawtooth';
  apuOsc.frequency.setValueAtTime(20, audioCtx.currentTime);
  apuOsc.frequency.linearRampToValueAtTime(80, audioCtx.currentTime + 30);
  
  apuGain.gain.setValueAtTime(0, audioCtx.currentTime);
  apuGain.gain.linearRampToValueAtTime(0.05, audioCtx.currentTime + 2);
  apuGain.gain.setValueAtTime(0.05, audioCtx.currentTime + 28);
  apuGain.gain.linearRampToValueAtTime(0.03, audioCtx.currentTime + 30);
  
  apuOsc.connect(apuGain);
  apuGain.connect(audioCtx.destination);
  apuOsc.start();
  apuOsc.stop(audioCtx.currentTime + 30);
}

/* ==============================================================
   9️⃣ MAIN LOOP
   ============================================================== */
let lastTs = performance.now();
let fpsCounter = 0, fpsTimer = 0;

/* Variables that will be used by the gauge‑drawing function */
let n1Val=0, n2Val=0, ittVal=0, opVal=0;
let n1Perc=0, n2Perc=0, ittPerc=0, opPerc=0;

function loop(ts){
  const dt = (ts - lastTs) / 1000; // seconds
  lastTs = ts;

  /* ---- FPS counter ---- */
  fpsTimer += dt;
  fpsCounter++;
  if(fpsTimer > 1){
    document.getElementById('fps').textContent = `FPS: ${fpsCounter}`;
    fpsCounter = 0; fpsTimer = 0;
  }

  /* ---- Background gradient ---- */
  const grad = ctx.createLinearGradient(0,0,0,H);
  grad.addColorStop(0,'#0a0a0a');
  grad.addColorStop(1,'#020202');
  ctx.fillStyle = grad;
  ctx.fillRect(0,0,W,H);

  /* ---- Engine geometry + gauges ---- */
  drawEngine();

  /* ---- Emit new particles (only when running) ---- */
  if(running){
    // Fuel consumption
    fuelQuantity -= 0.01 * throttle;
    if (fuelQuantity < 0) {
      fuelQuantity = 0;
      running = false;
      engineStarting = false;
      engineStartTimer = 0;
      document.getElementById('btnRun').textContent = '▶️ Start Engine';
      document.getElementById('engineStatus').textContent = 'OFF';
      engineAudio.pause();
      alert('Fuel depleted! Engine stopped.');
    }
    document.getElementById('fuelQuantity').textContent = Math.round(fuelQuantity);

    const g = geometry();

    // Inlet air (cold)
    const inletSpd = 40 * throttle;
    emitAir({x1:g.inletX,   x2:g.lpCompInX},   4, inletSpd, 210);

    // Low‑Pressure Compressor
    if(config.lpComp){
      const lpSpd = inletSpd * 1.5;
      emitAir({x1:g.lpCompInX, x2:g.lpCompOutX}, 3, lpSpd, 200);
    }

    // High‑Pressure Compressor
    if(config.hpComp){
      const hpSpd = inletSpd * 2.0;
      emitAir({x1:g.hpCompInX, x2:g.hpCompOutX}, 2, hpSpd, 190);
    }

    // Cooling‑air bypass
    const coolSpd = inletSpd * 1.2;
    emitCool({x1:g.bypassX, x2:g.combustorX1}, 2, coolSpd);

    // Fuel droplets
    const fuelSpd = 50 * throttle;
    emitFuel({x1:g.combustorX2-12, x2:g.combustorX2+4}, 3, fuelSpd);

    // Combustor hot gases
    const combustSpd = inletSpd * 2.6;
    emitAir({x1:g.combustorX1, x2:g.combustorX2}, 3, combustSpd, 30);

    // High‑Pressure Turbine
    if(config.hpTur){
      const hpTurSpd = combustSpd * 0.9;
      emitAir({x1:g.hpTurX1, x2:g.hpTurX2}, 2, hpTurSpd, 40);
    }

    // Low‑Pressure Turbine
    if(config.lpTur){
      const lpTurSpd = combustSpd * 0.7;
      emitAir({x1:g.lpTurX1, x2:g.lpTurX2}, 2, lpTurSpd, 45);
    }

    // After‑burner (extra hot plume)
    if(config.afterburner){
      const abSpd = combustSpd * 1.2;
      emitAir({x1:g.afterburnerX1, x2:g.afterburnerX2}, 2, abSpd, 25);
    }

    // Exhaust plume – colour shifts with throttle
    const exitSpd = combustSpd * 2.5;
    const hue = 15 + throttle*30; // from orange → yellowish
    emitExhaust({x1:g.nozzleX1, x2:g.outletX}, 6, exitSpd, hue);
  }

  /* ---- Update & draw particles ---- */
  const buckets = Object.values(particles);
  for(const bucket of buckets){
    for(let i=bucket.length-1;i>=0;i--){
      const p = bucket[i];
      p.update(dt);
      p.draw(ctx);
      if(p.life<=0 ||
         p.x<-30 || p.x>W+30 ||
         p.y<-30 || p.y>H+30){
        bucket.splice(i,1);
      }
    }
  }

  /* ---- Simple “physics” for the read‑outs ---- */
  // ---- Primary read‑out (kept from original) ----
  const massFlow = (throttle*0.8).toFixed(2);
  const temperature = (800 + throttle*200).toFixed(0);
  const thrust = (throttle*2500).toFixed(0);
  document.getElementById('mf').textContent = massFlow;
  document.getElementById('temp').textContent = temperature;
  document.getElementById('thrust').textContent = thrust;

  // ---- New pilot‑style metrics ----
  const egt      = Math.round(600 + throttle*200);   // °C
  const itt      = Math.round(500 + throttle*150);   // °C (a bit cooler)
  const n1       = Math.min(100, Math.round(throttle*100));   // %
  const n2       = Math.min(95,  Math.round(throttle*95));    // %
  const fuelFlow = Math.round(throttle*1800);        // lb/h (toy)
  const oilPress = Math.round(20 + throttle*40);    // psi

  document.getElementById('egt').textContent = egt;
  document.getElementById('itt').textContent = itt;
  document.getElementById('n1').textContent = n1;
  document.getElementById('n2').textContent = n2;
  document.getElementById('ff').textContent = fuelFlow;
  document.getElementById('op').textContent = oilPress;

  // Update gauge helper variables (0‑1 range)
  n1Val = n1; n2Val = n2; ittVal = itt; opVal = oilPress;
  n1Perc = n1/100; n2Perc = n2/95; ittPerc = (itt-400)/200; // 400‑600°C range → 0‑1
  opPerc  = (oilPress-20)/60;   // 20‑80 psi → 0‑1

  // ---- Push to history for the bottom graph ----
  pushMetric(thrustHistory, thrust/10);          // scale down for visible range
  pushMetric(egtHistory,    egt/6);             // also scale
  pushMetric(ffHistory,    fuelFlow/30);
  drawGraph();

  // ---- Audio follow‑up ----
  if(running) updateEngineSound();

  // Update APU sequence
  updateAPUSequence(dt);

  // Update engine startup sequence
  updateEngineStartSequence(dt);

  requestAnimationFrame(loop);
}
requestAnimationFrame(loop);