'use server'

export type videoSource = 'xvideos' | 'pornhub' | 'youtube';

export const convertLink = async (link: string, convertSource: videoSource) => {
    'use server'
    if(convertSource == 'xvideos') {
    // Regular expression to match the original format and capture the unique part of the URL
    const regex = /https:\/\/www\.xvideos\.com\/video\.([a-z0-9]+)\/.*/;
    // Use the replace method to create the new URL format
    const newLink = link.replace(regex, 'https://www.xvideos.com/embedframe/$1');
    console.log(newLink)
    return newLink;
    }
    if(convertSource == 'pornhub') {
        // Regular expression to match the original format and capture the unique part of the URL
        const regex = /https:\/\/www\.pornhub\.com\/view_video\.php\?viewkey=([^&]+)/;
        // Use the replace method to create the new URL format
        console.log(link.match(regex))
        
        const newLink = link.replace(regex, 'https://www.pornhub.com/embed/$1');
        console.log(newLink)
        return newLink;
        }
    if(convertSource == 'youtube') {
            // Regular expression to match the original format and capture the unique part of the URL
            const regex = /https:\/\/www\.youtube\.com\/watch\?v=([^&]+).*/;
            // Use the replace method to create the new URL format
            console.log(link.match(regex))
            const newLink = link.replace(regex, 'https://www.youtube.com/embed/$1');          
              console.log(newLink)
            return newLink;
            }
    else{
        
        throw new Error('Invalid video source');
    }
};