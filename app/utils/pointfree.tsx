// In order to find functions that are more production-ready, have a peek at ramda, lodash, or folktale.





// Essential Functions Support

// always :: a -> b -> a
const always = curry((a, b) => a);
// compose :: ((y -> z), (x -> y),  ..., (a -> b)) -> a -> z
const compose = (...fns) => (...args) => fns.reduceRight((res, fn) => [fn.call(null, ...res)], args)[0];
// curry :: ((a, b, ...) -> c) -> a -> b -> ... -> c
function curry(fn) {
  const arity = fn.length;

  return function $curry(...args) {
    if (args.length < arity) {
      return $curry.bind(null, ...args);
    }

    return fn.call(null, ...args);
  };
}
// either :: (a -> c) -> (b -> c) -> Either a b -> c
const either = curry((f, g, e) => {
  if (e.isLeft) {
    return f(e.$value);
  }

  return g(e.$value);
});
// identity :: x -> x
const identity = x => x;
// inspect :: a -> String
const inspect = (x) => {
  if (x && typeof x.inspect === 'function') {
    return x.inspect();
  }

  function inspectFn(f) {
    return f.name ? f.name : f.toString();
  }

  function inspectTerm(t) {
    switch (typeof t) {
      case 'string':
        return `'${t}'`;
      case 'object': {
        const ts = Object.keys(t).map(k => [k, inspect(t[k])]);
        return `{${ts.map(kv => kv.join(': ')).join(', ')}}`;
      }
      default:
        return String(t);
    }
  }

  function inspectArgs(args) {
    return Array.isArray(args) ? `[${args.map(inspect).join(', ')}]` : inspectTerm(args);
  }

  return (typeof x === 'function') ? inspectFn(x) : inspectArgs(x);
};
// left :: a -> Either a b
const left = a => new Left(a);
// liftA2 :: (Applicative f) => (a1 -> a2 -> b) -> f a1 -> f a2 -> f b
const liftA2 = curry((fn, a1, a2) => a1.map(fn).ap(a2));
// liftA3 :: (Applicative f) => (a1 -> a2 -> a3 -> b) -> f a1 -> f a2 -> f a3 -> f b
const liftA3 = curry((fn, a1, a2, a3) => a1.map(fn).ap(a2).ap(a3));
// maybe :: b -> (a -> b) -> Maybe a -> b
const maybe = curry((v, f, m) => {
  if (m.isNothing) {
    return v;
  }

  return f(m.$value);
});
// nothing :: Maybe a
const nothing = Maybe.of(null);
// reject :: a -> Task a b
const reject = a => Task.rejected(a);





/////////////////////////////////////////////////////////////

// add :: Number -> Number -> Number
const add = curry((a, b) => a + b);

// append :: String -> String -> String
const append = flip(concat);

// chain :: Monad m => (a -> m b) -> m a -> m b
const chain = curry((fn, m) => m.chain(fn));

// concat :: String -> String -> String
const concat = curry((a, b) => a.concat(b));

// eq :: Eq a => a -> a -> Boolean
const eq = curry((a, b) => a === b);

// filter :: (a -> Boolean) -> [a] -> [a]
const filter = curry((fn, xs) => xs.filter(fn));

// flip :: (a -> b -> c) -> b -> a -> c
const flip = curry((fn, a, b) => fn(b, a));

// forEach :: (a -> ()) -> [a] -> ()
const forEach = curry((fn, xs) => xs.forEach(fn));

// head :: [a] -> a
const head = xs => xs[0];

// intercalate :: String -> [String] -> String
const intercalate = curry((str, xs) => xs.join(str));

// join :: Monad m => m (m a) -> m a
const join = m => m.join();

// last :: [a] -> a
const last = xs => xs[xs.length - 1];

// map :: Functor f => (a -> b) -> f a -> f b
const map = curry((fn, f) => f.map(fn));

// match :: RegExp -> String -> Boolean
const match = curry((re, str) => re.test(str));

// prop :: String -> Object -> a
const prop = curry((p, obj) => obj[p]);

// reduce :: (b -> a -> b) -> b -> [a] -> b
const reduce = curry((fn, zero, xs) => xs.reduce(fn, zero));

// replace :: RegExp -> String -> String -> String
const replace = curry((re, rpl, str) => str.replace(re, rpl));

// reverse :: [a] -> [a]
const reverse = x => (Array.isArray(x) ? x.reverse() : x.split('').reverse().join(''));

// safeHead :: [a] -> Maybe a
const safeHead = compose(Maybe.of, head);

// safeLast :: [a] -> Maybe a
const safeLast = compose(Maybe.of, last);

// safeProp :: String -> Object -> Maybe a
const safeProp = curry((p, obj) => compose(Maybe.of, prop(p))(obj));

// sequence :: (Applicative f, Traversable t) => (a -> f a) -> t (f a) -> f (t a)
const sequence = curry((of, f) => f.sequence(of));

// sortBy :: Ord b => (a -> b) -> [a] -> [a]
const sortBy = curry((fn, xs) => xs.sort((a, b) => {
  if (fn(a) === fn(b)) {
    return 0;
  }

  return fn(a) > fn(b) ? 1 : -1;
}));
// split :: String -> String -> [String]
const split = curry((sep, str) => str.split(sep));
// take :: Number -> [a] -> [a]
const take = curry((n, xs) => xs.slice(0, n));
// toLowerCase :: String -> String
const toLowerCase = s => s.toLowerCase();
// toString :: a -> String
const toString = String;
// toUpperCase :: String -> String
const toUpperCase = s => s.toUpperCase();
// traverse :: (Applicative f, Traversable t) => (a -> f a) -> (a -> f b) -> t a -> f (t b)
const traverse = curry((of, fn, f) => f.traverse(of, fn));
// unsafePerformIO :: IO a -> a
const unsafePerformIO = io => io.unsafePerformIO();
