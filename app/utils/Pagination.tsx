'use client'

import React, { useState } from 'react'
import { useSearchParams } from 'next/navigation'



export function usePagination(data: object[], paginationCount: number): [object[], React.FC, React.FC] {

  const [sliceTopNumber, setSliceTopNumber] = useState(paginationCount)
  const [sliceBottomNumber, setSliceBottomNumber] = useState(0)
  const [activeButton, setActiveButton] = useState(1);
  const [showAllButtonColor, setShowAllButtonColor] = useState('bg-white');
  const [show10ButtonColor, setShow10ButtonColor] = useState('bg-green-300');

  function handlePagination (evt: React.MouseEvent<HTMLButtonElement>) {  
    let target = evt.target as HTMLButtonElement
    console.log(target.textContent?.toLowerCase())
    if(target.textContent?.toLowerCase() === 'show 10') {
        setSliceTopNumber(10)
        setShow10ButtonColor('bg-green-300')
        setShowAllButtonColor('bg-white')
    }
    if(target?.textContent?.toLowerCase() == 'show all') {
        setSliceTopNumber(453)
        setShowAllButtonColor('bg-green-300')
        setShow10ButtonColor('bg-white')
    }
     
}

  function PaginationHeader() {
    return (
      <div className='flex flex-row items-center justify-center gap-1 p-1'>
        <button onClick={(evt) => handlePagination(evt)} className={`btn ${showAllButtonColor}`}>Show All</button><button onClick={(evt) => handlePagination(evt)} className={`btn ${show10ButtonColor}`}>Show 10</button>
      </div>
    )
  }

  function PaginationFooter() {
    const handleButtonClick = (buttonNumber: number) => {
      console.log(buttonNumber)
      setActiveButton(buttonNumber)
      setSliceTopNumber(buttonNumber * 10)
      setSliceBottomNumber((buttonNumber * 10) - 10)


    };

    function generateNumberRange(end: number) {
      const result = [];
      for (let i = 1; i <= end; i++) {
        result.push(i);
      }
      return result;
    }

    let pages = Math.ceil(data.length / 10)

    let paginationRange = generateNumberRange(pages)
    console.log(paginationRange)

    console.log(pages)
    return (
      <div className='w-full'>
        {sliceTopNumber != 453 && <div className='flex flex-wrap justify-evenly items-center p-9'>
          <div className="join flex flex-wrap justify-center">
            <button className="join-item btn btn-md md:btn-lg">«</button>
            {paginationRange.map((number) => {
              return <button key={number} onClick={() => handleButtonClick(number)} className={`join-item btn btn-md md:btn-lg ${activeButton === number ? 'btn-active' : ''}`}>{number}</button>
            })}
            <button className="join-item btn btn-md md:btn-lg">»</button>
          </div>
        </div>}
      </div>
    )
  }

  let alteredData: any = []
  if (data) {

    alteredData = data.slice(sliceBottomNumber, sliceTopNumber)
  }


  return [alteredData, PaginationFooter, PaginationHeader]
}

