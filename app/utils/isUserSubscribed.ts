

import { isAMTCreditsRemaining } from "@/app/db/dbCurry";
import { digitalServiceDetector } from "@/app/utils/digitalServiceDetector"
import { handlePayment } from "@/app/services/googlePurchase";

/**
 * this is what the function needs to run
 * import useModal from "@/app/components/ui/Modal4";
 * import { useCallbackModal } from "@/app/components/ui/Modal4.CallBackModal";
 * import useDevModeToggle from "@/app/components/ai-transcriptions/internalComponents/DevModeToggle"
  * @example
 * ```jsx
    const [GenericModal, genericRef] = useModal();
    const [genericModalText, setGenericModalText] = useState('')
    const [toggled, DevToggleButton] = useDevModeToggle();
    const [GenericCallBackModal, showCallbackModal, closeCallbackModal, setModalCallback] = useCallbackModal();
    const genericModal = (
    <GenericModal>
      <div className="p-6 prose bg-gray-900 rounded-lg text-white">
        <h2 className="text-2xl font-bold text-blue-400 mb-4">{genericModalText}</h2>
        <div className="flex justify-end gap-3 mt-6">
          <button className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors shadow-md" onClick={() => genericRef.current?.close()}>
            Close
          </button>
        </div>
      </div>
    </GenericModal>
    );

    return (
    <>
      {userId == "690d75369547d4dbacd72cfe" && <DevToggleButton />}
      {genericModal}
    //   the rest of your content
    </>
  );
 *```
*/
  async function didUserSubscribe() {
    const service = await window.getDigitalGoodsService('https://play.google.com/billing');
    console.log('Google Play Billing service is available')
    let purchases = await service.listPurchases();
    console.log("these are the purchases:", purchases)
    if (purchases.length > 0) {
      return true
    } else {
      return false
    };
  }


export async function isUserSubscribed(userId: string, setGenericModalText: React.Dispatch<React.SetStateAction<string>>, genericRef: React.RefObject<HTMLDialogElement>, toggled: boolean, showCallbackModal: (title: string, message: string, callback: () => void, confirmText?: string) => void, callbackFunction: () => void, closeCallbackFunction: () => void) {
    console.log(userId)
    let trialPeriod = await isAMTCreditsRemaining(userId)
    console.log('trialPeriod:', trialPeriod) 
    console.log('dev toggled:', toggled)
    let didUserPay: boolean | null = null
    //if (trialPeriod == false && didUserPay == false) {
      //console.log('hit promo')
      //setGenericModalText('You Need to Subscribe to Use the A&P App')
      //genericRef.current?.showModal()
    //}
    if (trialPeriod == false) {
      //let testToken = await getAccessToken();
      //console.log('test access token****', testToken)
      //we only care about payments if its on a mobile device, computer can use for free
      let isOnMobile = digitalServiceDetector()
      // didUserPay = !isOnMobile ? false : await handlePayment()
      let simulatedSubscription = toggled as boolean // set to true by default unless the dev hits the toggle button in settings
      console.log(simulatedSubscription, toggled)
      didUserPay = !isOnMobile ? simulatedSubscription : await didUserSubscribe()

    }

    console.log(trialPeriod)
    let isAllowedToUseApp = trialPeriod || didUserPay
    console.log(isAllowedToUseApp)
    if (isAllowedToUseApp && (toggled == true)){
      await callbackFunction()
    }
    if (!isAllowedToUseApp || !toggled) {
        console.log('not allowed to use app')
      showCallbackModal(
        'Your 7 Day Free Trial Ended',
        'Please Subscribe to Continue Using the App.',
        () => {
          handlePayment()
        }, undefined, 'Continue on Preview Mode', () =>{
          closeCallbackFunction()
        }
      )
    }
  }