/**
 * Detects the availability of the DigitalGoodsService in the browser environment.
 *
 * This function only works in browser environments. It checks if the `getDigitalGoodsService`
 * method is available either on the `navigator` or `window` object. If available, it logs the presence
 * to the console (unless logging is disabled) and returns `true`. If not available, it logs the 
 * absence (unless logging is disabled) and returns `false`.
 *
 * @param silent - Optional. If set to `true`, suppresses all console logs. Defaults to `false`.
 * @returns `true` if DigitalGoodsService is available, otherwise `false`.
 */
export function digitalServiceDetector(silent: boolean = false): boolean {
  if (typeof window !== 'undefined') {
    const hasInNavigator: boolean = 'getDigitalGoodsService' in navigator;
    const hasInWindow: boolean = 'getDigitalGoodsService' in window;

    if (!silent) {
      if (hasInNavigator) {
        console.log('DigitalGoodsService is available in the navigator.');
      }
      if (hasInWindow) {
        console.log('DigitalGoodsService is available in the window.');
      }
      if (!hasInNavigator && !hasInWindow) {
        console.log('DigitalGoodsService is not available.');
      }
    }

    return hasInNavigator || hasInWindow;
  }

  // Not in browser environment
  return false;
}
