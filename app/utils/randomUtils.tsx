//Debounce Utill
//Only triggers 500ms after the user stops typing
import React, { useState } from "react";

// export function FisherYatesShuffle(array) {
//     function shuffle(array) {
//         for (let i = array.length - 1; i > 0; i--) {
//             const j = Math.floor(Math.random() * (i + 1));
//             [array[i], array[j]] = [array[j], array[i]];
//         }
//         return array;
//     }

//     return array;
// }

export function SearchBox() {
    const [timer, setTimer] = useState(null);

    function onChange(e) {
        const value = e.target.value;

        clearTimeout(timer); // cancel previous
        const t = setTimeout(() => {
            console.log("Searching:", value);
        }, 500);

        setTimer(t);
    }

    return <input onChange={onChange} className="border p-2" />;
}