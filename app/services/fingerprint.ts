export async function getStableFingerprint() {
    const fp = {
      userAgent: navigator.userAgent,
      platform: navigator.platform,
      language: navigator.language,
      hardwareConcurrency: navigator.hardwareConcurrency,
      deviceMemory: navigator.deviceMemory,
      maxTouchPoints: navigator.maxTouchPoints,
      screenResolution: `${screen.width}x${screen.height}`,
      colorDepth: screen.colorDepth,
      windowSize: `${window.innerWidth}x${window.innerHeight}`,
      timezoneOffset: new Date().getTimezoneOffset(),
      pluginsCount: navigator.plugins ? navigator.plugins.length : 0,
    };
  
    return fp;
  }
  //
  export async function getFingerprintHash() {
    const stableFp = await getStableFingerprint();
    return sha256(JSON.stringify(stableFp));
  }

  // Simple sha256 hashing function
export async function sha256(str:string) {
  let hash = 0;
  if (str.length === 0) return hash.toString();
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = (hash << 5) - hash + char;
    hash |= 0; // Convert to 32bit integer
  }
  return hash.toString();
}