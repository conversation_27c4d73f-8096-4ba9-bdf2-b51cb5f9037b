


// 'complex-math-gpt-questions','complex-math-groq-questions', 'complex-math-gpt-open-ai-activate-no-questions', 'complex-math-groq-activate-no-questions
// give me questions where i need to select the correct improper integral
// AISelectOutput

export const mathGeneratingRole = (prompt: string) => [
  {
    role: "assistant",
    content: `I want you to format any math expressions like this weather it be derivates, integrals, or anything just follow this format, just follow the best one you need for your example but if its in the middle of a sentence do the inline one: Inline math: $\\int 3x^2 \\, dx = x^3 + C$ . make sure you enclose inline quotes with a $

and enclose both sides with $$ if you have a Block math:

$$
\\int 3x^2 \\, dx = x^3 + C
$$`,
  },
  { role: "user", content: prompt },
];

export const mathAndQuestionsRole = (prompt: string) => [
  {
    role: "assistant",
    content: `Based on the example I provide, generate an array of multiple-choice questions in JSON format. Each object in the array should include:

A "question" field (string)

An "options" field (array of 4 strings)

A "correct_answer" field (string, exactly matching one of the options)

Format the output as a JSON array. Here's an example:

Example Input:
The capital of France is Paris. The tallest mountain in the world is Mount Everest.

Example Output (JSON):

json
Copy
Edit
[
{
"question": "What is the capital of France?",
"options": ["London", "Berlin", "Madrid", "Paris"],
"correct_answer": "Paris"
},
{
"question": "What is the tallest mountain in the world?",
"options": ["K2", "Kangchenjunga", "Mount Everest", "Lhotse"],
"correct_answer": "Mount Everest"
}
]
Now, using the following input, generate a similar JSON array of multiple-choice questions and give me the json response only, nothing elsee, make sure you dont give any intro, explination or anything, or any extra quotes. Also I want you to format any math expressions like this weather it be derivates, integrals, or anything just follow this format, just follow the best one you need for your example but if its in the middle of a sentence do the inline one: Inline math: $\\int 3x^2 \\, dx = x^3 + C$ . make sure you enclose inline quotes with a $, and dont include newline characters.

Block math:

$$
\\int 3x^2 \\, dx = x^3 + C
$$
and dont start the json with blockmath, make sure i can convert the entire thing to json`,
  },
  {
    role: "user",
    content: prompt,
  },
];
