import { getAccessToken } from "@/app/components/AMTQuestionStream/amtacknowledge";

 let accessToken: string | null = null

export async function handlePayment() {
    console.log('hit promo')
    try {
      //const [digitalGoods] = await navigator.getDigitalGoodsService('play');

      const service = await window.getDigitalGoodsService('https://play.google.com/billing');
      console.log('Google Play Billing service is available')

      let purchases = await service.listPurchases();
      console.log("these are the purchases:", purchases)
      let didCustomerPurchase = null
      if (purchases.length > 0) {
        didCustomerPurchase = true
        console.log('did customer purchase?', didCustomerPurchase)

        return true
      } else { didCustomerPurchase = false };
      console.log('did customer purchase?', didCustomerPurchase)

      // const itemDetails = await service.getDetails(['1','3']);


      const itemDetails = await service.getDetails(['12']);
      console.log('these are the item details:')
      for (const item of itemDetails) {
        // Display item information to user
        console.log(item.title, item.description, item.price, item.itemType, item.subscriptionPeriod, item.introductoryPrice, item.introductoryPricePeriod, item.freeTrialPeriod, item.sku, item.itemId);
        console.log('full object', item)
      }
      console.log("end of console.logs for this segment")

      //   [
      //     {
      //         "itemId": "3",
      //         "purchaseToken": "ahfhmlgflmgclipffnaafkem.AO-J1Oxon0pZNjt8BF8Jw4-FxMWduE7geFVVYcbY5CJ8vu08qf63bHMA1Mn-hBJTB-JTX5SMrCDA9D6J2Of0sAKinxUupla6MQ"
      //     }
      // ]
      // const paymentMethodData = [
      //   {
      //     supportedMethods: 'https://play.google.com/billing',
      //     data: {
      //       sku: "1"
      //     },
      //   },
      // ];
      const paymentMethodData = [
        {
          supportedMethods: 'https://play.google.com/billing',
          data: {
            sku: '12',
            // type: 'SUBS'  // Specify that this is a subscription
          },
        },
      ];
      const request = new PaymentRequest(paymentMethodData);
      const paymentResponse = await request.show();

      const { purchaseToken } = paymentResponse.details;
      console.log('this is the purchase response', purchaseToken)
      let paymentComplete = null;
      accessToken = await getAccessToken();
      console.log('access token', accessToken)
      let subscriptionResult = await acknowledgeSubscription('com.faamalc.twa', '12', purchaseToken);
      if (subscriptionResult) {
        paymentComplete = await paymentResponse.complete('success');
        console.log('payment complete', paymentComplete)
        return true
        // Let user know their purchase transaction has successfully completed and been verified 
      } else {
        paymentComplete = await paymentResponse.complete('fail');
        console.log('payment complete', paymentComplete)
        return false
        // Let user know their purchase transaction failed to verify 
      }

    } catch (error) {
      console.log('Error Processing Payment')
      return false
    }
  }

  async function acknowledgeSubscription(packageName: string, subscriptionId: string, token: string) {
    const url = `https://androidpublisher.googleapis.com/androidpublisher/v3/applications/${packageName}/purchases/subscriptions/${subscriptionId}/tokens/${token}:acknowledge`;

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({}) // Optional: { developerPayload: 'yourPayload' }
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Error ${response.status}: ${errorText}`);
      }

      // Some responses return empty object {}, so we handle that:
      const data = await response.text();
      console.log('Acknowledged successfully:', data || '{}');
      return true;
    } catch (err) {
      console.error('Failed to acknowledge subscription:', err.message);
      return false;
    }
  }