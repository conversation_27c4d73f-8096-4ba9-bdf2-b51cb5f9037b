export const getIP = async () => {
    try {
        const response = await fetch('/api2/ip-address', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json'
            }
        });

        const data = await response.json();
        
        if (data.success) {
            return data.data.ip; // This will return just the IP address
        } else {
            throw new Error(data.error);
        }
    } catch (error) {
        console.error('Failed to get IP:', error);
        throw error;
    }
};