body {
    font-family: Arial, sans-serif;
}

.container {
    margin: 0 14%;
    padding: 1rem;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.title {
    text-align: center;
    font-size: 2rem;
    margin-bottom: 1rem;
}

.input {
    width: 100%;
    max-width: 300px;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    margin-bottom: 1rem;
}

.controls {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    margin-bottom: 1rem;
}

.control {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.sort-button {
    background-color: #ff9e4a;
    border: none;
    padding: 0.5rem;
    border-radius: 4px;
    cursor: pointer;
    color: white;
}

.submissions {
    max-width: 700px;
    margin: 0 auto;
}

.submission-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem;
    border-bottom: 1px solid #ddd;
}

.submission-row p {
    background-color: #ff9e4a;
    color: white;
    padding: 0.5rem;
    border-radius: 4px;
    margin: 0;
}

.checkbox {
    margin-right: 1rem;
}
