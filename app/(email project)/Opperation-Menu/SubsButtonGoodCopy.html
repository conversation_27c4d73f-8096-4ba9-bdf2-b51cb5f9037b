<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trades Dashboard</title>
    <style>
        body {
            font-family: 'Roboto', sans-serif;
            background-color: #f0f2f5;
            margin: 0;
            padding: 20px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        .container {
            width: 80%;
            background: #fff;
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            margin: 20px auto;
            position: relative;
        }

        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .view-toggle {
            display: flex;
            justify-content: flex-start;
            align-items: center;
        }

        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
            margin-right: 10px;
        }

        .switch input {
            opacity: 0;
            width: 0;
            height: 0;
        }

        .slider {
            position: absolute;
            cursor: pointer;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }

        .slider:before {
            position: absolute;
            content: "";
            height: 26px;
            width: 26px;
            left: 4px;
            bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }

        input:checked + .slider {
            background-color: #ff8c00;
        }

        input:checked + .slider:before {
            transform: translateX(26px);
        }

        #viewLabel {
            font-size: 1.2em;
            color: #333;
        }

        .header-right {
            display: flex;
            gap: 10px;
        }

        .add-trade, .delete-trade {
            background: #ff8c00;
            color: white;
            font-size: 2em;
            text-decoration: none;
            padding: 0;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: inline-block;
            text-align: center;
            line-height: 60px; /* Ensure the text is vertically centered */
        }

        .delete-trade {
            background: #e74c3c;
        }

        #tableholder {
            width: 100%;
            overflow-y: auto;
            margin-top: 20px;
        }

        .traderow {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 20px;
            margin: 0;
            border: 1px solid #000;
            padding: 20px;
            padding-top: 30px;
            padding-bottom: 30px;
            border-radius: 10px;
            background-color: #f7f7f7;
            height: auto;
        }

        .traderow.grid-view a {
            padding: 20px 40px;
            border-radius: 5px;
            background-color: #ffcc80;
            cursor: pointer;
            transition: opacity 0.3s, transform 0.3s;
            opacity: 0.55;
            text-decoration: none;
            color: black;
            font-size: 2em;
        }

        .traderow.grid-view a:hover {
            transform: translateY(-5px);
            opacity: 1;
        }

        .traderow.list-view {
            flex-direction: column;
            align-items: center;
        }

        .traderow.list-view a {
            width: 80%;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 30px;
            border: 1px solid #ddd;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            background-color: #ffcc80;
            border-radius: 8px;
            transition: background 0.3s, box-shadow 0.3s;
            font-size: 1.5em;
            text-decoration: none; /* Remove underline */
        }

        .traderow.list-view a:hover {
            background-color: #ff8c00;
            color: white;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        }

        .traderow.list-view a span {
            font-size: 0.9em;
            color: #555;
        }

        .hidden {
            display: none;
        }

        .deleting a {
            border: 2px solid red;
            animation: shake 0.2s linear infinite;
        }

        @keyframes shake {
            0% { transform: translate(1px, 1px) rotate(0deg); }
            10% { transform: translate(-1px, -2px) rotate(-1deg); }
            20% { transform: translate(-3px, 0px) rotate(1deg); }
            30% { transform: translate(3px, 2px) rotate(0deg); }
            40% { transform: translate(1px, -1px) rotate(1deg); }
            50% { transform: translate(-1px, 2px) rotate(-1deg); }
            60% { transform: translate(-3px, 1px) rotate(0deg); }
            70% { transform: translate(3px, 1px) rotate(-1deg); }
            80% { transform: translate(-1px, -1px) rotate(1deg); }
            90% { transform: translate(1px, 2px) rotate(0deg); }
            100% { transform: translate(1px, -2px) rotate(-1deg); }
        }

        .vetting-results:hover {
            background-color: #e67e22;
            box-shadow: 0 6px 14px rgba(0, 0, 0, 0.2);
        }

        #vettingResultsButton {
            background-color: #ff8c00;
            color: white;
            font-size: 1.5em;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 5px;
            display: block;
            margin: 20px auto 0 auto; /* Center horizontally */
            text-align: center;
            max-width: 200px; /* Adjust the width as needed */
        }

        #header {
            text-align: center;
            font-size: 2em;
            margin-bottom: 20px;
        }

    </style>
</head>
<body>
    <div id="header"></div>
    <div class="container">
        <header>
            <div class="header-left">
                <div class="view-toggle">
                    <label class="switch">
                        <input type="checkbox" id="viewToggle">
                        <span class="slider round"></span>
                    </label>
                    <span id="viewLabel">Grid View</span>
                </div>
            </div>
            <div class="header-right">
                <button class="add-trade" id="addTrade">+</button>
                <button class="delete-trade" id="deleteToggle">-</button>
            </div>
        </header>
        <div id="tableholder">
            <div class="traderow grid-view" id="tradeContainer"></div>
        </div>
    </div>
    <a href="http://subhubtech.com/vetting-dashboard" class="vetting-results" id="vettingResultsButton">Vetting Results</a>

    <script>
        var trades = [];
        const container = document.getElementById("tradeContainer");

        var name = window.sessionStorage.getItem("project_name");
        var address = window.sessionStorage.getItem("project_address");
        console.log("Address from session storage: ", address);  // Log the address from session storage

        document.getElementById("header").textContent = `Trades for project "${name}"`;

        const requrl = sessionStorage.getItem("baseurl") + "/getuser?username=" + sessionStorage.getItem("username");

        headers = {"ngrok-skip-browser-warning": "yes"};
        fetch(requrl, {headers: headers}).then((result) => {
            result.text().then((textresult) => {
                sessionStorage.setItem("userdata", textresult);
                const user = JSON.parse(textresult);

                for (let project of user.projects) {
                    if (project.name == name) {
                        console.log("Project data: ", project); // Log the project data to check available attributes
                        
                        if (project.address) {
                            address = project.address;  // Update the address from the fetched data
                        } else if (project.location) {  // Check if the address is stored under a different name
                            address = project.location;
                        } else {
                            address = "default address";  // Fallback address
                        }
                        
                        window.sessionStorage.setItem("project_address", address);
                        console.log("Updated address from user data: ", address);  // Log the updated address
                        trades = project.trades;
                        loadTrades();
                        break;
                    }
                }
            })
        });

        function loadTrades() {
            for (let trade of trades) {
                if (trade.deletedStatus == "deleted") {
                    continue;
                }

                let trade_button = document.createElement("a");
                trade_button.textContent = trade.trade;
                trade_button.setAttribute("data-trade-name", trade.trade);

                if (trade.dateCreated) {
                    let dateSpan = document.createElement("span");
                    dateSpan.textContent = `Created on: ${trade.dateCreated}`;
                    trade_button.appendChild(dateSpan);
                }

                trade_button.addEventListener("click", () => {
                    if (document.body.classList.contains("deleting")) {
                        deleteTrade(trade.trade);
                    } else {
                        window.sessionStorage.setItem("trade", trade.trade);
                        window.sessionStorage.setItem("dataframe", trade.dataframe);
                        window.sessionStorage.setItem("currentaction", "vetting");

                        window.location.href = "/subs-results";
                    }
                });

                container.appendChild(trade_button);
            }
        }

        document.getElementById('viewToggle').addEventListener('change', function () {
            if (this.checked) {
                container.classList.add('list-view');
                container.classList.remove('grid-view');
                document.getElementById('viewLabel').textContent = 'List View';
            } else {
                container.classList.add('grid-view');
                container.classList.remove('list-view');
                document.getElementById('viewLabel').textContent = 'Grid View';
            }
        });

        document.getElementById('deleteToggle').addEventListener('click', function() {
            document.body.classList.toggle("deleting");
        });

        document.getElementById('addTrade').addEventListener('click', function() {
            console.log("Navigating to add-trades with address: ", address);  // Log the address before navigating
            window.sessionStorage.setItem("project_address", address);
            window.location.href = "/add-trades";
        });

        function deleteTrade(trade_name) {
            const username = window.sessionStorage.getItem("username");
            const project_name = window.sessionStorage.getItem("project_name");
            const requrl = "https://subhub.ngrok.dev/deletetrade"; // Make sure this endpoint exists and is implemented
            fetch(requrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    username: username,
                    project_name: project_name,
                    trade_name: trade_name
                })
            }).then((response) => {
                if (response.ok) {
                    // Find the trade element and remove it
                    const tradeElement = container.querySelector(`[data-trade-name="${trade_name}"]`);
                    if (tradeElement) {
                        tradeElement.remove();
                    }
                }
            });
        }
    </script>
</body>
</html>