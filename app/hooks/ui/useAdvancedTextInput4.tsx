'use client'
import { useState, useRef } from 'react';

/**
 * Represents a reference string that holds the current value.
 */
type referenceString = {
    /** The current value of the reference string. */
    current: string;
};


/**
 * Text Input Component
 * 
 * Generates a Text Input React Component that allows the user to input text.
 * 
 * @example
 * ```jsx
 * default function Home() {
 * let prompt = "Enter Your AI Prompt.."
 * //propmpt is the placeholder for the text input
 * const [articleName, BasicArticleName] = useAdvancedTextInput3({ prompt: prompt })
 * 
 * function handler(){
 * console.log(articleName()) // this is the information from the text input
 * }
 * 
 * return(
      * <BasicArticleName />)
      * 
 }
 *```
 */


export default function useAdvancedTextInput4({ prompt, validationRegex, validationMessage, note, hideText = false }: { 
    prompt: string, 
    validationRegex?: RegExp, 
    validationMessage?: string, 
    note?: string, 
    hideText?: boolean 
}): [()=> boolean,() => null | undefined, React.FC] {
    const inputRef = useRef<HTMLInputElement>(null); // Create a ref to manage input focus
    const [isValid, setIsValid] = useState(true);
    const [showPassword, setShowPassword] = useState(false);
    const [input, setInput] = useState(() => hideText ? 'password' : 'text');

    function togglePasswordVisibility(e: React.MouseEvent) {
        e.preventDefault(); // Prevent button click from affecting form
        
        setShowPassword(!showPassword);
        setInput(showPassword ? 'password' : 'text');
        
        // No need for setTimeout or manual value setting
        // Just toggle the input type while maintaining the value
    }

    function getValidatedValue(){

        let validState = isValid
        if (validationRegex) {
            console.log(validationRegex)
            console.log('hit')
            console.log(inputRef.current?.value)
            validState = validationRegex.test(inputRef.current?.value as string)
            setIsValid(validState)
            console.log(isValid)
            console.log(validState)
        }
        if (validState) {
            console.log('hit')

            return inputRef.current?.value
        }
        else{
            console.log(validState)
            console.log('hit')
            inputRef.current.value = ''
            throw new Error(validationMessage)
        }
    }

    function getValue(){
        console.log('inside getValue function', inputRef.current?.value)
        return inputRef.current?.value
    }

   console.log('render')
   

    function BasicTextInput() {
        if (isValid) return (
            <div className="flex items-center justify-center relative w-full max-w-xs">
                <input
                    ref={inputRef}
                    type={input}
                    placeholder={prompt}
                    className="input input-bordered w-full pr-10" // Added padding-right for the icon
                    defaultValue={inputRef.current?.value} // Maintain value
                />
                {hideText && (
                    <button 
                        type="button"
                        onMouseDown={(e) => e.preventDefault()} // Prevent focus loss
                        onClick={togglePasswordVisibility}
                        className="absolute right-3 top-1/2 transform -translate-y-1/2"
                    >
                        {showPassword ? (
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
                                <path strokeLinecap="round" strokeLinejoin="round" d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.242 4.242L9.88 9.88" />
                            </svg>
                        ) : (
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
                                <path strokeLinecap="round" strokeLinejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" />
                                <path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                        )}
                    </button>
                )}
            </div>
        );

        return (
            <div className="flex flex-col items-center justify-center max-w-xs text-wrap">
                <div className="relative w-full">
                    <input
                        ref={inputRef}
                        type={input}
                        placeholder={validationMessage}
                        className="input input-bordered w-full pr-10 placeholder-red-500"
                        defaultValue={inputRef.current?.value} // Maintain value
                    />
                    {hideText && (
                        <button 
                            type="button"
                            onMouseDown={(e) => e.preventDefault()} // Prevent focus loss
                            onClick={togglePasswordVisibility}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2"
                        >
                            {showPassword ? (
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
                                    <path strokeLinecap="round" strokeLinejoin="round" d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.242 4.242L9.88 9.88" />
                                </svg>
                            ) : (
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
                                    <path strokeLinecap="round" strokeLinejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" />
                                    <path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                            )}
                        </button>
                    )}
                </div>
                <p className='text-red-500'>{note}</p>
            </div>
        );
    }

    return [getValidatedValue, getValue, BasicTextInput];
}
