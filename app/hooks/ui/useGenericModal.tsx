import { useState } from 'react';
import useModal from "@/app/components/ui/Modal4";
import type { JSX } from "react";


export function useGenericModal(): [JSX.Element, (text: string) => void, () => void, React.RefObject<HTMLDialogElement>] {
  const [GenericModal, genericRef] = useModal();
  const [genericModalText, setGenericModalText] = useState('');

  const GenericModalComponent = (
    <GenericModal>
      <div className="p-4 prose">
        <h2>{genericModalText}</h2>
        <div className="flex justify-end gap-2 mt-4">
          <button className="btn" onClick={() => genericRef.current?.close()}>
            Close
          </button>
        </div>
      </div>
    </GenericModal>
  );

  const showGenericModal = (text: string) => {
    setGenericModalText(text);
    genericRef.current?.showModal();
  };

  const closeGenericModal = () => {
    genericRef.current?.close();
  };

  return [
    GenericModalComponent,
    showGenericModal,
    closeGenericModal,
    genericRef
  ]
}