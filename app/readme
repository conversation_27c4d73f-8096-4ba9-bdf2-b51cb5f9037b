## Table of Contents

[TOC]


[text](../config.ts)

## Admin dash
Edit the content type and category type in next_js_portfolio mongo settings
and useUserContentSettings to update the select options

## Usefull Programs
to get the current users ip use:        
```js
import { getIP } from "@/app/services/getIP";

let currentIP = await getIP()
```
to get the current users ip by importing 
## Regularly Used Files
[ai-transcription-component experimental](components/ai-transcriptions/AudioTranscriptionExperimental.tsx)
[ai-Transcription](<(main site)/(Landing Pages)/Web-Apps/ai-translation/page.tsx>)
[aiarticle generator](<(main site)/(Landing Pages)/ai-article-generator/AiArticleGenerator.tsx>)
[loading wrapper](hooks/ui/Loading3.tsx)


[dbCurry](db/dbCurry.ts)
[testScript](scripts/malcmindLogin.test.sh)
[curryTest](../__tests__/curryAPI.test.jsx)
[signin.spec Test](../tests/signin.spec.ts)
[package.json](../package.json)
[Mongo](<(main site)/Components/db_services/mongo.tsx>)
[DEBUG](utils/debug.tsx)   'Import {debug} from '@/app/utils/debug';'
[text](db/dbRepository.ts)

## Pages
- [ai translation](<(main site)/(Landing Pages)/Web-Apps/ai-translation/page.tsx>)

## Components
- [ai article generator](<(main site)/(Landing Pages)/ai-article-generator/AiArticleGenerator.tsx>)
- [malcmindLogin](malcmind-login/page.tsx)
  [aitranscription](components/ai-transcriptions/AudioTranscription.tsx)
  [signin](components/auth/SignIn.tsx)
## TODO 

## Hooks

- [Add DB access to Malcmind login](malcmind-login/page.tsx)
- [use loading](<(main site)/Components/ui/Loading2.tsx>)

## Page URLS
[malcmindlogin](malcmind-login/page.tsx)

## Features
### CSS Test
[CSS](<(main site)/(Test pages)/CSS/page.tsx>)
### Signin
[malcmindLogin](malcmind-login/page.tsx)
[signin.spec](../tests/signin.spec.ts)
[article generator](<(main site)/(Landing Pages)/ai-article-generator/AiArticleGenerator.tsx>)

## Blogging Files


This is the 'Edit Section' from my ai results ui

[ContentRenderUniversal2.tsx](<(main site)/(Landing Pages)/AdminDash/ContentRenderUniveseral2.tsx>)

## @app/scripts - my test scripts
[login test script](scripts/malcmindLogin.test.sh)
## @app/actions - serverside logic
## @app/services - Client-side or shared logic that call api routes
## @app/api2 - version 2 of my api
[emailreset](api2/reset-email/route.js)
## @app/db
### /utils
'import {transformResults2} from "@/app/(main site)/Components/db_services/mongo"
[text](<(main site)/Components/db_services/mongo.tsx>) 

### dbCurry
import {authenticateUser} from '@/app/db/dbCurry'
[dbCurry](db/dbCurry.ts)



