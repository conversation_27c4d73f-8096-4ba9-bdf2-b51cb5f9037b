'use client'

import { useState, useRef, useEffect } from "react";
import useAudioRecorder from "@/app/(main site)/(Landing Pages)/Web-Apps/ai-translation/useAudioRecorder";
import { uploadAudio, getAudio, getAllAudioRecordigns, deleteAudio } from "@/app/(main site)/Components/db_services/mongo"
import { groqAudio, transcribeBlob } from "@/app/services/groqAudiotoTextService";
import { GridFSFile } from "mongodb";
import Image from "next/image";
import { Modal3 } from "@/app/components/ui/Modal3"
import useAdvancedTextInput3 from '@/app/hooks/ui/useAdvancedTextInput3'
import { CloseButton } from '@/app/components/ui/CloseButton'
import { useBottomNav } from "@/app/hooks/ui/useBottomNav"
import Link from "next/link";
import useModal from "@/app/components/ui/Modal4";
import { useCallbackModal } from "@/app/components/ui/Modal4.CallBackModal";
import { useSearchParams } from 'next/navigation';
import { deleteUser, isCreditsRemaining } from "@/app/db/dbCurry";
import { useRouter, usePathname } from 'next/navigation'
import AIVoiceAppChatBox from '@/app/components/ai-chat/AIChatBox.VoiceAPP'
import AIVoiceAppChatBoxSavedRecordingView from '@/app/components/ai-chat/AIChatBox.VoiceAPPSavedRecordingView'

import { getAccessToken } from "@/app/components/ai-transcriptions/acknowledge"
import { digitalServiceDetector } from "@/app/utils/digitalServiceDetector"
import useDevModeToggle from "@/app/components/ai-transcriptions/internalComponents/DevModeToggle";

export const dynamic = 'force-dynamic'

//delete account url http://localhost:3000/Web-Apps/ai-translation?display=delete-account


const AudioTranscription = () => {
  const router = useRouter()
  const pathname = usePathname()

  const searchParams = useSearchParams();
  const displayParam = searchParams.get('display')

  const modalRef = useRef<HTMLDialogElement>(null)
  const nameRef = useRef<HTMLDialogElement>(null)

  const [audioSrc, setAudioSrc] = useState<Blob | null | string>("");
  const [allRecordings, setAllRecordings] = useState<GridFSFile[]>([]);
  const [displayedTranslations, setDisplayedTranslations] = useState<{ [key: string]: string | void }>({});
  // const [userId, resetUserId] = useUserId();
  const [userId, resetUserId] = useState(() => {
    if (typeof window !== 'undefined' && window.localStorage) {
      console.log(localStorage)
      return localStorage.getItem('userId');// a mod to lazy initialize the userid instead of using clerk
    }
    return null;
  });
  const { audioBlob, isRecording, startRecording, stopRecording, resetAudioBlob } = useAudioRecorder();
  const [articleName, BasicArticleName] = useAdvancedTextInput3({ prompt: "Enter a Recording Name" })
  const [realtimeTranscription, setRealtimeTranscription] = useState<string | null>(null);
  const [activeNavButton, BottomNavComponent] = useBottomNav(
    displayParam === 'delete-account' ? 'settings' : undefined
  );
  const [DeleteSuccessModal, deleteSuccessRef] = useModal();
  const [MustLoginModal, mustLoginRef] = useModal();
  const [DeleteConfirmModal, deleteConfirmRef] = useModal();
  const [DeleteFailedModal, deleteFailedRef] = useModal();

  const [getUserEmail, BasicUserEmail] = useAdvancedTextInput3({ prompt: "Enter Your Email" })
  const [getUserPassword, BasicPassword] = useAdvancedTextInput3({
    prompt: "Password",
  });
  const [GenericModal, genericRef] = useModal();
  const [genericModalText, setGenericModalText] = useState('')
  const [GenericCallBackModal, showCallbackModal, closeCallbackModal, setModalCallback] = useCallbackModal();

  const [recordingTime, setRecordingTime] = useState(0);
  const [toggled, DevToggleButton] = useDevModeToggle();
  const timerInterval = useRef<NodeJS.Timeout | null>(null);

  console.log(userId)
  console.log(toggled)

  const startTimer = () => {
    setRecordingTime(0);
    timerInterval.current = setInterval(() => {
      setRecordingTime(prev => prev + 1);
    }, 1000);
  };

  const stopTimer = () => {
    if (timerInterval.current) {
      clearInterval(timerInterval.current);
      timerInterval.current = null;
    }
    setRecordingTime(0);
  };

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const genericModal = (
    <GenericModal>
      <div className="p-6 prose bg-gray-900 rounded-lg text-white">
        <h2 className="text-2xl font-bold text-blue-400 mb-4">{genericModalText}</h2>
        <div className="flex justify-end gap-3 mt-6">
          <button className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors shadow-md" onClick={() => genericRef.current?.close()}>
            Close
          </button>
        </div>
      </div>
    </GenericModal>
  );

  const deleteSuccessModal = (
    <DeleteSuccessModal>
      <div className="p-6 prose bg-gray-900 rounded-lg text-white">
        <h2 className="text-2xl font-bold text-green-400 mb-4">Delete Successful!</h2>
        <div className="flex justify-end gap-3 mt-6">
          <button className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors shadow-md" onClick={() => deleteSuccessRef.current?.close()}>
            Close
          </button>
        </div>
      </div>
    </DeleteSuccessModal>
  );

  const mustLoginModal = (
    <MustLoginModal>
      <div className="p-6 prose bg-gray-900 rounded-lg text-white">
        <h2 className="text-2xl font-bold text-yellow-400 mb-4">You Must Login To Delete Your Account</h2>
        <div className="flex justify-end gap-3 mt-6">
          <button className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors shadow-md" onClick={() => mustLoginRef.current?.close()}>
            Close
          </button>
        </div>
      </div>
    </MustLoginModal>
  );


  const handleDeleteAccount = () => {
    if (userId != '' && userId != null) {
      deleteConfirmRef.current?.showModal();
    }
    else {
      mustLoginRef.current?.showModal()
    }
  }


  const handleConfirmDelete = async () => {
    try {
      console.log(getUserEmail(), getUserPassword())
      const result = await deleteUser(getUserEmail(), getUserPassword());
      console.log(result)

      if (result == 'you cannot delete a test account') {
        setGenericModalText('You Cannot Delete a Test Account')
        genericRef.current?.showModal()
      }

      else if (result.deletedCount == 1) {
        deleteConfirmRef.current?.close();
        deleteSuccessRef.current?.showModal();
        resetUserId('')
        localStorage.clear()
        setAllRecordings([])
        setAudioSrc('')
        resetAudioBlob()
        setRealtimeTranscription(null)
      } else {
        deleteFailedRef.current?.showModal(); // Show failed modal
      }
    } catch (error) {
      console.error("Error deleting account:", error);
      deleteFailedRef.current?.showModal(); // Show failed modal on error
    }
  };

  const deleteConfirmModal = (
    <DeleteConfirmModal>
      <div className="p-6 prose bg-gray-900 rounded-lg ">
        <h2 className="text-2xl font-bold text-red-400 mb-4">Are You Sure You Want To Delete Your Account?</h2>
        <p className="text-red-400 font-medium">This action cannot be undone.</p>
        <div className="space-y-4 mt-6">
          <BasicUserEmail />
          <BasicPassword />
          <div className="flex justify-end gap-3 mt-6">
            <button
              className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors shadow-md"
              onClick={handleConfirmDelete}
            >
              Confirm Delete
            </button>
            <button
              className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors shadow-md"
              onClick={() => deleteConfirmRef.current?.close()}
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </DeleteConfirmModal>
  );

  const deleteFailedModal = (
    <DeleteFailedModal>
      <div className="p-6 prose bg-gray-900 rounded-lg text-white">
        <h2 className="text-2xl font-bold text-red-400 mb-4">Failed to Delete Account</h2>
        <p className="text-red-400 font-medium">Invalid credentials or account not found.</p>
        <div className="flex justify-end gap-3 mt-6">
          <button
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors shadow-md"
            onClick={() => deleteFailedRef.current?.close()}
          >
            Try Again
          </button>
        </div>
      </div>
    </DeleteFailedModal>
  );





  console.log("this is the userid", userId)
  console.log(realtimeTranscription)


  async function handleRealTimeTranscription() {
    console.log(userId)
    let trialPeriod = await isCreditsRemaining(userId)
    let didUserPay: boolean | null = null
    if (trialPeriod == false && didUserPay == false) {
      setGenericModalText('You Need to Subscribe to Transcribe Messages')
      genericRef.current?.showModal()
    }
    if (trialPeriod == false) {
      let testToken = await getAccessToken();
      console.log('test access token****', testToken)
      //we only care about payments if its on a mobile device, computer can use for free
      let isOnMobile = digitalServiceDetector()
      // didUserPay = !isOnMobile ? false : await handlePayment()
      let simulatedSubscription = toggled as boolean // set to true by default unless the dev hits the toggle button in settings
      console.log(simulatedSubscription, toggled)
      didUserPay = !isOnMobile ? simulatedSubscription : await didUserSubscribe()

    }

    console.log(trialPeriod)
    let isAllowedtoTranscribe = trialPeriod || didUserPay
    console.log(isAllowedtoTranscribe)
    if (isAllowedtoTranscribe) {
      const transcribedBlob = await transcribeBlob(audioBlob)
      setRealtimeTranscription(transcribedBlob)
    }
    if (!isAllowedtoTranscribe) {

      //setGenericModalText('You Need to Subscribe to Transcribe Messages')
      //genericRef.current?.showModal()

      // const [GenericCallBackModal, showCallbackModal, closeCallbackModal, setModalCallback] = useCallbackModal();

      showCallbackModal(
        'Transcription Credits',
        'You Need to Subscribe to Transcribe Messages. You get 3 free transcriptions per day. Please wait 24 hours or subscribe to continue.',
        () => {
          handlePayment()
        },
      )
    }
  }

  function handleRecording() {
    if (userId != '' && userId != null) {
      startRecording();
      startTimer(); // Start the timer when recording starts
    }
    else {
      modalRef.current?.showModal()
    }
  }

  // Update your existing stopRecording logic
  const handleStopRecording = () => {
    stopRecording();
    stopTimer(); // Stop the timer when recording stops
  };

  const handleGetAllAudioRecordigns = async () => {
    let recordings = await getAllAudioRecordigns(userId);
    recordings = JSON.stringify(recordings)   //stringify recordings
    recordings = JSON.parse(recordings)
    setAllRecordings(recordings);
    console.log(recordings)
  };

  async function handleAudiotoText(audioID: string) {
    console.log('hit promo')
    const translatedAudio = await groqAudio(audioID);
    setDisplayedTranslations((prev) => ({ ...prev, [audioID]: translatedAudio })); // Store translation by audio ID

  }
  console.log(displayedTranslations)


  const fetchAudio = async () => {
    console.log('cool')
    //const response = await fetch(`http://localhost:3000/Web-Apps/ai-translation/audioAPI?id=67a5ae5c675b2c59881618a4`);
    const response = await getAudio('67a5ae5c675b2c59881618a4');

    // if (!response.ok) {
    //   alert("Failed to fetch audio");
    //   return;
    // }

    // const blob = await response.blob();
    // const url = URL.createObjectURL(blob);
    const url = URL.createObjectURL(response);

    console.log(url)
    setAudioSrc(url);
  };


  const downloadAudio = () => {
    if (!audioBlob) return;
    const url = URL.createObjectURL(audioBlob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "recorded_audio.wav";
    a.click();
  };

  async function handleDeleteAudio(audioID: string) {
    await deleteAudio(audioID);
    handleGetAllAudioRecordigns();
  }

  async function handleUpload() {
    //nigga
    nameRef.current?.close()
    if (!audioBlob) return alert("Please select an audio file");
    let theAudioName = articleName()
    const formData = new FormData();
    formData.append("audio", audioBlob, "cool.wav");
    formData.append("userId", userId);
    formData.append("audioName", theAudioName);


    const result = await uploadAudio(formData);
    console.log(result)
    await new Promise(resolve => setTimeout(resolve, 3000));

    handleGetAllAudioRecordigns()
    if (result.error) {
      alert(result.error);
    } else {

    }

  }
  const handleSaveAudio = async () => {
    nameRef.current?.showModal()

    // if (!audioBlob) return alert("Please select an audio file");

    // const formData = new FormData();
    // formData.append("audio", audioBlob, "cool.wav");
    // formData.append("userId", userId);


    // const result = await uploadAudio(formData);

    // if (result.error) {
    //   alert(result.error);
    // } else {
    // }
  };

  useEffect(() => {
    const toolbarElement = document.querySelector('.MuiContainer-root');

    if (toolbarElement) {
      toolbarElement.style.display = 'none';
    }

    if (userId != '') handleGetAllAudioRecordigns();
  }, [userId]);

  console.log(userId)

  digitalServiceDetector()

  let accessToken: string | null = null

  async function didUserSubscribe() {
    const service = await window.getDigitalGoodsService('https://play.google.com/billing');
    console.log('Google Play Billing service is available')
    let purchases = await service.listPurchases();
    console.log("these are the purchases:", purchases)
    if (purchases.length > 0) {
      return true
    } else {
      return false
    };
  }


  async function handlePayment() {
    console.log('hit promo')
    try {
      //const [digitalGoods] = await navigator.getDigitalGoodsService('play');

      const service = await window.getDigitalGoodsService('https://play.google.com/billing');
      console.log('Google Play Billing service is available')

      let purchases = await service.listPurchases();
      console.log("these are the purchases:", purchases)
      let didCustomerPurchase = null
      if (purchases.length > 0) {
        didCustomerPurchase = true
        console.log('did customer purchase?', didCustomerPurchase)

        return true
      } else { didCustomerPurchase = false };
      console.log('did customer purchase?', didCustomerPurchase)

      // const itemDetails = await service.getDetails(['1','3']);


      const itemDetails = await service.getDetails(['1', '2', '3', '4', '5', '11']);
      console.log('these are the item details:')
      for (const item of itemDetails) {
        // Display item information to user
        console.log(item.title, item.description, item.price, item.itemType, item.subscriptionPeriod, item.introductoryPrice, item.introductoryPricePeriod, item.freeTrialPeriod, item.sku, item.itemId);
        console.log('full object', item)
      }
      console.log("end of console.logs for this segment")

      //   [
      //     {
      //         "itemId": "3",
      //         "purchaseToken": "ahfhmlgflmgclipffnaafkem.AO-J1Oxon0pZNjt8BF8Jw4-FxMWduE7geFVVYcbY5CJ8vu08qf63bHMA1Mn-hBJTB-JTX5SMrCDA9D6J2Of0sAKinxUupla6MQ"
      //     }
      // ]
      // const paymentMethodData = [
      //   {
      //     supportedMethods: 'https://play.google.com/billing',
      //     data: {
      //       sku: "1"
      //     },
      //   },
      // ];
      const paymentMethodData = [
        {
          supportedMethods: 'https://play.google.com/billing',
          data: {
            sku: '11',
            // type: 'SUBS'  // Specify that this is a subscription
          },
        },
      ];
      const request = new PaymentRequest(paymentMethodData);
      const paymentResponse = await request.show();

      const { purchaseToken } = paymentResponse.details;
      console.log('this is the purchase response', purchaseToken)
      let paymentComplete = null;
      accessToken = await getAccessToken();
      console.log('access token', accessToken)
      let subscriptionResult = await acknowledgeSubscription('com.malcmind.twa', '11', purchaseToken);
      if (subscriptionResult) {
        paymentComplete = await paymentResponse.complete('success');
        console.log('payment complete', paymentComplete)
        return true
        // Let user know their purchase transaction has successfully completed and been verified 
      } else {
        paymentComplete = await paymentResponse.complete('fail');
        console.log('payment complete', paymentComplete)
        return false
        // Let user know their purchase transaction failed to verify 
      }

    } catch (error) {
      console.log('Error Processing Payment')
      return false
    }
  }

  async function acknowledgeSubscription(packageName: string, subscriptionId: string, token: string) {
    const url = `https://androidpublisher.googleapis.com/androidpublisher/v3/applications/${packageName}/purchases/subscriptions/${subscriptionId}/tokens/${token}:acknowledge`;

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({}) // Optional: { developerPayload: 'yourPayload' }
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Error ${response.status}: ${errorText}`);
      }

      // Some responses return empty object {}, so we handle that:
      const data = await response.text();
      console.log('Acknowledged successfully:', data || '{}');
      return true;
    } catch (err) {
      console.error('Failed to acknowledge subscription:', err.message);
      return false;
    }
  }

  // async function checkSubscriptionStatus() {
  //   try {
  //     const service = await window.getDigitalGoodsService('https://play.google.com/billing');
  //     const subscriptionInfo = await service.getSubscriptionInfo('your_subscription_id');

  //     if (subscriptionInfo.state === 'ACTIVE') {
  //       console.log('Subscription is active');
  //       return true;
  //     } else {
  //       console.log('Subscription is not active');
  //       return false;
  //     }
  //   } catch (error) {
  //     console.error('Error checking subscription:', error);
  //     return false;
  //   }
  // }


  return (
    <>
      <div className="flex flex-col justify-center items-center bg-gradient-to-b from-gray-900 to-black min-h-screen w-full px-4 pb-20">
        {deleteSuccessModal}
        {mustLoginModal}
        {deleteConfirmModal}
        {deleteFailedModal}
        {genericModal}
        {GenericCallBackModal}
        <Modal3 ref={nameRef} modalTitle="Enter A Name for the Recording" hideOutsideButton={true} buttonText="not used">
          <div className="p-4 bg-gray-800 rounded-lg">
            <BasicArticleName />
            <button className="mt-4 w-full px-5 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors shadow-lg" onClick={handleUpload}>Save Recording</button>
          </div>
        </Modal3>

        <Modal3 ref={modalRef} modalTitle="You Must Login To Record" hideOutsideButton={true} buttonText="not used">
          <div className="p-4 bg-gray-800 rounded-lg">
            <Link href="/malcmind-login?app=audiorecorder">
              <button className="w-full px-5 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors shadow-lg">Sign In</button>
            </Link>
          </div>
        </Modal3>

        {/* <p className="text-red-600">This is an Experimental Dev Account</p> */}
        <div className="">
          <Image
            src="/voiceTranscriptionIcon-512x512.png"
            alt="Logo"
            width={512}
            height={512}
          // className="w-full h-full object-contain drop-shadow-lg"
          />
        </div>

        {activeNavButton == "record" &&
          <>
            {realtimeTranscription &&
              <div className="w-full max-w-md mb-8 bg-gray-800 rounded-xl shadow-xl overflow-hidden">
                <AIVoiceAppChatBox recordedConversation={realtimeTranscription} />
              </div>
            }

            {/* Recording Timer Display */}
            {isRecording && (
              <div className="text-white text-xl font-mono mb-4 bg-gray-800 px-4 py-2 rounded-lg">
                {formatTime(recordingTime)}
              </div>
            )}

            <button
              className={`${isRecording
                ? "bg-red-600 hover:bg-red-700"
                : "bg-blue-600 hover:bg-blue-700"} 
                px-6 py-3 rounded-full text-white font-medium shadow-lg transition-all transform hover:scale-105 mb-6 flex items-center`}
              onClick={isRecording ? handleStopRecording : handleRecording}
            >
              <span className={`${isRecording ? "animate-pulse" : ""} flex items-center`}>
                {isRecording ? (
                  <>
                    <span className="inline-block w-3 h-3 bg-red-400 rounded-full mr-2"></span>
                    Stop Recording
                  </>
                ) : (
                  <>
                    <span className="inline-block w-3 h-3 bg-white rounded-full mr-2"></span>
                    Start Recording
                  </>
                )}
              </span>
            </button>
          </>
        }

        {activeNavButton == "record" && audioBlob && (
          <div className="flex flex-col justify-center items-center w-full max-w-md mb-20">
            <div className="w-full bg-gray-800 rounded-xl p-4 shadow-lg mb-4">
              <audio
                controls
                src={URL.createObjectURL(audioBlob)}
                className="w-full focus:outline-none"
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3 w-full">
              <button
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-3 rounded-lg transition-colors shadow-md flex items-center justify-center"
                onClick={downloadAudio}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L10 11.586l2.293-2.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
                Download
              </button>
              <button
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-3 rounded-lg transition-colors shadow-md flex items-center justify-center"
                onClick={handleSaveAudio}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path d="M7.707 10.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V6h5a2 2 0 012 2v7a2 2 0 01-2 2H4a2 2 0 01-2-2V8a2 2 0 012-2h5v5.586l-1.293-1.293zM9 4a1 1 0 012 0v2H9V4z" />
                </svg>
                Save
              </button>
              <button
                className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-3 rounded-lg transition-colors shadow-md flex items-center justify-center"
                onClick={handleRealTimeTranscription}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 13V5a2 2 0 00-2-2H4a2 2 0 00-2 2v8a2 2 0 002 2h3l3 3 3-3h3a2 2 0 002-2zM5 7a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H6z" clipRule="evenodd" />
                </svg>
                Transcribe
              </button>
            </div>
          </div>
        )}

        {activeNavButton == "settings" &&
          <div className="w-full max-w-md flex flex-col items-center space-y-4 mt-6">
            {!userId &&
              <Link href="/malcmind-login?app=audiorecorder" className="w-full">
                <button className="w-full px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors shadow-lg">
                  Sign In
                </button>
              </Link>
            }

            {userId &&
              <button
                className="w-full px-6 py-3 bg-gray-700 hover:bg-gray-800 text-white font-medium rounded-lg transition-colors shadow-lg"
                onClick={() => {
                  resetUserId('')
                  localStorage.clear()
                  setAllRecordings([])
                  setAudioSrc('')
                  resetAudioBlob()
                  setRealtimeTranscription(null)
                  router.push(pathname)
                }}
              >
                Sign Out
              </button>
            }

            <Link href="/Web-Apps/ai-translation/privacy-policy" className="w-full">
              <button className="w-full px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white font-medium rounded-lg transition-colors shadow-lg">
                Privacy Policy
              </button>
            </Link>
            {/* <button
              className="w-full px-6 py-3 bg-blue-700 hover:bg-green-700 text-white font-medium rounded-lg transition-colors shadow-lg"
            >
              Enter Dev Mode
            </button> */}
            {userId == "690d75369547d4dbacd72cfe" && <DevToggleButton />}
            <button
              className="w-full px-6 py-3 bg-red-600 hover:bg-red-700 text-white font-medium rounded-lg transition-colors shadow-lg"
              onClick={handleDeleteAccount}
            >
              Delete Account
            </button>
          </div>
        }

        {activeNavButton == "record" && audioSrc && (
          <div className="w-full max-w-md bg-gray-800 rounded-xl p-4 shadow-lg mb-4">
            <audio
              controls
              className="w-full focus:outline-none"
            >
              <source src={audioSrc} type="audio/mpeg" />
            </audio>
          </div>
        )}

        {activeNavButton == "saved recordings" && allRecordings.length > 0 && (
          <div className="w-full max-w-md pb-20">
            <h2 className="text-xl font-bold text-white mb-4 text-center">Saved Recordings</h2>
            <div className="space-y-4">
              {allRecordings && allRecordings.map((recording) => (
                <div key={recording._id.toString()} className="bg-gray-800 rounded-xl shadow-lg overflow-hidden">
                  <CloseButton left={"90"} bottom={"85"} callback={() => handleDeleteAudio(recording._id.toString())}>
                    <div className="flex flex-col justify-center items-center p-4">
                      <p className="text-white font-medium text-lg mb-3">{recording.filename}</p>
                      <div className="w-full mb-4">
                        <audio
                          controls
                          src={`/Web-Apps/ai-translation/audioAPI?id=${recording._id.toString()}`}
                          className="w-full focus:outline-none"
                        />
                      </div>
                      <button
                        className="w-full px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white font-medium rounded-lg transition-colors shadow-md mb-3"
                        onClick={() => handleAudiotoText(recording._id.toString())}
                      >
                        Transcribe Audio
                      </button>
                      {displayedTranslations[recording._id.toString()] as string && (
                        <>
                          <div className="w-full mt-2 p-3 bg-gray-700 rounded-lg">
                            <p className="text-white">
                              <span className="font-semibold">Transcription:</span><br />
                              {displayedTranslations[recording._id.toString()]}
                            </p>
                          </div>
                        </>
                      )}
                    </div>
                    {displayedTranslations[recording._id.toString()] as string &&
                      <div className="w-full max-w-md mb-8 bg-gray-800 rounded-xl shadow-xl overflow-hidden">
                        <AIVoiceAppChatBoxSavedRecordingView recordedConversation={displayedTranslations[recording._id.toString()]} />
                      </div>
                    }
                  </CloseButton>
                </div>
              ))}
            </div>
          </div>
        )}

        <BottomNavComponent />
      </div>
    </>
  );
};

export default AudioTranscription;