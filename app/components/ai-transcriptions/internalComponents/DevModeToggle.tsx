"use client"

import useBasicToggle from "@/app/(main site)/Components/ui/BasicToggle2"

export default function DevModeToggle() {
    const [toggled, setToggled, BasicToggle] = useBasicToggle({ leftText: 'Simulate Inactive Subscription', RightText: 'Simulate Active Subscription' })

    function ToggleButton() { 
    return (
        <>
        <p className="text-cyan-400 text-center">If you Toggle this off the Modal that you require payment will trigger, because the conditions for a valid subscription are simulated to be false</p>
        <div className="">
            <BasicToggle />
        </div>
        </>
    )}

    return [toggled, ToggleButton]
    
}