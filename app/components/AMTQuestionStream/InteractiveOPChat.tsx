'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import useAudioRecorder from '@/app/(main site)/(Landing Pages)/Web-Apps/ai-translation/useAudioRecorder';
import { transcribeBlob } from '@/app/services/groqAudiotoTextService';
import handlefetch_ai_data from '@/app/(main site)/(Landing Pages)/ai-article-generator/servercontroller';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import 'katex/dist/katex.min.css' 
//import { opPracticeQuestions } from './opPracticeQuestions';
import { MicrophoneIcon } from '@heroicons/react/24/solid';
//import { shuffledQuestions as opPracticeQuestions}  from '@/app/components/AMTQuestionStream/O&P Questions'
import { general}  from '@/app/components/AMTQuestionStream/O&P Questions'
interface Message {
    sender: 'user' | 'ai';
    text: string;
}

 const allQuestions = general.General_Oral_Questions.flatMap(area => area.Questions);

const shuffle = (array: typeof allQuestions) => {
        let currentIndex = array.length,  randomIndex;
      
        // While there remain elements to shuffle.
        while (currentIndex !== 0) {
      
          // Pick a remaining element.
          randomIndex = Math.floor(Math.random() * currentIndex);
          currentIndex--;
      
          // And swap it with the current element.
          [array[currentIndex], array[randomIndex]] = [
            array[randomIndex], array[currentIndex]];
        }
      
        return array;
      }

      const opPracticeQuestions = JSON.stringify(shuffle(allQuestions))

const InteractiveOPChat = () => {
    const [messages, setMessages] = useState<Message[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [isTranscribing, setIsTranscribing] = useState(false);
    const [isSpeaking, setIsSpeaking] = useState(false);
    const chatEndRef = useRef<HTMLDivElement>(null);
    const { audioBlob, isRecording, startRecording, stopRecording, resetAudioBlob } = useAudioRecorder();

    
    //  const initialPrompt = `You are an FAA Designated Mechanic Examiner (DME). Your role is to conduct an oral and practical exam for an A&P mechanic. Ask me questions list of questions I give you, only the math ones. After I provide an answer, give me feedback on my response (is it correct, what could be improved but make your explination brief also make sure you use the answer), and then ask the next question. Do not ask any questions that are not on this list, and only ask the math ones.

    // Here is the list of questions: 
    // ${JSON.stringify(opPracticeQuestions)}

    // Start now.`;

    const initialPrompt = ''

    //   const initialPrompt = `You are an FAA Designated Mechanic Examiner (DME). Your role is to conduct an oral and practical exam for an A&P mechanic. Ask me questions list of questions I give you. and ask the question exactly as worded. Also give me the subjects, and questions in random order. After I provide an answer, give me feedback on my response (is it correct, what could be improved but make your explination brief also make sure you use the answer), and then ask another random question from the data. Do not ask any questions that are not on this list.

    // Here is the list of questions:
    // ${JSON.stringify(opPracticeQuestions)}

    // Start now.`;

    // const initialPrompt = `You are an FAA Designated Mechanic Examiner (DME). Your role is to conduct an oral and practical exam for an A&P mechanic. Ask me questions list of questions I give you. After I provide an answer, give me feedback on my response (is it correct, what could be improved but make your explination brief also make sure you use the answer), and then ask the next question. Do not ask any questions that are not on this list.

    // Here is the list of questions:
    // ${JSON.stringify(opPracticeQuestions)}

    // Start now.`;
    /**
 * @deprecated Use newFunction() instead.
 */
    // const initialPrompt = `You are an FAA Designated Mechanic Examiner (DME). Your role is to conduct an oral and practical exam for an A&P mechanic. Ask me the the first question. After I provide an answer, give me feedback on my response (is it correct, what could be improved), and then ask the next question. Do not ask any questions that are not on this list.

    // Here is the list of questions:
    // ${opPracticeQuestions}

    // Start now with your first question from the Basic Electricity section.`;

    const audioRef = useRef<HTMLAudioElement | null>(null);

        /**
         * @deprecated Use newFunction() instead.
         */
        // const speak = useCallback((text: string) => {
        //     if (typeof window === 'undefined' || !('speechSynthesis' in window)) {
        //         console.error("Text-to-speech not supported in this browser.");
        //         return;
        //     }
        //     // Clean up markdown for better speech flow
        //     const cleanText = text.replace(/(\*\*|__|\*|_|`|#)/g, '');
        //     const utterance = new SpeechSynthesisUtterance(cleanText);
    
        //     utterance.onstart = () => setIsSpeaking(true);
        //     utterance.onend = () => setIsSpeaking(false);
        //     utterance.onerror = (e) => {
        //         console.error("Speech synthesis error:", e);
        //         setIsSpeaking(false);
        //     };
    
        //     window.speechSynthesis.cancel(); // Stop any currently playing speech
        //     window.speechSynthesis.speak(utterance);
        // }, []);

    const speak = useCallback((text: string) => {
        if (audioRef.current) {
            audioRef.current.pause();
        }
        setIsSpeaking(true);
        fetch('/components/AMTQuestionStream', { 
        // fetch('/api2/test', { 
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ text }),
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Failed to fetch audio stream.');
            }
            return response.blob();
        })
        .then(blob => {
            const audioUrl = URL.createObjectURL(blob);
            const audio = new Audio(audioUrl);
            audioRef.current = audio;
            audio.play();
            audio.onended = () => {
                setIsSpeaking(false);
                URL.revokeObjectURL(audioUrl);
            };
        })
        .catch(error => {
            console.error("Speech generation failed:", error);
            setIsSpeaking(false);
        });
    }, []);

    const fetchInitialQuestion = useCallback(async () => {
        setIsLoading(true);
        try {
            const result = await handlefetch_ai_data({
                //selectedOption: 'openai o1-mini',
                selectedOption: 'O&P questions gemini-2.5-flash', 
                textInput: initialPrompt, 
            });
            setMessages([{ sender: 'ai', text: result }]);
            speak(result);
        } catch (error) {
            console.error("Failed to fetch initial question:", error);
            const errorMsg = "I'm having trouble connecting right now. Please try again later.";
            setMessages([{ sender: 'ai', text: errorMsg }]);
            speak(errorMsg);
        } finally {
            setIsLoading(false);
        }
    }, [initialPrompt, speak]);

    useEffect(() => {
        fetchInitialQuestion();
    }, [fetchInitialQuestion]);

    useEffect(() => {
        chatEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, [messages, isLoading, isTranscribing]);

    const handleSendMessage = useCallback(async (messageText: string) => {
        if (!messageText.trim() || isLoading) return;

        const newMessages: Message[] = [...messages, { sender: 'user', text: messageText }];
        setMessages(newMessages);
        setIsLoading(true);

        const conversationHistory = newMessages.map(msg => `${msg.sender === 'user' ? 'Applicant' : 'Examiner'}: ${msg.text}`).join('\n\n');
        const prompt = `${conversationHistory}\n\nExaminer: (Provide feedback on the applicant's last answer and ask the next question.)`;

        try {
            const result = await handlefetch_ai_data({
                //selectedOption: 'openai/gpt-oss-120b-VOICEAPP',
                //selectedOption: 'openai o1-mini',
                selectedOption: 'O&P questions gemini-2.5-flash',
                textInput: prompt,
            });
            setMessages(prev => [...prev, { sender: 'ai', text: result }]);
            speak(result);
        } catch (error) {
            console.error("Failed to get AI response:", error);
            const errorMsg = "Sorry, I encountered an error. Let's try that again.";
            setMessages(prev => [...prev, { sender: 'ai', text: errorMsg }]);
            speak(errorMsg);
        } finally {
            setIsLoading(false);
        }
    }, [isLoading, messages, speak]);

    const handleToggleRecording = () => {
        if (audioRef.current) {
            audioRef.current.pause();
        }
        if (isRecording) {
            stopRecording();
        } else {
            resetAudioBlob();
            startRecording();
        }
    };

    useEffect(() => {
        if (!audioBlob || isRecording) {
            return;
        }

        const transcribeAndSend = async () => {
            setIsTranscribing(true);
            try {
                const transcribedText = await transcribeBlob(audioBlob);
                if (transcribedText && transcribedText.trim()) {
                    await handleSendMessage(transcribedText);
                } else {
                    const errorMsg = "I didn't catch that. Could you please repeat?";
                    setMessages(prev => [...prev, { sender: 'ai', text: errorMsg }]);
                    speak(errorMsg);
                }
            } catch (error) {
                console.error("Transcription failed:", error);
                const errorMsg = "Sorry, I had trouble understanding that. Please try again.";
                setMessages(prev => [...prev, { sender: 'ai', text: errorMsg }]);
                speak(errorMsg);
            } finally {
                setIsTranscribing(false);
                resetAudioBlob();
            }
        };

        transcribeAndSend();
    }, [audioBlob, isRecording, handleSendMessage, resetAudioBlob, speak]);

    // Cleanup speech synthesis on component unmount
    useEffect(() => {
        return () => {
            if (audioRef.current) {
                audioRef.current.pause();
            }
        };
    }, []);

    return (
        <div className="flex flex-col h-[calc(100vh-250px)] bg-gray-100 rounded-lg shadow-inner">
            <div className="flex-1 p-4 overflow-y-auto space-y-4">
                {messages.map((msg, index) => (
                    <div key={index} className={`flex items-end gap-2 ${msg.sender === 'user' ? 'justify-end' : 'justify-start'}`}>
                        {msg.sender === 'ai' && <div className="w-8 h-8 rounded-full bg-gray-600 text-white flex items-center justify-center shrink-0">AI</div>}
                        <div className={`rounded-lg p-3 max-w-xl prose ${msg.sender === 'user' ? 'bg-blue-600 text-white' : 'bg-white text-gray-800 shadow-sm'}`}>
                            <ReactMarkdown 
                            remarkPlugins={[remarkGfm, remarkMath]}
                            rehypePlugins={[rehypeKatex]}
                            >{msg.text}</ReactMarkdown>
                        </div>
                    </div>
                ))}
                {(isLoading || isTranscribing) && (
                    <div className="flex items-end gap-2 justify-start">
                        <div className="w-8 h-8 rounded-full bg-gray-600 text-white flex items-center justify-center shrink-0">AI</div>
                        <div className="rounded-lg p-3 bg-white text-gray-800 shadow-sm">
                            <div className="flex items-center space-x-1">
                                <span className="h-2 w-2 bg-gray-400 rounded-full animate-pulse [animation-delay:-0.3s]"></span>
                                <span className="h-2 w-2 bg-gray-400 rounded-full animate-pulse [animation-delay:-0.15s]"></span>
                                <span className="h-2 w-2 bg-gray-400 rounded-full animate-pulse"></span>
                            </div>
                        </div>
                    </div>
                )}
                <div ref={chatEndRef} />
            </div>
            <div className="p-4 bg-white border-t border-gray-200 flex flex-col items-center">
                <button
                    onClick={handleToggleRecording}
                    disabled={isLoading || isTranscribing}
                    className={`px-6 py-3 rounded-full text-white font-medium shadow-lg transition-all transform hover:scale-105 mb-2 flex items-center ${isRecording
                        ? 'bg-red-600 hover:bg-red-700 animate-pulse'
                        : 'bg-blue-600 hover:bg-blue-700'
                        } disabled:bg-gray-400 disabled:cursor-not-allowed`}
                    aria-label={isRecording ? "Stop recording" : "Start recording"}
                >
                    <MicrophoneIcon className="h-6 w-6 mr-2" />
                    {isRecording ? 'Stop Recording' : 'Record Answer'}
                </button>
                {isTranscribing && <p className="text-sm text-gray-500 mt-1">Transcribing your answer...</p>}
            </div>
        </div>
    );
};

export default InteractiveOPChat;