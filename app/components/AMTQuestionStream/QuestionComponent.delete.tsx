"use client"

import React, { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import { CloseButton } from '@/app/components/ui/CloseButton';
import { temporary, corrosion } from './Question';
import { useSearchParams } from "next/navigation";
import { questionData } from '@/app/components/AMTQuestionStream/Question';
import AIVoiceAppChatBox from '@/app/components/ai-chat/AIChatBox.VoiceAPP' 



// generate questions from chapter 4  "aircraft drawings" of aircraft maintenance general faa book


console.log('cool beans')
// Define the question type
type Question = {
  question: string;
  options: string[];
  correct_answer: string;
};

//type AIResultType = [Question[]];
//we changed the type 6/24/2025 to stop the errors
type AIResultType = null | (string | { question: string; options: string[]; correct_answer: string; }[])[];


// Keep track of selected answers for each question
type AnswerState = {
  selectedAnswer: string | null;
  isSubmitted: boolean;
};



function QuestionComponent({ ai_result }: { ai_result: AIResultType }) {
  // State to track answers for each question
  const [answerStates, setAnswerStates] = useState<Record<string, AnswerState>>({});
    // Add state for tracking which explanations are visible
  const [explanationStates, setExplanationStates] = useState<Record<string, boolean>>({});
console.log('cool beans')

  // Handle explanation button click

function handleExplainClick(questionIndex: number){
  console.log('Explain clicked');
  setExplanationStates(prev => ({
    ...prev,
    [questionIndex]: true
  }));
}
  console.log(ai_result)
  //ai_result = corrosion // delete this line to restore
  const searchParams = useSearchParams();
  const chapter = searchParams?.get('chapter');
  console.log(chapter)

  // Handle answer selection
  const handleAnswerSelect = (questionIndex: number, option: string) => {
    const questionKey = `q-${questionIndex}`;
    
    setAnswerStates(prev => ({
      ...prev,
      [questionKey]: {
        selectedAnswer: option,
        isSubmitted: true
      }
    }));
  };

  // Determine button classes based on answer state
  const getButtonClasses = (questionIndex: number, option: string, correctAnswer: string) => {
    const questionKey = `q-${questionIndex}`;
    const answerState = answerStates[questionKey];
    
    if (!answerState || !answerState.isSubmitted) {
      return "btn btn-outline w-full justify-start h-auto py-2 px-4 normal-case";
    }
    
    // Correct answer is always green when submitted
    if (option === correctAnswer) {
      return "btn btn-success w-full justify-start h-auto py-2 px-4 normal-case text-white"; 
    }
    
    // User's incorrect choice is red
    if (option === answerState.selectedAnswer && option !== correctAnswer) {
      return "btn btn-error w-full justify-start h-auto py-2 px-4 normal-case text-white";
    }
    
    // Non-selected options - use a light background but darker text for contrast
    return "btn w-full justify-start h-auto py-2 px-4 normal-case bg-base-200 text-base-content border-base-300";
  };

  // Show feedback message
  const getFeedbackMessage = (questionIndex: number, correctAnswer: string, question) => {
    const questionKey = `q-${questionIndex}`;
    const answerState = answerStates[questionKey];
    console.log("this is the question.quest", question)
    if (!answerState || !answerState.isSubmitted) {
      return null;
    }
    
    return (
      <div className="mt-3">
        <div className={answerState.selectedAnswer === correctAnswer ? "text-success font-bold" : "text-error font-bold"}>
          {answerState.selectedAnswer === correctAnswer ? "Correct!" : "Incorrect"}
        </div>
        <button 
          className="btn btn-primary mt-2"
          onClick={() => handleExplainClick(questionIndex)}
        >
          Explain
        </button>
                    {explanationStates[questionIndex] &&
              <div className="w-full max-w-md mb-8 bg-gray-800 rounded-xl shadow-xl overflow-hidden">
                <AIVoiceAppChatBox recordedConversation={question} />
              </div>
            }
      </div>
    );
  };



  return (
    <>
      {ai_result && ai_result.map((q, i) => {
        if (i === 0) return (<p key={i}></p>);
        
        return (q.map((question, j) => {
          return (
            <div className="card bg-base-100 shadow-xl p-6 mb-8" key={`question-${j}`}>
                {question.fig && (
                  <div className="mb-4">
                    <img 
                      src={`/amt_app_images/question_images/${question.fig}`} 
                      alt={`Figure for question ${j+1}`}
                      className="max-w-full mx-auto"
                    />
                  </div>
                )}
                {console.log(question.question)}
                <h3 className="card-title mb-4 text-left">
                  <span className="mr-2 font-bold">{j+1}.</span>
                  <ReactMarkdown
                    remarkPlugins={[remarkMath]}
                    rehypePlugins={[rehypeKatex]}
                  >{question.question}</ReactMarkdown>
                </h3>
                <div className="flex flex-col gap-3">
                  {question.options.map((option, k) => (
                    <button 
                      key={`option-${k}`}
                      className={getButtonClasses(j, option, question.correct_answer)}
                      onClick={() => handleAnswerSelect(j, option)}
                    //   disabled={answerStates[`q-${j}`]?.isSubmitted && option !== question.correct_answer && option !== answerStates[`q-${j}`]?.selectedAnswer}
                    >
                      <div className="markdown-content text-left w-full overflow-hidden break-words">
                        <ReactMarkdown
                          remarkPlugins={[remarkMath]}
                          rehypePlugins={[rehypeKatex]}
                        >{option}</ReactMarkdown>
                      </div>
                    </button>
                  ))}
                </div>
                {getFeedbackMessage(j, question.correct_answer, JSON.stringify(question))}
            </div>
          );
        }));
      })}

      {/* Add some global styles for mathematical content */}
      <style jsx global>{`
        .markdown-content {
          display: inline-block;
          width: 100%;
        }
        
        /* Make sure math elements don't overflow */
        .katex-html {
          max-width: 100%;
          overflow-x: auto;
          overflow-y: hidden;
        }
        
        /* Adjust button height for multi-line content */
        .btn {
          height: auto;
          min-height: 3rem;
          white-space: normal;
          text-align: left;
        }
      `}</style>
    </>
  );
}

export default QuestionComponent;



// import React from 'react';
// import ReactMarkdown from 'react-markdown';
// import remarkMath from 'remark-math';
// import rehypeKatex from 'rehype-katex';
// import { CloseButton } from '@/app/components/ui/CloseButton'

// // Define the question type
// type Question = {
//     question: string;
//     options: string[];
//     correct_answer: string;
//   };

// type AIResultType = [Question[]];


// function QuestionComponent({ ai_result}: { ai_result: AIResultType }) {
//   return (
//     <>
//       {ai_result.map((q, i) => {
//         console.log('hit');
//         if (i === 0) return (<p key={i}></p>);
//         console.log(ai_result);
//         console.log(q);
//     // if(aiResult != 'Your Results Will Appear Here') console.log(JSON.parse(aiResult));
//         return (q.map((question, j) => {
//           return (
//             <div className="p-10" key={`question-${j}`}>
//               <CloseButton>
//                 <h3>
//                   <ReactMarkdown
//                     remarkPlugins={[remarkMath]}
//                     rehypePlugins={[rehypeKatex]}
//                   >{question.question}</ReactMarkdown>
//                 </h3>
//                 <ol>
//                   {question.options.map((option, k) => (
//                     <li key={`option-${k}`}>
//                       <button>
//                         <ReactMarkdown
//                           remarkPlugins={[remarkMath]}
//                           rehypePlugins={[rehypeKatex]}
//                         >{option}</ReactMarkdown>
//                       </button>
//                     </li>
//                   ))}
//                 </ol>
//               </CloseButton>
//             </div>
//           );
//         }));
//       })}
//     </>
//   );
// }

// export default QuestionComponent;
