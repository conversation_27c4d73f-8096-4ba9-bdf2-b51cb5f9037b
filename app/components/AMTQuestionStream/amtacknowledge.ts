'use server'
import { GoogleAuth } from 'google-auth-library';
import * as path from 'path';
//import serviceAccountKey from './service-account.json' assert { type: 'json' };

//const KEY_FILE_PATH = serviceAccountKey;
const serviceAccountJsonString = process.env.GOOGLE_SERVICE_ACCOUNT_JSON
// const KEY_FILE_PATH = *************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
//need to revert to this key path
const KEY_FILE_PATH = ***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
//nst KEY_FILE_PATH = JSON.parse(serviceAccountJsonString || '{}');
console.log(KEY_FILE_PATH)
// const KEY_FILE_PATH = "./app/components/ai-transcriptions/service-account.json"
// Path to your service account key file
//const KEY_FILE_PATH = path.join(__dirname,'./service-account.json'); // <--- IMPORTANT: Update this path and it had to be a root path thats we we added path.join _dirname its also on app/(main site)   to prevent a mobile error we kept getting

// The scope required for the Android Publisher API
const SCOPES = ['https://www.googleapis.com/auth/androidpublisher'];

// Function to get an access token
export async function getAccessToken(): Promise<string | null | undefined> {
  try {
    const auth = new GoogleAuth({
      //keyFilename: KEY_FILE_PATH,
      credentials: KEY_FILE_PATH,
      scopes: SCOPES,
    });

    const client = await auth.getClient();
    const accessTokenResponse = await client.getAccessToken();
    console.log('Access token:', accessTokenResponse.token);
    return accessTokenResponse.token;
  } catch (error) {
    console.error('Error getting access token:', error);
    throw new Error('Could not obtain access token'); // Or handle more gracefully
  }
}