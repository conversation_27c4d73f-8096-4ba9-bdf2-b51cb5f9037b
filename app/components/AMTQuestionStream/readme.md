# Todoos

## Gemini simulations

--Magneto sim video: https://gemini.google.com/app/afdba35cd7cf47d1

--Magneto sim program: https://gemini.google.com/app/1b6d0ea032b889b9

--Turbo Jet: https://gemini.google.com/app/364f8c1087357e39

## videos to watch
- magnetos: https://www.youtube.com/watch?v=QJNy0I2Hzvc

## Keywords we are trying to rank for


we are using whatsmyserp for data

- ### Online A&P Test Prep Vol 0
    - how to study for a&P 40
    - how to study for a&p exam 10
### powerplant test prep 0
    - airframe and powerplant test prep 50
    - airframe and powerplant test prep schools 30
    - a&p prep course near me 20
    - airframe and powerplant practice test online 10

## add new question SOP
- extract questions with https://aistudio.google.com/prompts/19WnuMXWOZKLP58iOeZMefUpufbfd9Mst
- make a new file that is similar to "Question.PropellerPracticeCombined.ts"
- make a new entry that follows this format
```jsx
export const Prac + PropellerPracticeCombined = [
  "Your Results Will Appear Here",
  [
    {
      "question": "What actuates the pilot valve in the governor of a constant-speed propeller?",
      "options": [
        "Engine oil pressure",
        "Governor flyweights",
        "Governor pump oil pressure"
      ],
      "correct_answer": "Governor pump oil pressure"
    },
    {
      "question": "Propeller fluid anti-icing systems generally use which of the following?",
      "options": [
        "Ethylene glycol",
        "Isopropyl alcohol",
        "Ethyl alcohol"
      ],
      "correct_answer": "Ethyl alcohol"
    }
  ]
]
```

- open 
[text](deduplicateScript.ts)

- change 
```jsx
        const fileIdentifier = 'PropellerPracticeCombined'; // Change this to the part of the filename you want to process
```
 3. bun run deduplicate 
 4. import the appropriate file into [text](sectionData.tsx) like this:
 ```jsx
import { PracPropellerPracticeCombinedDeduplicated } from './Question.PropellerPracticeCombined.deduplicated';
```
5. add the questions to the sectionData.tsx file like this:
```jsx
    {
        type: 'testItem',
        icon: 'PropellerIcon', // Assuming you still want this to be distinct in data
        title: 'Aircraft Propeller',
        active: true,
        questions: PracPropellerPracticeCombinedDeduplicated
    },
```