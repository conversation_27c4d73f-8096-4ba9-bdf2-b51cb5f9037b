'use client'
// /home/<USER>/monoNextjsreboot/app/components/AMTQuestionStream/StarterCircuit.tsx
import React, { useState } from 'react';

const StarterCircuit = () => {
  const [isStarting, setIsStarting] = useState(false);
  const [isFuelPump, setIsFuelPump] = useState(false)
  const [isExternalPower, setIsExternalPower] = useState(false)

  const toggleStart = () => setIsStarting(!isStarting);
  const toggleFuelPump = () => setIsFuelPump(!isFuelPump);
  const toggleExternalPower = () => setIsExternalPower(!isExternalPower)

  return (
    <div className="flex flex-col items-center p-8 bg-gray-100 min-h-screen">
      <div className="w-full max-w-4xl mb-6 p-4 bg-blue-50 border-l-4 border-blue-500 text-blue-700 rounded shadow-sm">
        <p className="font-bold">Beta Feature</p>
        <p>We are making simulations to help your test scores and O&Ps.</p>
      </div>
      <div className="relative inline-block bg-white p-4 rounded-xl shadow-2xl border border-gray-300">

        {/* Main Circuit Diagram */}
        <img
          src="/sims/sim1.png"
          alt="Starter Generator Circuit"
          className="max-w-4xl h-auto block"
        />

        {/* SVG Overlay Layer */}
        <svg
          className="absolute top-0 left-0 w-full h-full pointer-events-none"
          viewBox="0 0 1000 750" // Adjust viewbox to match image aspect ratio
        >
          {/* B-4 Relay / Battery Solenoid Bridge (the moving part) */}
          <line
            x1="88"
            y1={isStarting ? "282" : "265"}
            x2="118"
            y2={isStarting ? "282" : "265"}
            className={`transition-all duration-300 ease-in-out ${isStarting ? "stroke-red-600 opacity-100" : "stroke-transparent opacity-0"
              }`}
            strokeWidth="6"
            strokeLinecap="round"
          />
          {/* Highlighted power paths when starting */}
          <g
            className={`transition-opacity duration-300 ease-in-out ${isStarting ? "stroke-red-600 opacity-100" : "stroke-transparent opacity-0"
              }`}
            strokeWidth="6"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            {/* 1. Control circuit path to energize the relay coil */}
            <path d="M 88 345 V 320 H 118 V 295 H 140" />
            <path d="M 140 265 H 160" />

            {/* 2. Main power circuit path to the starter motor */}
            {/*    This path is completed by the moving line above */}
            <path d="M 88 345 V 282" /> {/* Power from Battery to Relay Input */}
            <path d="M 118 282 H 280 V 240 H 300" /> {/* Power from Relay Output to Starter */}

            {/* Ground path for the starter motor */}
            <path d="M 400 240 H 420" />

            {/* External Power Receptacle */}
            <path d="M 542 57 H 690" />
            <path d="M 542 57 V 167 H 585" />

            <path d="M 569 80 H 690" />
            <path d="M 569 80 V 150 L 585 168" />

            <path d="M 200 53 V 105 H 510 V 187 H 572 L 585 168" />

          </g>



          {/* Connected to External power*/}
          <g
            className={`transition-opacity duration-300 ease-in-out ${isExternalPower ? "stroke-red-600 opacity-100" : "stroke-transparent opacity-0"
              }`}
            strokeWidth="6"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
            filter="url(#glow)"
          >
            <defs>
              <filter id="glow">
                <feGaussianBlur stdDeviation="4" result="blur" />
                <feMerge>
                  <feMergeNode in="blur" />
                  <feMergeNode in="SourceGraphic" />
                </feMerge>
              </filter>
            </defs>
            <animate
              attributeName="stroke-opacity"
              values="0.2;1;0.2"
              dur="1.2s"
              repeatCount="indefinite"
            />
            {/* External Power Receptacle */}
            <path d="M 542 57 H 690" />
            <path d="M 542 57 V 167 H 585" />

            <path d="M 569 80 H 690" />
            <path d="M 569 80 V 150 L 585 168" />

            <path d="M 200 53 V 105 H 510 V 187 H 572 L 585 168" />

          </g>
           <g
            className={`transition-opacity duration-300 ease-in-out ${isExternalPower ? "stroke-black opacity-200" : "stroke-transparent opacity-0"
              }`}
            strokeWidth="6"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M 128 388 L 95 400" />

          </g>
          <g
            className={`transition-opacity duration-300 ease-in-out ${isExternalPower ? "stroke-white opacity-200" : "stroke-transparent opacity-0"
              }`}
            strokeWidth="6"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M 128 394 L 114 420" />

          </g>
          <g
            className={`transition-opacity duration-300 ease-in-out ${isFuelPump ? "stroke-red-600 opacity-100" : "stroke-transparent opacity-0"
              }`}
            strokeWidth="6"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
          >

            {/* Fuel pump */}
            <path d="M 160 53 Q 180 85 160 118 H 250" />
            <path d="M 300 118 H 378" />
          </g>

          <g
            className={`transition-opacity duration-300 ease-in-out ${isFuelPump ? "stroke-black opacity-200" : "stroke-transparent opacity-0"
              }`}
            strokeWidth="6"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M 250 118 L 287 118" />

          </g>
          <g
            className={`transition-opacity duration-300 ease-in-out ${isFuelPump ? "stroke-white opacity-200" : "stroke-transparent opacity-0"
              }`}
            strokeWidth="6"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M 250 118 L 287 136" />

          </g>

          <g
            className={`transition-opacity duration-300 ease-in-out ${isFuelPump ? "stroke-black opacity-200" : "stroke-transparent opacity-0"
              }`}
            strokeWidth="6"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            {/* Engine Master Switch */}
            <path d="M 250 205 L 300 190" />
            <path d="M 250 260 L 300 242" />


            {/* Battery Switch x406 -156 y 292  +115*/}
            <path d="M 250 390 L 305 400" />

            {/* Power Lever Switch */}
            <path d="M 270 605 L 300 598" />
            <path d="M 270 605 L 305 619" />
          </g>

          <g
            className={`transition-opacity duration-300 ease-in-out ${isFuelPump ? "stroke-white opacity-200" : "stroke-transparent opacity-0"
              }`}
            strokeWidth="6"
            fill="none"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            {/* Engine Master Switch */}
            <path d="M 250 205 L 300 210" />
            <path d="M 250 260 L 300 268" />



            {/* Battery Switch */}
            <path d="M 255 388 L 300 382" />
          </g>

          {/* Optional: Glow effect for the relay contact point */}
          {isStarting && (
            <circle cx="103" cy="282" r="8" className="fill-red-500 animate-pulse opacity-50" />
          )}
        </svg>

        {/* Clickable Hotspot over the "Battery and Start Switch" area */}
        <button
          onClick={toggleStart}
          className="absolute top-[42%] left-[12%] w-12 h-16 cursor-pointer rounded border-2 border-dashed border-transparent hover:border-blue-400 hover:bg-blue-400/20 transition-all flex items-center justify-center"
          title="Toggle Battery Switch"
        >
          <span className="sr-only">Toggle Battery Switch</span>
        </button>

        <button
          onClick={toggleExternalPower}
          className="absolute top-[13%] left-[58%] w-24 h-16 cursor-pointer rounded border-2 border-dashed border-transparent hover:border-blue-400 hover:bg-blue-400/20 transition-all flex items-center justify-center"
          title="Toggle Battery Switch"
        >
          <span className="sr-only">Toggle External Power</span>
        </button>

        <button
          onClick={toggleFuelPump}
          className="absolute top-[18%] left-[24%] w-12 h-16 cursor-pointer rounded border-2 border-dashed border-transparent border-blue-400 bg-blue-400/20 transition-all flex items-center justify-center"
          title="Toggle Battery Switch"
        >
          <span className="sr-only">Toggle Fuel Pump</span>
        </button>
      </div>

      {/* Control Panel / Status Indicator */}
      <div className="mt-8 p-6 bg-white rounded-lg shadow-md border-l-4 border-blue-500 w-full max-w-md">
        <h3 className="text-lg font-bold text-gray-800">Circuit Status</h3>
        <p className="text-sm text-gray-600 mb-4">Click the battery switch on the diagram to turn the power on or off.</p>
        <fieldset className="fieldset bg-base-100 border-base-300 rounded-box w-64 border p-4">
          <legend className="fieldset-legend">Power options</legend>

          <label className="label gap-3">
            <input
              onClick={toggleExternalPower}
              type="checkbox"
              className="toggle rotate-[-90deg]"
            />
            Connect External Power
          </label>
        </fieldset>

        <div className="flex items-center gap-4">
          <div className={`px-4 py-2 rounded-full font-mono text-sm font-bold transition-colors ${isStarting ? "bg-red-100 text-red-700" : "bg-gray-100 text-gray-500"
            }`}>
            BATTERY SWITCH: {isStarting ? "ON" : "OFF"}
          </div>
        </div>
      </div>
    </div>
  );
};

export default StarterCircuit;
