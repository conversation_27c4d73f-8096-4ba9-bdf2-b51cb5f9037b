import { NextRequest, NextResponse } from 'next/server';
import Groq from 'groq-sdk';

const groq = new Groq({
    apiKey: process.env.GROQAPI,
});

export async function GET() {
    return NextResponse.json({ message: 'Hello from TTS API' });
} 

export async function POST(req: NextRequest) {
    console.log('tts route hit')
    try {
        const { text } = await req.json();

        if (!text) {
            return new NextResponse('Text input is required', { status: 400 });
        }

        // Clean up markdown for better speech flow
        const cleanText = text.replace(/(\*\*|__|\*|_|`|#)/g, '');

        const response = await groq.audio.speech.create({
            model: "playai-tts",
            voice: "Chip-PlayAI",
            input: cleanText,
            response_format: "mp3", // Using mp3 for better web compatibility
        });

        // The SDK returns a stream. We can pipe it directly to the response.
        return new NextResponse(response.body, {
            headers: { 'Content-Type': 'audio/mpeg' },
        });
    } catch (error) {
        console.error("Groq TTS API error:", error);
        return new NextResponse('Failed to generate speech', { status: 500 });
    }
}

