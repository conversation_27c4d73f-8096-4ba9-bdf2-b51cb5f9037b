"use client"

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex'
import 'katex/dist/katex.min.css'
import { CloseButton } from '@/app/components/ui/CloseButton';
import { temporary, corrosion } from './Question';
import { useSearchParams } from "next/navigation";
import { questionData } from '@/app/components/AMTQuestionStream/Question';
import AIVoiceAppChatBox from '@/app/components/ai-chat/AIChatBox.VoiceAPP'
import AIVoiceAppChatBoxSavedRecordingView from '@/app/components/ai-chat/AIChatBox.VoiceAPPSavedRecordingView'
import handlefetch_ai_data from '@/app/(main site)/(Landing Pages)/ai-article-generator/servercontroller'
import useLoading from "@/app/(main site)/Components/ui/Loading2";
import { MagnifyingGlassIcon } from '@/app/components/ui/svgIcons';
import { addFAAQuestions, getFAAFavoriteQuestions, removeFAAQuestion, updateFAAQuestionSkillLevelPublic } from "@/app/db/dbCurry";


// generate questions from chapter 4  "aircraft drawings" of aircraft maintenance general faa book


console.log('cool beans')
// Define the question type
type Question = {
  question: string;
  options: string[];
  correct_answer: string;
  fig?: string | string[];
  explination_ref?: string;
  explination_images?: string[];
};

//type AIResultType = [Question[]];
//we changed the type 6/24/2025 to stop the errors
type AIResultType = null | (string | Question[])[];


// Keep track of selected answers for each question
type AnswerState = {
  selectedAnswer: string | null;
  isSubmitted: boolean;
};

// Skill level options
const SKILL_LEVEL_OPTIONS = ["really bad", "bad", "somewhat bad"];
const DEFAULT_SKILL_LEVEL = "somewhat bad";



function QuestionComponent({
  ai_result,
  questionFontSize,
  setQuestionFontSize,
  isQuestionPreview,
  goHome,
  onGenerateNewTest,
  totalQuestionsAvailable,
  numberOfQuestions,
  setNumberOfQuestions,
  onSkillLevelChange
}: {
  ai_result: AIResultType,
  questionFontSize: number,
  setQuestionFontSize: (size: number) => void,
  totalQuestionsAvailable: number,
  isQuestionPreview: boolean,
  goHome: () => void,
  onGenerateNewTest?: () => void,
  numberOfQuestions?: number,
  setNumberOfQuestions?: (count: number) => void,
  onSkillLevelChange?: () => void
}) {
  // State to track answers for each question
  const [answerStates, setAnswerStates] = useState<Record<string, AnswerState>>({});
  // Add state for tracking which explanations are visible
  const [explanationStates, setExplanationStates] = useState<Record<string, boolean>>({});
  // Add state for tracking favorited questions
  const [favoriteStates, setFavoriteStates] = useState<Record<string, boolean>>({});
  // Add state for tracking skill levels
  const [skillLevels, setSkillLevels] = useState<Record<string, string>>({});
  const [zoomedImage, setZoomedImage] = useState<string | null>(null);
  const [isFontSizeSliderVisible, setIsFontSizeSliderVisible] = useState(false);
  const [ai_responses, setAi_responses] = useState<Record<string, string>>({});
  const [setLoading, LoadingWrapper, LoadSuccess, LoadError] = useLoading()

  useEffect(() => {
    // When the question bank (ai_result) changes, reset all component state.
    setAnswerStates({});
    setExplanationStates({});
    setAi_responses({});
    setSkillLevels({});

    const fetchAndSetFavorites = async () => {
      const userId = localStorage.getItem('userId');
      if (!userId || !ai_result || !Array.isArray(ai_result[1])) {
        setFavoriteStates({});
        setSkillLevels({});
        return;
      }

      const favoriteQuestions = await getFAAFavoriteQuestions(userId);
      const newFavoriteStates: Record<string, boolean> = {};
      const newSkillLevels: Record<string, string> = {};

      if (favoriteQuestions && favoriteQuestions.length > 0) {
        const questionsList = ai_result[1] as Question[];
        questionsList.forEach((question, j) => {
          // Using question text as a unique identifier.
          const favoriteQuestion = favoriteQuestions.find(fav => fav.question === question.question);
          if (favoriteQuestion) {
            newFavoriteStates[j] = true;
            newSkillLevels[j] = favoriteQuestion.skillLevel || DEFAULT_SKILL_LEVEL;
          }
        });
      }

      setFavoriteStates(newFavoriteStates);
      setSkillLevels(newSkillLevels);
    };

    fetchAndSetFavorites();
  }, [ai_result]);
  console.log('cool beans')

  // Handle favorite button click
  const handleFavoriteClick = async (questionIndex: number, question: Question) => {
    const userId = localStorage.getItem('userId');
    if (userId) {
      const isCurrentlyFavorited = !!favoriteStates[questionIndex];

      setFavoriteStates(prev => ({
        ...prev,
        [questionIndex]: !isCurrentlyFavorited
      }));

      console.log(isCurrentlyFavorited)

      if (isCurrentlyFavorited) {
        await removeFAAQuestion(userId, question);
        // Remove skill level when unfavoriting
        setSkillLevels(prev => {
          const newSkillLevels = { ...prev };
          delete newSkillLevels[questionIndex];
          return newSkillLevels;
        });
      } else {
        // Add to favorites with default skill level
        const skillLevel = skillLevels[questionIndex] || DEFAULT_SKILL_LEVEL;
        await addFAAQuestions(userId, question, skillLevel);
        setSkillLevels(prev => ({
          ...prev,
          [questionIndex]: skillLevel
        }));
      }
    }
  };

  // Handle skill level change
  const handleSkillLevelChange = async (questionIndex: number, question: Question, newLevel: string) => {
    console.log('Changing skill level for question:', question.question, 'to:', newLevel);

    setSkillLevels(prev => ({
      ...prev,
      [questionIndex]: newLevel
    }));

    const userId = localStorage.getItem('userId');
    if (userId) {
      // Update the favorite with the new skill level using the dedicated function
      const result = await updateFAAQuestionSkillLevelPublic(userId, question, newLevel);
      console.log('Skill level update result:', result);

      // Notify parent component that skill level has changed
      if (onSkillLevelChange) {
        onSkillLevelChange();
      }
    }
  };

  // Handle explanation button click

  async function handleExplainClick(questionIndex: number, questionJson: string) {
    const question: Question = JSON.parse(questionJson);

    if (question.explination_ref) {
      setAi_responses(prev => ({
        ...prev,
        [questionIndex]: `**Reference:** ${question.explination_ref}`
      }));
      setExplanationStates(prev => ({
        ...prev,
        [questionIndex]: true
      }));
    } else {
      console.log('Explain clicked');
      let result = handlefetch_ai_data({
        selectedOption: 'openai/gpt-oss-120b',
        textInput: 'why is this the correct answer also you dont got to repeat the answer just explain why it is correct. ' + questionJson,
        multipleGenerationText: '',
        generationCount: 'Select Chapter Amount'
      });
      let answer = await result;
      setAi_responses(prev => ({
        ...prev,
        [questionIndex]: answer
      }));
      setExplanationStates(prev => ({
        ...prev,
        [questionIndex]: true
      }));
    }
  }
  console.log(ai_result)
  //ai_result = corrosion // delete this line to restore
  const searchParams = useSearchParams();
  const chapter = searchParams?.get('chapter');
  console.log(chapter)

  // Handle answer selection (and if you click the question again it will reset itself back to unanswered)
  const handleAnswerSelect = (questionIndex: number, option: string) => {
    const questionKey = `q-${questionIndex}`;

    if (answerStates[questionKey]?.isSubmitted == true) {
      setAnswerStates(prev => ({
        ...prev,
        [questionKey]: {
          selectedAnswer: option,
          isSubmitted: false
        }
      }));
    }
    else {
      setAnswerStates(prev => ({
        ...prev,
        [questionKey]: {
          selectedAnswer: option,
          isSubmitted: true
        }
      }));
    } 

  };

  // Determine button classes based on answer state
  const getButtonClasses = (questionIndex: number, option: string, correctAnswer: string) => {
    const questionKey = `q-${questionIndex}`;
    const answerState = answerStates[questionKey];

    if (!answerState || !answerState.isSubmitted) {
      return "btn btn-outline w-full justify-start h-auto py-2 px-4 normal-case";
    }

    // Correct answer is always green when submitted
    if (option === correctAnswer) {
      return "btn btn-success w-full justify-start h-auto py-2 px-4 normal-case text-white";
    }

    // User's incorrect choice is red
    if (option === answerState.selectedAnswer && option !== correctAnswer) {
      return "btn btn-error w-full justify-start h-auto py-2 px-4 normal-case text-white";
    }

    // Non-selected options - use a light background but darker text for contrast
    return "btn w-full justify-start h-auto py-2 px-4 normal-case bg-base-200 text-base-content border-base-300";
  };

  // Show feedback message
  const getFeedbackMessage = (questionIndex: number, correctAnswer: string, questionJson: string) => {
    const questionKey = `q-${questionIndex}`;
    const answerState = answerStates[questionKey];
    const question: Question = JSON.parse(questionJson);
    console.log("this is the question.quest", questionJson)
    if (!answerState || !answerState.isSubmitted) {
      return null;
    }

    return (
      <div className="mt-3 text-center">
        <div className={answerState.selectedAnswer === correctAnswer ? "text-success font-bold" : "text-error font-bold"}>
          {answerState.selectedAnswer === correctAnswer ? "Correct!" : "Incorrect"}
        </div>
        {!explanationStates[questionIndex] && (
          <LoadingWrapper callback={() => handleExplainClick(questionIndex, questionJson)}>
            <button
              className="btn btn-primary mt-10"
            // onClick={() => {handleExplainClick(questionIndex, questionJson)}}
            >
              Explain
            </button>
          </LoadingWrapper>
        )}
        {explanationStates[questionIndex] &&
          <>
            <div className="prose text-center mx-auto">
              <ReactMarkdown
                remarkPlugins={[remarkGfm, remarkMath]}
                rehypePlugins={[rehypeKatex]}
              >{ai_responses[questionIndex] || ''}</ReactMarkdown>
              {question.explination_images && question.explination_images.map((img, i) => ( 
                <img 
                  key={i} src={img} alt={`Explanation image ${i + 1}`} 
                  className="w-full h-auto mx-auto my-2 cursor-pointer" 
                  onClick={() => setZoomedImage(img)}
                />
              ))}
            </div>
            {/* <div className="w-full max-w-md mb-8 bg-gray-800 rounded-xl shadow-xl overflow-hidden">
            <AIVoiceAppChatBoxSavedRecordingView recordedConversation={questionJson} />
          </div> */}
          </>
        }
      </div>
    );
  };

  let correctCount = 0;
  let submittedCount = 0;
  let totalQuestions = 0;

  if (ai_result && Array.isArray(ai_result[1])) {
    const questionsList = ai_result[1] as Question[];
    totalQuestions = questionsList.length;
    questionsList.forEach((question, index) => {
      const questionKey = `q-${index}`;
      const answerState = answerStates[questionKey];

      if (answerState && answerState.isSubmitted) {
        submittedCount++;
        if (answerState.selectedAnswer === question.correct_answer) {
          correctCount++;
        }
      }
    });
  }

  const scorePercentage = submittedCount > 0 ? (correctCount / submittedCount) * 100 : 0;



  return (
    <>
      {onGenerateNewTest && (
        <div className="flex justify-center items-center p-4 bg-base-200 rounded-t-lg">
          <button
            onClick={onGenerateNewTest}
            className="btn btn-primary"
          >Generate New Test</button>
        </div>
      )}
      <div className="flex justify-center items-center p-4 bg-base-200 rounded-t-lg">
        <button
          onClick={() => setIsFontSizeSliderVisible(!isFontSizeSliderVisible)}
          className="flex items-center gap-2 px-3 py-2 rounded-lg bg-white border border-base-300 hover:bg-base-50 hover:border-base-400 transition-all duration-200 shadow-sm"
          aria-label="Adjust font size"
        >
          <MagnifyingGlassIcon className="h-5 w-5" />
          <span className="text-sm font-medium hidden sm:inline">Font Size</span>
        </button>
      </div>
         {onGenerateNewTest && setNumberOfQuestions && numberOfQuestions !== undefined && ai_result && ai_result[0] !== 'Your Favorite Questions' && (
                            <div className="px-4 py-2 bg-slate-100 border-y border-slate-200">
                                <label htmlFor="question-count-slider" className="block text-sm font-medium text-slate-700 mb-1">Set # of Questions: {numberOfQuestions}</label>
                                <input
                                    id="question-count-slider"
                                    type="range"
                                    min="5"
                                    max={totalQuestionsAvailable > 5 ? totalQuestionsAvailable : 100}
                                    value={numberOfQuestions}
                                   onChange={(e) => setNumberOfQuestions(Number(e.target.value))}
                                    className="w-full h-2 bg-slate-200 rounded-lg appearance-none cursor-pointer"
                                />
                            </div>
                        )}
      {isFontSizeSliderVisible && (
        <div className="px-4 py-3 bg-base-200">
          <label htmlFor="font-size-slider" className="block text-sm font-medium text-base-content mb-1">Question Font Size: {questionFontSize}px</label>
          <input
            id="font-size-slider"
            type="range"
            min="12"
            max="24"
            value={questionFontSize}
            onChange={(e) => setQuestionFontSize(Number(e.target.value))}
            className="w-full h-2 bg-base-300 rounded-lg appearance-none cursor-pointer"
          />
        </div>
      )}
      {ai_result && ai_result.map((q, i) => {
        if (i === 0) return (<p key={i}></p>);

        return (Array.isArray(q) ? q.map((question, j) => {
          const isPaywalled = isQuestionPreview && j >= 19;

          if (isPaywalled) {
            return (
              <div key={`question-${j}`} className="card bg-base-100 shadow-xl p-6 mb-8 relative overflow-hidden">
                <div className="blur-sm opacity-20">
                  <h3 className="card-title text-left flex-grow">
                    <span className="mr-2 font-bold">{j + 1}.</span>
                    <div className="px-2 py-1" style={{ fontSize: `${questionFontSize}px` }}>Question hidden</div>
                  </h3>
                </div>
                {j === 19 && <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-50"  ><button  className="btn btn-primary" onClick={goHome}>Subscribe to see more</button></div>}
              </div>
            );
          }
          return (
            <div className="card bg-base-100 shadow-xl p-6 mb-8" key={`question-${j}`}>
              {question.fig && (
                <div className="mb-4">
                  {Array.isArray(question.fig) ? (
                    question.fig.map((figSrc, figIndex) => (
                      <img
                        key={figIndex}
                        src={`${figSrc}`}
                        alt={`Figure for question ${j + 1} - ${figIndex + 1}`}
                        className="w-full h-auto mx-auto mb-2 cursor-pointer"
                        onClick={() => setZoomedImage(figSrc)}
                      />
                    ))
                  ) : (
                    <img
                      src={`${question.fig}`}
                      alt={`Figure for question ${j + 1}`}
                      className="w-full h-auto mx-auto cursor-pointer"
                      onClick={() => setZoomedImage(question.fig as string)}
                    />
                  )}
                </div>
              )}
              <div className="flex justify-between items-start w-full mb-4">
                <h3 className="card-title text-left flex-grow">
                  <span className="mr-2 font-bold">{j + 1}.</span>
                  <div className="px-2 py-1" style={{ fontSize: `${questionFontSize}px` }}>
                    <ReactMarkdown
                      remarkPlugins={[remarkGfm, remarkMath]}
                      rehypePlugins={[rehypeKatex]}
                    >{question.question}</ReactMarkdown>
                  </div>
                </h3>
                <div className="flex flex-col sm:flex-row sm:items-center gap-2">
                  {favoriteStates[j] && (
                    <select
                      className="select select-bordered select-sm w-full sm:w-auto sm:max-w-xs order-2 sm:order-1"
                      value={skillLevels[j] || DEFAULT_SKILL_LEVEL}
                      onChange={(e) => handleSkillLevelChange(j, question, e.target.value)}
                      onClick={(e) => e.stopPropagation()}
                    >
                      {SKILL_LEVEL_OPTIONS.map(option => (
                        <option key={option} value={option}>{option}</option>
                      ))}
                    </select>
                  )}
                  <button
                    onClick={() => {
                      handleFavoriteClick(j, question)
                    }}
                    className="btn btn-ghost btn-circle flex-shrink-0 self-end sm:self-auto order-1 sm:order-2"
                    aria-label="Favorite this question"
                  >
                    {favoriteStates[j] == true && <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className={`h-6 w-6 transition-colors duration-200 ${favoriteStates[j] ? 'text-yellow-400' : 'text-gray-400 hover:text-yellow-300'}`}
                      fill="currentColor"
                      viewBox="0 0 24 24">
                      <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                    </svg>}
                    {favoriteStates[j] != true && <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className={`h-6 w-6 transition-colors duration-200 text-gray-400 hover:text-yellow-300`}
                      fill="currentColor"
                      viewBox="0 0 24 24">
                      <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                    </svg>}
                  </button>
                </div>
              </div>
              <div className="flex flex-col gap-3">
                {question.options.map((option, k) => (
                  <button
                    key={`option-${k}`}
                    className={getButtonClasses(j, option, question.correct_answer)}
                    onClick={() => handleAnswerSelect(j, option)}
                    style={{ minHeight: `${questionFontSize * 2.5}px` }}
                  > 
                    <div className="markdown-content text-left w-full break-words px-2 py-1" style={{ fontSize: `${questionFontSize}px` }}>
                      <ReactMarkdown
                        remarkPlugins={[remarkGfm, remarkMath]}
                        rehypePlugins={[rehypeKatex]}
                      >{option}</ReactMarkdown>
                    </div>
                  </button>
                ))}
              </div>
              {getFeedbackMessage(j, question.correct_answer, JSON.stringify(question))}
            </div>
          );
        }) : null);
      })}

      {/* Add some global styles for mathematical content */}
      <style jsx global>{`
        .markdown-content {
          display: inline-block;
          width: 100%;
          line-height: 1.4;
        }
        
        /* Make sure math elements don't overflow */
        .katex-html {
          max-width: 100%;
          overflow-x: auto;
          overflow-y: hidden;
        }
        
        /* Adjust button height for multi-line content */
        .btn {
          height: auto;
          min-height: 3rem;
          white-space: normal;
          text-align: left;
          padding: 0.75rem 1rem;
        }
      `}</style>

      {submittedCount > 0 && (
        <div className="fixed bottom-5 right-5 bg-base-200/50 p-4 rounded-lg shadow-lg z-50 text-center w-32 backdrop-blur-sm">
          <h4 className="font-bold text-lg text-base-content">Score</h4>
          <div className="text-4xl font-bold text-primary">
            {scorePercentage.toFixed(0)}%
          </div>
          <div className="text-sm text-base-content/70">
            ({correctCount}/{submittedCount})
          </div>
        </div>
      )}

      {zoomedImage && (
        <div
          className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4"
          onClick={() => setZoomedImage(null)}
        >
          <img
            src={zoomedImage}
            alt="Zoomed figure"
            className="max-w-full max-h-full object-contain"
            onClick={(e) => e.stopPropagation()} // Prevent closing when clicking on the image itself
          />
        </div>
      )}
    </>
  );
}

export default QuestionComponent;



// import React from 'react';
// import ReactMarkdown from 'react-markdown';
// import remarkMath from 'remark-math';
// import rehypeKatex from 'rehype-katex';
// import { CloseButton } from '@/app/components/ui/CloseButton'

// // Define the question type
// type Question = {
//     question: string;
//     options: string[];
//     correct_answer: string;
//   };

// type AIResultType = [Question[]];


// function QuestionComponent({ ai_result}: { ai_result: AIResultType }) {
//   return (
//     <>
//       {ai_result.map((q, i) => {
//         console.log('hit');
//         if (i === 0) return (<p key={i}></p>);
//         console.log(ai_result);
//         console.log(q);
//     // if(aiResult != 'Your Results Will Appear Here') console.log(JSON.parse(aiResult));
//         return (q.map((question, j) => {
//           return (
//             <div className="p-10" key={`question-${j}`}>
//               <CloseButton>
//                 <h3>
//                   <ReactMarkdown
//                     remarkPlugins={[remarkMath]}
//                     rehypePlugins={[rehypeKatex]}
//                   >{question.question}</ReactMarkdown>
//                 </h3>
//                 <ol>
//                   {question.options.map((option, k) => (
//                     <li key={`option-${k}`}>
//                       <button>
//                         <ReactMarkdown
//                           remarkPlugins={[remarkMath]}
//                           rehypePlugins={[rehypeKatex]}
//                         >{option}</ReactMarkdown>
//                       </button>
//                     </li>
//                   ))}
//                 </ol>
//               </CloseButton>
//             </div>
//           );
//         }));
//       })}
//     </>
//   );
// }

// export default QuestionComponent;
