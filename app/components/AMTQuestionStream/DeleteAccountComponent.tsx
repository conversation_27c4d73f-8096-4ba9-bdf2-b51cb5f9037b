'use client'
import React from 'react';
import useModal from "@/app/components/ui/Modal4";
import useAdvancedTextInput3 from '@/app/hooks/ui/useAdvancedTextInput3';
import { deleteUser } from "@/app/db/dbCurry";

interface DeleteAccountComponentProps {
    onDeleteSuccess?: () => void;
    userId?: string;
}

export default function DeleteAccountComponent({ onDeleteSuccess, userId }: DeleteAccountComponentProps) {
    const [DeleteSuccessModal, deleteSuccessRef] = useModal();
    const [MustLoginModal, mustLoginRef] = useModal();
    const [DeleteConfirmModal, deleteConfirmRef] = useModal();
    const [DeleteFailedModal, deleteFailedRef] = useModal();

    const [getUserEmail, BasicUserEmail] = useAdvancedTextInput3({ prompt: "Enter Your Email" });
    const [getUserPassword, BasicPassword] = useAdvancedTextInput3({ prompt: "Password" });

    const handleDeleteAccount = () => {
        if (userId && userId !== '') {
            deleteConfirmRef.current?.showModal();
        } else {
            mustLoginRef.current?.showModal();
        }
    };

    const handleConfirmDelete = async () => {
        try {
            const result = await deleteUser(getUserEmail(), getUserPassword());
            
            if (result == 'you cannot delete a test account') {
                deleteFailedRef.current?.showModal();
            } else if (result.deletedCount == 1) {
                deleteConfirmRef.current?.close();
                deleteSuccessRef.current?.showModal();
                onDeleteSuccess?.();
                localStorage.clear()
            } else {
                deleteFailedRef.current?.showModal();
            }
        } catch (error) {
            console.error("Error deleting account:", error);
            deleteFailedRef.current?.showModal();
        }
    };

    return (
        <>
            <DeleteSuccessModal>
                <div className="p-6 prose bg-gray-900 rounded-lg text-white">
                    <h2 className="text-2xl font-bold text-green-400 mb-4">Delete Successful!</h2>
                    <div className="flex justify-end gap-3 mt-6">
                        <button 
                            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors shadow-md" 
                            onClick={() => deleteSuccessRef.current?.close()}
                        >
                            Close
                        </button>
                    </div>
                </div>
            </DeleteSuccessModal>

            <DeleteConfirmModal>
                <div className="p-6 prose bg-gray-900 rounded-lg">
                    <h2 className="text-2xl font-bold text-red-400 mb-4">Are You Sure You Want To Delete Your Account?</h2>
                    <p className="text-red-400 font-medium">This action cannot be undone.</p>
                    <div className="space-y-4 mt-6">
                        <BasicUserEmail />
                        <BasicPassword />
                        <div className="flex justify-end gap-3 mt-6">
                            <button
                                className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors shadow-md"
                                onClick={handleConfirmDelete}
                            >
                                Confirm Delete
                            </button>
                            <button
                                className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors shadow-md"
                                onClick={() => deleteConfirmRef.current?.close()}
                            >
                                Cancel
                            </button>
                        </div>
                    </div>
                </div>
            </DeleteConfirmModal>

            <DeleteFailedModal>
                <div className="p-6 prose bg-gray-900 rounded-lg text-white">
                    <h2 className="text-2xl font-bold text-red-400 mb-4">Failed to Delete Account</h2>
                    <p className="text-red-400 font-medium">Invalid credentials or account not found.</p>
                    <div className="flex justify-end gap-3 mt-6">
                        <button
                            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors shadow-md"
                            onClick={() => deleteFailedRef.current?.close()}
                        >
                            Try Again
                        </button>
                    </div>
                </div>
            </DeleteFailedModal>

            <MustLoginModal>
                <div className="p-6 prose bg-gray-900 rounded-lg text-white">
                    <h2 className="text-2xl font-bold text-yellow-400 mb-4">Login Required</h2>
                    <p className="text-yellow-400 font-medium">You must be logged in to delete your account.</p>
                    <div className="flex justify-end gap-3 mt-6">
                        <button
                            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors shadow-md"
                            onClick={() => mustLoginRef.current?.close()}
                        >
                            Close
                        </button>
                    </div>
                </div>
            </MustLoginModal>

            <button 
                className="w-full px-6 py-3 bg-red-600 hover:bg-red-700 text-white font-medium rounded-lg transition-colors shadow-lg"
                onClick={handleDeleteAccount}
            >
                Delete Account
            </button>
        </>
    );
}