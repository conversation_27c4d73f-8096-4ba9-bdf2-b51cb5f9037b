'use client'

import dynamic from 'next/dynamic'
import {use} from 'react'
import { useSearchParams } from 'next/navigation'

const AudioRecorder = dynamic(
  () => import('@/app/components/ai-transcriptions/AudioTranscription'),
  {
    ssr: false,
    loading: () => <div>Loading...</div> // Optional loading component
  }
);

const AudioRecorderExperimental = dynamic(
  () => import('@/app/components/ai-transcriptions/AudioTranscriptionExperimental'),
  {
    ssr: false,
    loading: () => <div>Loading...</div> // Optional loading component
  }
);



export default function AITranslationPage() {

// export default function AITranslationPage({searchParams}) {
  let myParams = useSearchParams()
  // let display = use(searchParams)
   let display = myParams?.get("display")
   console.log(display)
  if (display == "experimental") {

  // if (display.display == "experimental") {
    console.log('hit promo')
    return <AudioRecorderExperimental />;
  }
  return <AudioRecorder />;
}
