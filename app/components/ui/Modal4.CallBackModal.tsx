'use client'
import type { JSX } from "react";

import useModal from "@/app/components/ui/Modal4";
import { useState } from "react";


// export function useCallbackModal(): [JSX.Element, (title: string, message: string, callback: () => void) => void, () => void, React.RefObject<HTMLDialogElement>] {
export function useCallbackModal(): [JSX.Element, (title: string, message: string, callback: () => void) => void, () => void, React.Dispatch<React.SetStateAction<() => void>>] {

  const [CallbackModal, callbackRef] = useModal();
  const [callbackTitle, setCallbackTitle] = useState('');
  const [callbackMessage, setCallbackMessage] = useState('');
  const [confirmText, setConfirmText] = useState('Yes I want to Subscribe');
  const [cancelText, setCancelText] = useState('Cancel');

  const [callback, setCallback] = useState<() => void>(() => {});
  const [closecallback, setCloseCallback] = useState<() => void>(() => {});

  const CallbackModalComponent = (
    <CallbackModal>
      <div className="p-6 prose bg-gray-900 rounded-lg text-white">
        <h2 className="text-2xl font-bold text-blue-400 mb-4">{callbackTitle}</h2>
        <p className="text-white mb-6">{callbackMessage}</p>
        <div className="flex justify-end gap-3 mt-6">
          <button 
            className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors shadow-md" 
            onClick={() => {
              callback();
              callbackRef.current?.close();
            }}
          >
            {confirmText}
          </button>
          <button 
            className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors shadow-md" 
            onClick={() => {
              closecallback()
              callbackRef.current?.close()
            }}
          >
            {cancelText}
          </button>
        </div>
      </div>
    </CallbackModal>
  );

  const showCallbackModal = (title: string, message: string, callback: () => void, confirmText?: string, cancelText?: string, closecallback?: () => void) => {
    setCallbackTitle(title);
    setCallbackMessage(message);
    setCallback(() => callback);
    setCloseCallback(() => closecallback || (() => {}));
    setConfirmText(confirmText || 'Yes I want to Subscribe');
    setCancelText(cancelText || 'Cancel');
    callbackRef.current?.showModal();
  };

  const closeCallbackModal = () => {
    callbackRef.current?.close();
  };

  return [
    CallbackModalComponent,
    showCallbackModal,
    closeCallbackModal,
    setCallback,
    setConfirmText
  ]
}