"use client"
import Image from "next/image";
import Link from "next/link";


interface ImageComponentProps {
  images: { 
    src: string; 
    alt: string; 
    href: string; 
    questions: (string | { question: string; options: string[]; correct_answer: string; }[])[]; 
    type: string
  }[],
  onClick: (image: { 
    src: string; 
    alt: string; 
    href: string; 
    questions: (string | { question: string; options: string[]; correct_answer: string; }[])[]; 
  }) => void
}

export const ImageComponent: React.FC<ImageComponentProps> = ({ images, onClick  }) => {
  return (
    <div className="flex flex-wrap justify-center gap-4" >
      {images.map((image, index) => (
        // <Link key={index} href={image.href}>
        <div key = {image.type} className="bg-white">
          <Image
            key={index}
            src={image.src}
            alt={image.alt}
            width={300}
            height={200}
            onClick={() => onClick(image)}
          />
          <p>{image.alt}</p>
          <p>{image.type}</p>
          </div>
        // </Link>
      ))
      }
    </div >
  );
};

