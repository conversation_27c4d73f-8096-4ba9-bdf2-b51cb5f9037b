'use client'
import React, {  useRef } from 'react'
import { CloseButton } from '@/app/components/ui/CloseButton'
import { ComponentType, ReactNode } from 'react'


export default function useModal(){

  const modalRef = useRef<HTMLDialogElement>(null)

  const closeModal = () => {
    modalRef.current?.close();
  };

  const ModalComponent = ({children}: {children: ReactNode}) => ( 
    <>
      <dialog id="my_modal_1" ref={modalRef} className="modal">
        <div className="modal-box">
          <div className="modal-action">
            <CloseButton callback={closeModal}>
              <div>
                <div className="flex flex-col gap-2 ">
                  {children}
                </div>
              </div>
            </CloseButton>
          </div>
        </div>
      </dialog>
    </>
  )

  return [ModalComponent, modalRef] as const //preserves types

  // return {ModalComponent, modalRef}
}