"use client"

import { create } from "zustand";

// Define the finite states
type AuthState = "idle" | "loading" | "authenticated" | "error" | "loggedIn" | "logOut";

interface AuthStore {
    state: AuthState;
    loggedIn: () => void;
    // loginSuccess: () => void;
    // loginFailure: () => void;
    logOut: () => void;
}

// Zustand store with transition guards
const useAuthStore = create<AuthStore>((set, get) => ({
    state: localStorage.getItem('userId') ? "loggedIn" : "idle",

    loggedIn: () => {
        console.log('hit promo')
        // if (get().state === "idle") 
            set({ state: "loggedIn" });
        console.log('zustand set state to', get().state)
    }, 

    // loginSuccess: () => {
    //     if (get().state === "loading") set({ state: "authenticated" });
    // },

    // loginFailure: () => {
    //     if (get().state === "loading") set({ state: "error" });
    // },

    logOut: () => {
        console.log(get().state)
        if (get().state === "loggedIn") set({ state: "idle" });
    },
}));

export default useAuthStore;


// function LoginButton() {
//     const { state, login, loginSuccess, loginFailure, logout } = useAuthStore();

//     const handleLogin = async () => {
//         login(); // Transition to "loading"
//         try {
//             await new Promise((resolve) => setTimeout(resolve, 1000)); // Simulate API call
//             loginSuccess(); // Transition to "authenticated"
//         } catch {
//             loginFailure(); // Transition to "error"
//         }
//     };

//     return (
//         <div>
//             <p>Current State: {state}</p>
//             {state === "idle" && <button onClick={handleLogin}>Login</button>}
//             {state === "authenticated" && <button onClick={logout}>Logout</button>}
//             {state === "error" && <button onClick={handleLogin}>Retry</button>}
//         </div>
//     );
// }