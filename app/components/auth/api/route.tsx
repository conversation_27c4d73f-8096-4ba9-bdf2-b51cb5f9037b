import { Resend } from 'resend';
import resetemail from '@/app/components/auth/resetemail'

const resend = new Resend(process.env.RESEND_EMAIL_SERVICE);

export async function POST() {
  try {
    const { data, error } = await resend.emails.send({
      from: 'NoReply <<EMAIL>>',
      to: ['<EMAIL>'],
      subject: 'Hello world',
      html: resetemail
    });

    if (error) {
      return Response.json({ error }, { status: 500 });
    }

    return Response.json(data);
  } catch (error) {
    return Response.json({ error }, { status: 500 });
  }
}