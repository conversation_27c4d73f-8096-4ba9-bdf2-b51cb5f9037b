"use client";
import useAdvancedTextInput3 from "@/app/hooks/ui/useAdvancedTextInput3";
import useAdvancedTextInput4 from "@/app/hooks/ui/useAdvancedTextInput4";
import useModal from "@/app/components/ui/Modal4";
import { upsertUser, lookupUser, deleteUser, lookupEmailOnly, upsertFingerprint, lookupRecentFingerprint, lookupRecentIpAddress, upsertIpAddress } from "@/app/db/dbCurry";
import { debug } from "@/app/utils/debug";
import userStore from "@/app/components/auth/userStore";
import { text } from "@/app/components/auth/signIn.config";
import { useEffect, useState } from "react";
import { useSearchParams } from 'next/navigation'
import Link from "next/link";
import config from '@/config';
import { getFingerprintHash } from "@/app/services/fingerprint";
import { useGenericModal } from "@/app/hooks/ui/useGenericModal";
import { getIP } from "@/app/services/getIP";
import useLoading from "@/app/hooks/ui/Loading.v4";
export const dynamic = 'force-dynamic'

console.log(process.env.NODE_ENV);
export default function SignIn() {
  const searchParams = useSearchParams()
  const [signedInFromOtherApp, setSignedInFromOtherApp] = useState(() => searchParams.get('app'))
  const [emailValidate, email, BasicEmail] = useAdvancedTextInput4({ prompt: "Email", validationRegex: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, validationMessage: "Please enter a valid email." });
  const [passwordValidate, password, BasicPassword] = useAdvancedTextInput4({
    prompt: "Password", validationRegex: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[\W_]).{8,}$/, validationMessage: "Weak Password...", note: "Password must be at least 8 characters long and contain at least one uppercase letter, one lowercase letter, one number, and one special character.", hideText: true
  });
  const [SuccessModal, successRef] = useModal();
  const [AccountExistModal, accountExistRef] = useModal();
  const [ErrorModal, errorRef] = useModal();
  const [BadPassOrEmailModal, badPassOrEmailRef] = useModal();
  const [AccountCreatedModal, accountCreatedRef] = useModal();
  const [InvalidModal, invalidRef] = useModal();
  const [LogOutSuccessModal, logOutSuccessRef] = useModal();
  const [GenericModalComponent, showGenericModal, closeGenericModal, genericRef]    =  useGenericModal()
  const [setLoading, LoadingWrapper, LoadSuccess, LoadError] = useLoading()
  
  const loggedIn = userStore((state) => state.loggedIn);
  const logOut = userStore((state) => state.logOut);
  const state = userStore((state) => state.state);
  // const {ModalComponent:ModalComponent, modalRef:modalRef }   =  useModal()
  // const {ModalComponent: SuccessModal, modalRef:successRef }   =  useModal()

  //console.log("zustand", state) // prevent react optimization by using the state in a call
  console.log(signedInFromOtherApp)
  async function createUser() {
    const res = await fetch("/db/add-user", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        email: "<EMAIL>",
        password: "hhserrrcurepassword123",
      }),
    });

    const data = await res.json();
    console.log("Response:", data);
  }

  async function updateUser() {
    const res = await fetch("/db/api", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        dbName: "Next_JS_Portfolio",
        collectionName: "Users",
        filter: { email: "<EMAIL>" },
        update: { $set: { password: "hhnewparrssword123" } },
      }),
    });

    const data = await res.json();
    console.log("Response:", data);
  }

  type T = {
    emailString?: string | undefined;
    password?: string | undefined;
    buttonType: string;
  };
  async function handleClick({ ...args }: T) {
    let { emailString, password, buttonType } = args;
    console.log(email())
    console.log(emailString, password, buttonType);

    if (typeof emailString == "string"  && typeof password == "string" && emailString != "" && password != "") {
      emailString = emailString.toLowerCase();
      //
      setLoading("on")
      console.log(email())
      let lookup = await lookupUser(emailString, password)
      console.log(email())
      console.log("lookupUserResult:", lookup)
      switch (buttonType) {
        case "Log Out":
          localStorage.removeItem("userId");
          logOut();
          setLoading("off")
          logOutSuccessRef.current?.showModal();
          break;
        case "Login":
          try {
            //emailValidate()
            //passwordValidate()
            console.log(lookup)
            if (lookup != null) {
              console.log(lookup?._id)
              localStorage.setItem('userId', lookup?._id.toString())
              console.log(email())
              let fingerprintHash = await getFingerprintHash()
              upsertFingerprint(emailString, fingerprintHash)
              console.log(emailString)
              console.log(email())
              upsertIpAddress(emailString, await getIP())
              console.log(email())
              loggedIn();
              setLoading("off")
              successRef.current?.showModal();
            } else {
              setLoading("off")
              badPassOrEmailRef.current?.showModal();
            }
            break;
          }
          catch {
            //if email validation fails the box turns red 
            break
          }

        case "Sign Up":
          let trick = email()
          console.log(trick)
          console.log(emailString)
          try {
            setLoading("on")
            emailValidate()
            //passwordValidate()
          }
          catch {
            setLoading("off")
            break
          }
          lookup = await lookupEmailOnly(emailString)
          console.log(lookup); //null if emailString doesnt exist cuz we only want to signup the account if the email not present
          if (lookup == null) {
            let currentFingerPrint = await getFingerprintHash()
            let currentIP = await getIP()
            try {
               await lookupRecentFingerprint(currentFingerPrint)
               await lookupRecentIpAddress(currentIP)
            }
            catch(error){
              setLoading("off")
              showGenericModal(error.message)
              break
            }
            setLoading("on")
            let upsertUserResult = await upsertUser(emailString, password);
            upsertFingerprint(emailString, currentFingerPrint)
            upsertIpAddress(emailString, await getIP())
            console.log(upsertUserResult)
            console.log(upsertUserResult.upsertedCount)
            console.log(upsertUserResult.upsertedId)
            if (upsertUserResult.upsertedCount == 1) {
              localStorage.setItem('userId', upsertUserResult.upsertedId.toString())
              setLoading("off")
              loggedIn();
              accountCreatedRef.current?.showModal();
            }
          }
          else {
            setLoading("off")
            accountExistRef.current?.showModal();
          }
          break;
      }
      window.__myComponentState = { emailString };
      // debug(result);
    }
  }

  useEffect(() => {
    try {
      let currentUserId = localStorage?.getItem('userId')
      console.log(currentUserId)
      if (currentUserId != null) {
        loggedIn();
      }
    }
    catch (e) {
      console.log(e)
    }
  }, [])

  const successModal = (
    <SuccessModal>
      {/* This becomes the modal content */}
      <div className="p-4 prose">
        <h2>Login Successful!</h2>
        {/* <>{[<p>i didnt know this would work</p>, <p>cool</p>]}</> */}
        <div className="flex justify-end gap-2 mt-4">
          <button className="btn" onClick={() => successRef.current?.close()}>
            Close
          </button>
        </div>
      </div>
    </SuccessModal>
  );

  const accountExistModal = (
    <AccountExistModal>
      {/* This becomes the modal content */}
      <div className="p-4 prose">
        <h2>Account Already Exists</h2>
        <p>Do you want to continuee?</p>
        {/* <>{[<p>i didnt know this would work</p>, <p>cool</p>]}</> */}
        <div className="flex justify-end gap-2 mt-4">
          <button
            className="btn"
            onClick={() => accountExistRef.current?.close()}
          >
            Cancel
          </button>
          <button
            className="btn bg-blue-500 text-white"
            onClick={() => {
              accountExistRef.current?.close()
              handleClick({
                emailString: email(),
                password: password(),
                buttonType: "Login",
              })
            }}
          >
            Confirm
          </button>
        </div>
      </div>
    </AccountExistModal>
  );

  const somethingWentWrongModal = (
    <ErrorModal>
      <div className="p-4 prose">
        <h2>Something Went Wrong</h2>
        <div className="flex justify-end gap-2 mt-4">
          <button className="btn" onClick={() => errorRef.current?.close()}>
            Try Again
          </button>
        </div>
      </div>
    </ErrorModal>
  );

  const badPassOrEmailModal = (
    <BadPassOrEmailModal>
      <div className="p-4 prose">
        <h2>UserName or Password Was Incorrect</h2>
        <div className="flex justify-end gap-2 mt-4">
          {/* <button className='btn' onClick={() => sendResetEmail()}> 
            Reset Password
          </button> */}
          <button
            className="btn bg-green-800 text-white"
            onClick={() => badPassOrEmailRef.current?.close()}
          >
            Try Again
          </button>
        </div>
      </div>
    </BadPassOrEmailModal>
  );

  const logOutSuccessModal = (
    <LogOutSuccessModal>
      <div className="p-4 prose">
        <h2>Log Out Successful!</h2>
        <div className="flex justify-end gap-2 mt-4">
          <button
            className="btn"
            onClick={() => logOutSuccessRef.current?.close()}
          >
            Close
          </button>
        </div>
      </div>
    </LogOutSuccessModal>
  );

  const accountCreatedModal = (
    <AccountCreatedModal>
      <div className="p-4 prose">
        <h2>Account Created!</h2>
        <div className="flex justify-end gap-2 mt-4">
          <button
            data-testid="accountclose"
            className="btn"
            onClick={() => {
              // loggedIn();
              accountCreatedRef.current?.close();
            }
            }
          >
            Close
          </button>
        </div>
      </div>
    </AccountCreatedModal>
  );

  return (
    <>
      {accountCreatedModal}
      {successModal}
      {accountExistModal}
      {somethingWentWrongModal}
      {badPassOrEmailModal}
      {logOutSuccessModal}
      {GenericModalComponent}      
      <div className="flex flex-col justify-center items-center h-screen gap-5 text-white bg-black p-5">
        <h2 className="text-3xl">Sign In</h2>
        {state == "idle" &&
              <LoadingWrapper>

          <div className="flex flex-col gap-5">
            <div className="text-black">
              <BasicEmail />
            </div>
            <div className="text-black">
              <BasicPassword />
            </div>
          </div>
          </LoadingWrapper>

        }
        <div className="flex gap-5">
          {state == "idle" && (
            <button
              className="btn"
              onClick={() =>
                handleClick({
                  emailString: email(),
                  password: password(),
                  buttonType: "Login",
                })
              }
            >
              {text.login}
            </button>
          )}
          {state == "loggedIn" && (
            <button
              className="btn"
              onClick={() =>
                handleClick({
                  emailString: 'N/A',
                  password: 'N/A',
                  buttonType: "Log Out",
                })
              }
            >Log Out</button>
          )}
          {state != "loggedIn" && <button
            className="btn"
            onClick={() =>
              handleClick({
                emailString: email(),
                password: password(),
                buttonType: "Sign Up",
              })
            }
          >
            {text.signUp}
          </button>}
        </div>
        {console.log('frontend email() consolelog', email())}
        {email() == '<EMAIL>'
          && signedInFromOtherApp != 'AMTapp'
          && <Link href={config.apps.aiTranslation.url + '?display=experimental'}>
            <button className='btn bg-green-700 text-white'>Go Back to App</button>
          </Link>}
        {email() != '<EMAIL>' 
        && signedInFromOtherApp != 'AMTapp'
        && <Link href={config.apps.aiTranslation.url}>
          <button className='btn bg-green-700 text-white'>Go Back to App</button>
        </Link>}
        {signedInFromOtherApp == 'AMTapp'
        && <Link href={config.apps["A&P App"].url}>
          <button className='btn bg-green-700 text-white'>Go Back to App</button>
        </Link>}
      </div>

    </>
  );
}
