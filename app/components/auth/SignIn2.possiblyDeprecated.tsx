"use client";

import useAdvancedTextInput3 from "@/app/hooks/ui/useAdvancedTextInput3";
import useModal from "@/app/components/ui/Modal4";
import { upsertUser, lookupUser, deleteUser } from "@/app/db/dbCurry";
import { debug } from "@/app/utils/debug";
import userStore from "@/app/components/auth/userStore";
import { text } from "@/app/components/auth/signIn.config";
import { User } from "@/app/(main site)/user";
import { useEffect } from "react";

console.log(process.env.NODE_ENV);
export default function SignIn() {
  const [email, BasicEmail] = useAdvancedTextInput3({ prompt: "Email" });
  const [password, BasicPassword] = useAdvancedTextInput3({
    prompt: "Password",
  });
  const [SuccessModal, successRef] = useModal();
  const [AccountExistModal, accountExistRef] = useModal();
  const [ErrorModal, errorRef] = useModal();
  const [BadPassOrEmailModal, badPassOrEmailRef] = useModal();
  const [AccountCreatedModal, accountCreatedRef] = useModal();
  const [InvalidModal, invalidRef] = useModal();
  const [LogOutSuccessModal, logOutSuccessRef] = useModal();
  const loggedIn = userStore((state) => state.loggedIn);
  const state = userStore((state) => state.state);
  
  // const {ModalComponent:ModalComponent, modalRef:modalRef }   =  useModal()
  // const {ModalComponent: SuccessModal, modalRef:successRef }   =  useModal()
  console.log(state) // prevent react optimization by using the state in a call


 
  async function createUser() {
    const res = await fetch("/db/add-user", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        email: "<EMAIL>",
        password: "hhserrrcurepassword123",
      }),
    });

    const data = await res.json();
    console.log("Response:", data);
  }

  async function updateUser() {
    const res = await fetch("/db/api", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        dbName: "Next_JS_Portfolio",
        collectionName: "Users",
        filter: { email: "<EMAIL>" },
        update: { $set: { password: "hhnewparrssword123" } },
      }),
    });

    const data = await res.json();
    console.log("Response:", data);
  }

  type T = {
    email: string | undefined;
    password: string | undefined;
    buttonType: string;
  };
  async function handleClick({ ...args }: T) {
    let { email, password, buttonType } = args;
    console.log(email, password, buttonType);

    if (typeof email == "string" && typeof password == "string") {
      switch (buttonType) {
        case "login":
            successRef.current?.showModal();
          break;
        case "Sign Up":
          let lookup = null
          // let lookup = await lookupUser(email);
          console.log(lookup); //null if email doesnt exist
          if (lookup == null) {
            // let upsertUserResult = await upsertUser(email, password);
            // console.log(upsertUserResult);
            
            accountCreatedRef.current?.showModal();
          }
          break;
      }
      window.__myComponentState = { email };
      // debug(result);
    }
  }

  const successModal = (
    <SuccessModal>
      {/* This becomes the modal content */}
      <div className="p-4 prose">
        <h2>Login Successful!</h2>
        {/* <>{[<p>i didnt know this would work</p>, <p>cool</p>]}</> */}
        <div className="flex justify-end gap-2 mt-4">
          <button className="btn" onClick={() => successRef.current?.close()}>
            Close
          </button>
        </div>
      </div>
    </SuccessModal>
  );

  const accountExistModal = (
    <AccountExistModal>
      {/* This becomes the modal content */}
      <div className="p-4 prose">
        <h2>Account Already Exists</h2>
        <p>Are you sure you want to continuee?</p>
        {/* <>{[<p>i didnt know this would work</p>, <p>cool</p>]}</> */}
        <div className="flex justify-end gap-2 mt-4">
          <button
            className="btn"
            onClick={() => accountExistRef.current?.close()}
          >
            Cancel
          </button>
          <button
            className="btn bg-blue-500 text-white"
            onClick={() => handleClick()}
          >
            Confirm
          </button>
        </div>
      </div>
    </AccountExistModal>
  );

  const somethingWentWrongModal = (
    <ErrorModal>
      <div className="p-4 prose">
        <h2>Something Went Wrong</h2>
        <div className="flex justify-end gap-2 mt-4">
          <button className="btn" onClick={() => errorRef.current?.close()}>
            Try Again
          </button>
        </div>
      </div>
    </ErrorModal>
  );

  const badPassOrEmailModal = (
    <BadPassOrEmailModal>
      <div className="p-4 prose">
        <h2>UserName or Password Was Incorrect</h2>
        <div className="flex justify-end gap-2 mt-4">
          <button
            className="btn"
            onClick={() => badPassOrEmailRef.current?.close()}
          >
            Try Again
          </button>
        </div>
      </div>
    </BadPassOrEmailModal>
  );

  const logOutSuccessModal = (
    <LogOutSuccessModal>
      <div className="p-4 prose">
        <h2>UserName or Password Was Incorrect</h2>
        <div className="flex justify-end gap-2 mt-4">
          <button
            className="btn"
            onClick={() => badPassOrEmailRef.current?.close()}
          >
            Try Again
          </button>
        </div>
      </div>
    </LogOutSuccessModal>
  );

  const accountCreatedModal = (
    <AccountCreatedModal>
      <div className="p-4 prose">
        <h2>Account Created!</h2>
        <div className="flex justify-end gap-2 mt-4">
          <button
            className="btn"
            onClick={() => {
                loggedIn();
                accountCreatedRef.current?.close();
              } 
            }
          >
            Close
          </button>
        </div>
      </div>
    </AccountCreatedModal>
  );

  return (
    <div className="flex flex-col justify-center items-center h-screen gap-5 text-white bg-black p-5">
      <h2 className="text-3xl">Sign In</h2>
      <div className="text-black">
        <BasicEmail />
      </div>
      <div className="text-black">
        <BasicPassword />
      </div>
      <div className="flex gap-5">
        {state == "idle" && (
          <button
            className="btn"
            onClick={() =>
              handleClick({
                email: email(),
                password: password(),
                buttonType: "login",
              })
            }
          >
            {text.login}
          </button>
        )}
        {state == "loggedIn" && (
          <button
            className="btn"
            onClick={() =>
              handleClick({
                email: email(),
                password: password(),
                buttonType: "logOut",
              })
            }
          >
            Log Out
          </button>
        )}
        <button
          className="btn"
          onClick={() =>
            handleClick({
              email: email(),
              password: password(),
              buttonType: "Sign Up",
            })
          }
        >
          {text.signUp}
        </button>
      </div>
      {accountCreatedModal}
      {successModal}
    </div>
  );
}
