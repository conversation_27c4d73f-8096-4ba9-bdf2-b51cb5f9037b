'use server'

import { Resend } from 'resend';
import resetemail from '@/app/components/auth/resetemail'

const resend = new Resend(process.env.RESEND_EMAIL_SERVICE);

export async function sendResetEmail() {
  try {
    const { data, error } = await resend.emails.send({
      from: 'NoReply <<EMAIL>>',
      to: ['<EMAIL>'],
      subject: 'Voice App Password Reset',
      html: resetemail
    });

    if (error) {
      return "There was a Propblem Sending the Email" + error;
    }

    return (data);
  } catch (error) {
    return "There was a Propblem Sending the Email" + error;
  }
}