


# concurrently script
#!/bin/bash

TEMP_FILE="/tmp/fingerprint_result.txt"

finger_print_test() {

echo "Collecting Finger Print"
# Capture the test output
jest __tests__/fingerprint.test.ts --testEnvironment=jsdom -t 'Fingerprint Service' --forceExit > "$TEMP_FILE"

# Extract the hash value from the output
export HASH_VALUE=$(grep "HASH_VALUE=" "$TEMP_FILE" | cut -d'=' -f2)

echo "Captured hash value: $HASH_VALUE"

# Now you can use $HASH_VALUE in subsequent tests
echo "Saving Finger Print into Mongo DB"
HASH_VALUE="$HASH_VALUE" jest __tests__/curryAPI.test.ts --testEnvironment=node -t 'Save Fingerprint' --forceExit

echo "All tests completed successfully!"

# Clean up
rm -f "$TEMP_FILE"
}