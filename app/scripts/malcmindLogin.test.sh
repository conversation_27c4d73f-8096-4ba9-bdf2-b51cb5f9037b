


# concurrently script
#!/bin/bash

export MY_EMAIL="<EMAIL>"
export MY_PASSWORD="123"1
export INCORRECT_PASSWORD="#$%$#@$%"
export FORCE_FAIL_PASSWORD="123"




run_spec_test() {

# Check if port 3000 is in use
if ! netstat -tuln | grep -q ":3000 "; then
  echo "Port 3000 is not in use. Starting the server..."
  bun run dev2 &
  sleep 20  # Wait a few seconds to ensure the server starts
else
  echo "Port 3000 is already in use."
fi

echo "Clearning Old Db Data"
jest __tests__/curryAPI.test.ts --testEnvironment=node -t 'clear user' --forceExit

echo "Starting Playwright 'normal signup test'"
playwright test signin.spec.ts  --headed -g 'normal signup test' || { echo "Playwright tests failed"; exit 1; }
playwright test signin.spec.ts  --headed -g 'Bad Email SignUp' || { echo "Playwright tests failed"; exit 1; }

# playwright test signin.spec.ts  -g 'normal signup test' || { echo "Playwright tests failed"; exit 1; }


# echo "Starting Playwright 'normal signout test'"
# playwright test signin.spec.ts --headed -g 'logout test' || { echo "Playwright tests failed"; exit 1; }

echo "All tests completed successfully!"

# parallel script 

# echo "Starting Playwright tests..."
# playwright test signin.spec.ts &   # Run Playwright test in the background

# echo "Starting Jest API tests..."
# jest __tests__/curryAPI.test.jsx --testEnvironment=node &   # Run Jest test in the background

# wait  # Wait for both background processes to finish

# echo "All tests completed successfully!"

}


