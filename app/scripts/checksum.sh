#!/bin/bash

#New
CHECKSUM_FILE="app/scripts/.file_checksums.md5"


# Function to calculate checksums and detect changes  - New
check_for_changes() {
  local changes_detected=false
  local files_to_watch=("$@")
  local checksum_file="${CHECKSUM_FILE}_$(echo "${files_to_watch[*]}" | md5sum | cut -d' ' -f1)"
  
  # Create a temporary file for new checksums
  temp_checksums=$(mktemp)
  
  for file in "${files_to_watch[@]}"; do
    if [[ -f "$file" ]]; then
      md5sum "$file" >> "$temp_checksums"
    fi
  done
  
  # Check if checksums file exists
  if [[ ! -f "$checksum_file" ]]; then
    echo "First run detected, creating initial checksums."
    cp "$temp_checksums" "$checksum_file"
    changes_detected=false
  else
    # Compare checksums to detect changes
    changed_files=$(diff "$checksum_file" "$temp_checksums" | grep "^<\|^>" | cut -d' ' -f3-)
    
    if [[ -n "$changed_files" ]]; then
      echo "Changes detected in the following files:"
      echo "$changed_files"
      changes_detected=true
      
      # Update checksums file
      cp "$temp_checksums" "$checksum_file"
    else
      echo "No changes detected."
    fi
  fi
  
  # Clean up temp file
  rm "$temp_checksums"
  
  return $([ "$changes_detected" = true ] && echo 0 || echo 1)
}
