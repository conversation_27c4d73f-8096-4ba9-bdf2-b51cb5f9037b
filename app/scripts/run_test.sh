#!/bin/bash


source app/scripts/malcmindLogin.test.sh
source app/scripts/fingerPrint.test.sh
source app/scripts/checksum.sh


# Create an array of files to watch - New
files_to_watch=(
  "tests/signin.spec.ts"
  "app/components/ai-transcriptions/AudioTranscription.tsx"
)

fingerprint_files_to_watch=(
  "__tests__/fingerprint.test.ts"
  "app/services/fingerprint.ts"
)

check_for_changes "${files_to_watch[@]}"
login_changes=$?
echo "Return value: $login_changes"

# Main execution  - New
if check_for_changes "${files_to_watch[@]}"; then
  echo "Running login script due to changes..."
  run_spec_test
  # $SCRIPT_TO_RUN
else
  echo "No action needed."
fi
if check_for_changes "${fingerprint_files_to_watch[@]}"; then
  echo "Running fingerprint script due to changes..."
  finger_print_test
else
  echo "No action needed."
fi
  
# run_spec_test
# finger_print_test