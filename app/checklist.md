On handlepay we are purposefully going to disable the check to ask for payment when open on desktop to make development easier

http://localhost:3000/Web-Apps/ai-article-generator-new  is currently taken appart and i need to add a button to hit the different chapter content




6/14/2025 - 6/15/2025

 Fixed issue with transcribe not working on first click. There was a logic issue that did not account for the trial period. 
`let isAllowedtoTranscribe = trialPeriod || didUserPay`   fixed it

Atempting fix for You need to subscribe modal appearing after the user already subscribe by reorginizing the modal structure

set this from false back to true   didUserPay = !isOnMobile


- fixed the styling of the subscribe modal

- fixed types on dbcurry

-  updated the audio transcription modal to state the user can transcribe 3 per day

- added the ability to deduct credits

-implement a grey theme into  AICHATBOX.Voiceapp.tsx   "theme dark"


- on aichatbox.tsx chans this line to this
                                    <div className="p-10">


                                    <div className="p-10 bg-gray-700">


or possibly add a color class on maincontenttemplate.tsx

    if (theme == 'dark') return (
        <div data-theme="black" className='p-9  flex-col  md:flex md:flex-row md:overflow-visible items-center justify-evenly overflow-y-hidden overflow-x-hidden'>

-br



- [ ] I want to show a warning modal that says "Are you sure youy want to Permenantly Delete This audio"?

- Confirm -   - Cancel - 

- [ ] make a modal for warning you that your about to delete an audio file
- [ ] put that modal into a different file

```js
    showCallbackModal(
        'Transcription Credits',
        'You Need to Subscribe to Transcribe Messages. You get 3 free transcriptions per day. Please wait 24 hours or subscribe to continue.',
        () => {
          handlePayment()
        },
      )
```
- [ ] Unchecked item
- [x] Checked item

- [x] Edit show Call Back Moal File so that we have a way to edit the Confim text
- [x] Make a prop so that we can call that setConfirmtext
- [x] Test that set Confirm Test doesnt jitter on Modal4.CallBackModal.tsx
- [x] In Settings make a button to enter dev mode and make it only <NAME_EMAIL> or 690d75369547d4dbacd72cfe
    - [ ] this is a vector for attack we need to put 690d75369547d4dbacd72cfe into the backend eventually
    - [x] When you enter Devmode you get a toggle switch that lets you simulate that you ran out of credits "On" or "Off"
- [ ] We need to Deprocate Component  AiArticleGenerator  for AiChatBox  on pages /ai-article-generator and delete /ai-article-generator-new page (which already uses AiChatBox)
- [x] Added Pictures to AMTAPP
- [x] Made it so the Home page of AMTAPP shows corrosion questions by default
- [x] Navigate clicking on amt picture and navigates to the  appropriate content
- [x] /Web-Apps/ai-article-generator-new deleted   now we need to work on swaping all AIArticleGenerator usages for AIChatBox
- [x] fixed QuestionComponent AIResultType types for typescript
- [ ] figure out the difference between data-drivien development/design and single sorce of truth ssot
- [ ]swap all AIArticleGenerator @/app/(main site)/(Landing Pages)/ai-article-generator/AiArticleGenerator usages for AIChatBox
- 6/19/2025 added dev mode toggle button for  simulating active or non active subscription
- [x] added AMT app home page and restructructured a few components
- [x] made AMT app look more like stitch designs with the associated code

- [x] need to add a back button to AMT app
- [x] I need to add  back Button on AirCraft Maintenance Subjects to work
- [ ] get rid of the stupid x's in the question generation
- [ ] work on Resend email service RESEND_EMAIL_SERVICE
- [x] i need to get pictures into the questions
- [x] clean table
- [ ] do 8 and 9 uploads on tuesday for the maintenance sections
- [ ] do 10 and 11 uploads on thursday
- [x] get the select menu working for the quiz type
- [ ] fix the types for all my new code
- [x] get the "explain button working"
- [x] updated figures and questions
- [ ] integrate voice app
  - [x] get voice app meta data to work
  - [x] get voice app home menu to work
  - [ ] register voice app with 
  - [] AMT APP
    - [x] Added privacy policy @ /privacy-pages/faa-app which mimicked https://voiceapp.malcmind.com/Web-Apps/ai-translation/privacy-policy
    - [ ] make the sign in button less confusing.... when they click signup the title still says sign in..
    - [x] Make the AMT get Started/sign in page go to http://faa-app.localhost:3000/malcmind-login
    - [ ] Once they sign in, it will redirect them back to faa-app.localhost:3000 Aircraft Maintenance Subjects
    - [x] if they are already logged in and hit the page from fresh Instead of `Get Started`  it will say `Continue`
    - [x] make the button not flash 'Get Started' then 'Continue' when reading the user name
    - [x] added a logout button and hid 'have an account? sign in " message if the user is alreadysigned in [40 min]
    - [x] clicking the home button goes back to home [40 min - 26min]
    - [x] upload test account to play store [20 min - 7 min]
    - [ ] make a delete account button that works [40 min]
    - [x] when  the  user clicks profile i want   it  to say settings instead [20 min - 1hr 30]
    - [x] when the user clicks settings i want it to have 'sign out' privacy policy' and 'delete account buttons'
    - [x] center the settings menu
    - [x] delete button works at https://faa-app.malcmind.com/display=delete-account
  -[ ] Audio Transctiption
    - [x] made the delete account button have black text instead of white text for better visibility
    - [ ] merge AudioTranscription.tsx into july update




## Good Pages
- http://localhost:3000/malcmind-login?app=audiorecorder  
  - [text](components/auth/SignIn.tsx)
  - [text](malcmind-login/page.tsx)
  - [AudioTranscription.tsx](components/ai-transcriptions/AudioTranscription.tsx)
  - [text](<(main site)/useAudioRecorder.tsx>) == [text](vscode-remote://wsl%2Bubuntu/home/<USER>/nextjs-flask/app/%28main%20site%29/%28Landing%20Pages%29/Web-Apps/ai-translation/useAudioRecorder.tsx)
  -[text](components/ai-chat/AIChatBox.VoiceAPPSavedRecordingView.tsx)
  -[text](components/ai-chat/AIChatBox.tsx)
  - [GoogleAuth](components/ai-transcriptions/acknowledge.ts)
  - [digitalServiceDetector](utils/digitalServiceDetector.ts)
  - [devModeToggle](components/ai-transcriptions/internalComponents/DevModeToggle.tsx)
- [text](db/dbCurry.ts)
[text](components/ai-chat/AIChatBox.tsx)
[text](components/ai-chat/AIChatBox.VoiceAPP.tsx)
[text](components/ai-chat/AIChatBox.VoiceAPPSavedRecordingView.tsx)
[text](utils/digitalServiceDetector.ts)
[text](components/ai-transcriptions/AudioTranscription.tsx)

  [ ] - uninstalled "node-pty": "^1.0.0"  to fix docker. reinstall if needed

  docker build -t my-app .  # builds docker file thats in the root of your project. -t flag assigns the name my app.   the "." tells docker to look for the file in the current directory.

docker run -p 3000:3000 my-app  # runs the docker image





That is still too much. On the phone. And in the mail that's not the amount they sent. They closed my account on the phone with a completely different amount.  This amount you're sending me is still almost doubled.  The same way I cant go back in time and say, " well I didnt use emerchant services  all year give me the money back" your trying to tell me that they can cancel with one amount on the phone, then tell me a different amount later. 


Fundamentals of Electricity and Electronics
Aircraft Drawings
Weight and Balance
Fluid Lines and Fittings
Aircraft Materials, Hardware and Processes
Ground Operations and Servicing
Cleaning and Corrosion Control
Mathematics
Regulations, Maintenance Forms, Records and Publications
Physics for Aviation
Inspection Concepts and Techniques
Human Factors