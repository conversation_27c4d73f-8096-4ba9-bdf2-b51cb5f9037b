'use client'

import SignIn from "@/app/components/auth/SignIn"
import useModal from "@/app/components/ui/Modal4";

export default function malcMindLogin() {
    // Create a modal with the hook
    const { ModalComponent, modalRef } =  useModal()
   
    const openModalProgrammatically = () => {
        modalRef.current?.showModal();
      };

    const myModal = (
        <ModalComponent>
        {/* This becomes the modal content */}
        <div className="p-4 prose">
            <h2>Account Already Exists</h2>
            <p>Are you sure you want to continuee?</p>
            {/* <>{[<p>i didnt know this would work</p>, <p>cool</p>]}</> */}
            <div className="flex justify-end gap-2 mt-4">
                <button className="btn" onClick={() => modalRef.current?.close()}>Cancel</button>
                <button className="btn bg-blue-500 text-white">Confirm</button>
            </div>
        </div>
    </ModalComponent>
    )

    return (
        <>
            <button className="btn" onClick={openModalProgrammatically}>Open Modal</button>
            {myModal}
            <SignIn />
        </>
    );
}
