<!DOCTYPE html>
<html lang="en">
<head>
   <meta charset="UTF-8">
   <meta name="viewport" content="width=device-width, initial-scale=1.0">
   <title>Horizon - Modern Digital Solutions</title>
   <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
   <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
   <script src="https://unpkg.com/babel-standalone@6/babel.min.js"></script>
   <script src="https://cdn.tailwindcss.com"></script>
   <link href="https://cdn.jsdelivr.net/npm/daisyui@3.8.0/dist/full.css" rel="stylesheet">
   <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
   <div id="root"></div>
   
   <script type="text/babel">
       function App() {
           const [menuOpen, setMenuOpen] = React.useState(false);
           const [darkMode, setDarkMode] = React.useState(false);
           
           React.useEffect(() => {
               const darkModeMedia = window.matchMedia('(prefers-color-scheme: dark)');
               setDarkMode(darkModeMedia.matches);
               
               const handleChange = (e) => {
                   setDarkMode(e.matches);
               };
               
               darkModeMedia.addEventListener('change', handleChange);
               return () => darkModeMedia.removeEventListener('change', handleChange);
           }, []);
           
           return (
               <div className={`min-h-screen ${darkMode ? 'bg-gray-900 text-white' : 'bg-white text-gray-800'}`}>
                   {/* Header */}
                   <header className={`sticky top-0 z-50 ${darkMode ? 'bg-gray-900' : 'bg-white'} shadow-md`}>
                       <div className="container mx-auto px-4 sm:px-6 lg:px-8">
                           <div className="flex justify-between items-center py-4">
                               <div className="flex items-center">
                                   <div className="flex-shrink-0">
                                       <span className="text-3xl font-bold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-violet-500 to-indigo-600">
                                           Horizon
                                       </span>
                                   </div>
                               </div>
                               
                               {/* Desktop Navigation */}
                               <nav className="hidden md:flex space-x-10">
                                   <a href="#" className="font-medium hover:text-indigo-500 transition duration-300">Home</a>
                                   <a href="#" className="font-medium hover:text-indigo-500 transition duration-300">Services</a>
                                   <a href="#" className="font-medium hover:text-indigo-500 transition duration-300">About</a>
                                   <a href="#" className="font-medium hover:text-indigo-500 transition duration-300">Portfolio</a>
                                   <a href="#" className="font-medium hover:text-indigo-500 transition duration-300">Contact</a>
                               </nav>
                               
                               <div className="flex items-center">
                                   <button
                                       onClick={() => setDarkMode(!darkMode)}
                                       className={`mr-6 p-1 rounded-full ${darkMode ? 'bg-gray-800' : 'bg-gray-100'}`}
                                   >
                                       {darkMode ? 
                                           <i className="fas fa-sun text-yellow-400"></i> : 
                                           <i className="fas fa-moon text-gray-600"></i>
                                       }
                                   </button>
                                   
                                   <button 
                                       className="bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition duration-300 hidden md:block"
                                   >
                                       Get Started
                                   </button>
                                   
                                   {/* Mobile menu button */}
                                   <button
                                       type="button"
                                       className="md:hidden p-2 rounded-md focus:outline-none"
                                       onClick={() => setMenuOpen(!menuOpen)}
                                   >
                                       <svg 
                                           xmlns="http://www.w3.org/2000/svg" 
                                           fill="none" 
                                           viewBox="0 0 24 24" 
                                           stroke="currentColor" 
                                           className="h-6 w-6"
                                       >
                                           <path 
                                               strokeLinecap="round" 
                                               strokeLinejoin="round" 
                                               strokeWidth={2} 
                                               d={menuOpen ? "M6 18L18 6M6 6l12 12" : "M4 6h16M4 12h16M4 18h16"} 
                                           />
                                       </svg>
                                   </button>
                               </div>
                           </div>
                       </div>
                       
                       {/* Mobile menu */}
                       <div className={`md:hidden ${menuOpen ? 'block' : 'hidden'} ${darkMode ? 'bg-gray-900' : 'bg-white'} shadow-md`}>
                           <div className="px-4 pt-2 pb-3 space-y-1">
                               <a href="#" className="block px-3 py-2 rounded-md font-medium hover:bg-indigo-500 hover:text-white transition duration-300">Home</a>
                               <a href="#" className="block px-3 py-2 rounded-md font-medium hover:bg-indigo-500 hover:text-white transition duration-300">Services</a>
                               <a href="#" className="block px-3 py-2 rounded-md font-medium hover:bg-indigo-500 hover:text-white transition duration-300">About</a>
                               <a href="#" className="block px-3 py-2 rounded-md font-medium hover:bg-indigo-500 hover:text-white transition duration-300">Portfolio</a>
                               <a href="#" className="block px-3 py-2 rounded-md font-medium hover:bg-indigo-500 hover:text-white transition duration-300">Contact</a>
                               <div className="px-3 py-2">
                                   <button 
                                       className="w-full bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700 transition duration-300"
                                   >
                                       Get Started
                                   </button>
                               </div>
                           </div>
                       </div>
                   </header>
                   
                   {/* Hero Section */}
                   <section className="pt-16 md:pt-20 pb-20 md:pb-28">
                       <div className="container mx-auto px-4 sm:px-6 lg:px-8">
                           <div className="flex flex-col md:flex-row items-center">
                               <div className="md:w-1/2">
                                   <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight mb-6">
                                       Modern Solutions for the
                                       <span className="bg-clip-text text-transparent bg-gradient-to-r from-violet-500 to-indigo-600">
                                           {" Digital Age"}
                                       </span>
                                   </h1>
                                   <p className={`text-lg md:text-xl mb-8 ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                                       Elevate your digital presence with our cutting-edge design and development services. We create stunning, functional experiences that drive results.
                                   </p>
                                   <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
                                       <button className="px-8 py-3 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition duration-300 text-lg">
                                           Explore Services
                                       </button>
                                       <button className={`px-8 py-3 rounded-md text-lg border ${darkMode ? 'border-gray-600 hover:bg-gray-800' : 'border-gray-300 hover:bg-gray-100'} transition duration-300`}>
                                           Learn More
                                       </button>
                                   </div>
                               </div>
                               
                               <div className="md:w-1/2 mt-12 md:mt-0 md:pl-8">
                                   <div className="relative">
                                       <div className="absolute -inset-1 bg-gradient-to-r from-violet-500 to-indigo-600 rounded-lg blur opacity-25"></div>
                                       <div className={`relative ${darkMode ? 'bg-gray-800' : 'bg-white'} rounded-lg overflow-hidden shadow-xl`}>
                                           <img 
                                               src="https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-1.2.1&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1350&q=80" 
                                               alt="Digital Solutions"
                                               className="w-full h-auto"
                                           />
                                       </div>
                                   </div>
                               </div>
                           </div>
                       </div>
                   </section>
                   
                   {/* Features Section */}
                   <section className={`py-16 ${darkMode ? 'bg-gray-800' : 'bg-gray-50'}`}>
                       <div className="container mx-auto px-4 sm:px-6 lg:px-8">
                           <div className="text-center mb-16">
                               <h2 className="text-3xl md:text-4xl font-bold tracking-tight mb-4">Our Services</h2>
                               <p className={`text-lg max-w-3xl mx-auto ${darkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                                   We offer a complete range of digital services to help your business thrive in the modern marketplace.
                               </p>
                           </div>
                           
                           <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                               {/* Service Card 1 */}
                               <div className={`p-8 rounded-lg ${darkMode ? 'bg-gray-900' : 'bg-white'} shadow-lg transition duration-300 hover:shadow-xl`}>
                                   <div className="w-12 h-12 rounded-full bg-indigo-100 flex items-center justify-center mb-6">
                                       <i className="fas fa-laptop-code text-xl text-indigo-600"></i>
                                   </div>
                                   <h3 className="text-xl font-bold mb-3">Web Development</h3>
                                   <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                                       Custom websites and web applications built with the latest technologies for optimal performance and user experience.
                                   </p>
                               </div>
                               
                               {/* Service Card 2 */}
                               <div className={`p-8 rounded-lg ${darkMode ? 'bg-gray-900' : 'bg-white'} shadow-lg transition duration-300 hover:shadow-xl`}>
                                   <div className="w-12 h-12 rounded-full bg-indigo-100 flex items-center justify-center mb-6">
                                       <i className="fas fa-mobile-alt text-xl text-indigo-600"></i>
                                   </div>
                                   <h3 className="text-xl font-bold mb-3">Mobile Solutions</h3>
                                   <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                                       Native and cross-platform mobile applications that provide seamless experiences across all devices.
                                   </p>
                               </div>
                               
                               {/* Service Card 3 */}
                               <div className={`p-8 rounded-lg ${darkMode ? 'bg-gray-900' : 'bg-white'} shadow-lg transition duration-300 hover:shadow-xl`}>
                                   <div className="w-12 h-12 rounded-full bg-indigo-100 flex items-center justify-center mb-6">
                                       <i className="fas fa-paint-brush text-xl text-indigo-600"></i>
                                   </div>
                                   <h3 className="text-xl font-bold mb-3">UI/UX Design</h3>
                                   <p className={`${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                                       Beautiful, intuitive interfaces that enhance user engagement and drive conversions.
                                   </p>
                               </div>
                           </div>
                       </div>
                   </section>
                   
                   {/* CTA Section */}
                   <section className="py-16">
                       <div className="container mx-auto px-4 sm:px-6 lg:px-8">
                           <div className={`rounded-2xl ${darkMode ? 'bg-gradient-to-r from-gray-800 to-gray-900' : 'bg-gradient-to-r from-indigo-600 to-violet-600'} p-10 md:p-16 shadow-xl`}>
                               <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                                   <div className="mb-8 md:mb-0 md:pr-8">
                                       <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">Ready to transform your digital presence?</h2>
                                       <p className="text-lg text-white/90">
                                           Let's discuss your project and explore how we can help you achieve your goals.
                                       </p>
                                   </div>
                                   <div className="flex-shrink-0">
                                       <button className={`px-8 py-4 rounded-lg ${darkMode ? 'bg-indigo-600 hover:bg-indigo-700' : 'bg-white hover:bg-gray-100'} shadow-lg transition duration-300 text-lg font-medium ${darkMode ? 'text-white' : 'text-indigo-600'}`}>
                                           Get in Touch
                                       </button>
                                   </div>
                               </div>
                           </div>
                       </div>
                   </section>
                   
                   {/* Footer */}
                   <footer className={`py-12 ${darkMode ? 'bg-gray-900' : 'bg-white'}`}>
                       <div className="container mx-auto px-4 sm:px-6 lg:px-8">
                           <div className="flex flex-col md:flex-row justify-between items-start md:items-center border-b border-gray-700 pb-8">
                               <div className="mb-8 md:mb-0">
                                   <span className="text-2xl font-bold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-violet-500 to-indigo-600">
                                       Horizon
                                   </span>
                                   <p className={`mt-2 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                                       Modern digital solutions for forward-thinking businesses.
                                   </p>
                               </div>
                               
                               <div className="flex space-x-6">
                                   <a href="#" className="text-gray-400 hover:text-gray-300 transition duration-300">
                                       <i className="fab fa-twitter text-xl"></i>
                                   </a>
                                   <a href="#" className="text-gray-400 hover:text-gray-300 transition duration-300">
                                       <i className="fab fa-instagram text-xl"></i>
                                   </a>
                                   <a href="#" className="text-gray-400 hover:text-gray-300 transition duration-300">
                                       <i className="fab fa-github text-xl"></i>
                                   </a>
                                   <a href="#" className="text-gray-400 hover:text-gray-300 transition duration-300">
                                       <i className="fab fa-linkedin text-xl"></i>
                                   </a>
                               </div>
                           </div>
                           
                           <div className="grid grid-cols-1 md:grid-cols-4 gap-8 py-8">
                               <div>
                                   <h3 className="text-lg font-medium mb-4">Services</h3>
                                   <ul className={`space-y-2 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                                       <li><a href="#" className="hover:text-indigo-500 transition duration-300">Web Development</a></li>
                                       <li><a href="#" className="hover:text-indigo-500 transition duration-300">Mobile Apps</a></li>
                                       <li><a href="#" className="hover:text-indigo-500 transition duration-300">UI/UX Design</a></li>
                                       <li><a href="#" className="hover:text-indigo-500 transition duration-300">Digital Marketing</a></li>
                                   </ul>
                               </div>
                               
                               <div>
                                   <h3 className="text-lg font-medium mb-4">Company</h3>
                                   <ul className={`space-y-2 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                                       <li><a href="#" className="hover:text-indigo-500 transition duration-300">About Us</a></li>
                                       <li><a href="#" className="hover:text-indigo-500 transition duration-300">Our Team</a></li>
                                       <li><a href="#" className="hover:text-indigo-500 transition duration-300">Careers</a></li>
                                       <li><a href="#" className="hover:text-indigo-500 transition duration-300">Contact</a></li>
                                   </ul>
                               </div>
                               
                               <div>
                                   <h3 className="text-lg font-medium mb-4">Resources</h3>
                                   <ul className={`space-y-2 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                                       <li><a href="#" className="hover:text-indigo-500 transition duration-300">Blog</a></li>
                                       <li><a href="#" className="hover:text-indigo-500 transition duration-300">Case Studies</a></li>
                                       <li><a href="#" className="hover:text-indigo-500 transition duration-300">Documentation</a></li>
                                       <li><a href="#" className="hover:text-indigo-500 transition duration-300">Help Center</a></li>
                                   </ul>
                               </div>
                               
                               <div>
                                   <h3 className="text-lg font-medium mb-4">Contact</h3>
                                   <ul className={`space-y-2 ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                                       <li><EMAIL></li>
                                       <li>+1 (555) 123-4567</li>
                                       <li>123 Digital Ave, Tech City</li>
                                   </ul>
                               </div>
                           </div>
                           
                           <div className="pt-8 border-t border-gray-700">
                               <p className={`text-center ${darkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                                   © {new Date().getFullYear()} Horizon. All rights reserved.
                               </p>
                           </div>
                       </div>
                   </footer>
               </div>
           );
       }

       const root = ReactDOM.createRoot(document.getElementById('root'));
       root.render(<App />);
   </script>
</body>
</html>