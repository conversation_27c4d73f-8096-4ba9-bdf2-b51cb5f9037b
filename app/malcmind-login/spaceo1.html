<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>React with Babel Standalone</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/babel-standalone@6/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@3.8.0/dist/full.css" rel="stylesheet">
    <style>
        .spaceship {
            position: absolute;
            top: 50%;
            left: -100px;
            width: 50px;
            height: 50px;
            transition: left 2s linear;
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        function App() {
            const [count, setCount] = React.useState(0);
            const [position, setPosition] = React.useState(-100);

            React.useEffect(() => {
                const timer = setInterval(() => {
                    setPosition((prevPosition) => (prevPosition >= window.innerWidth ? -100 : prevPosition + 10));
                }, 100);

                return () => clearInterval(timer);
            }, []);

            return (
                <div className="flex flex-col items-center justify-center h-screen bg-gray-100 relative">
                    <h1 className="text-3xl font-bold text-blue-600">Hello, React!</h1>
                    <p className="text-lg mt-4">Count: {count}</p>
                    <button className="btn bg-blue-500">
                        this is a new one
                    </button>
                    <button 
                        className="mt-4 px-4 py-2 bg-green-700 text-white rounded-lg hover:bg-blue-600"
                        onClick={() => setCount(count + 1)} >
                        Increment
                    </button>

                    <div 
                        className="spaceship" 
                        style={{ left: `${position}px` }}>
                        🚀
                    </div>
                </div>
            );
        }

        const root = ReactDOM.createRoot(document.getElementById('root'));
        root.render(<App />);
    </script>
</body>
</html>
