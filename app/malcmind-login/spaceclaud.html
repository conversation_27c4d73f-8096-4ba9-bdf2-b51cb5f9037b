<!DOCTYPE html>
<html lang="en">
<head>
   <meta charset="UTF-8">
   <meta name="viewport" content="width=device-width, initial-scale=1.0">
   <title>React Spaceship Animation</title>
   <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
   <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
   <script src="https://unpkg.com/babel-standalone@6/babel.min.js"></script>
   <script src="https://cdn.tailwindcss.com"></script>
   <link href="https://cdn.jsdelivr.net/npm/daisyui@3.8.0/dist/full.css" rel="stylesheet">
</head>
<body>
   <div id="root"></div>
   
   <script type="text/babel">
       function App() {
           const [position, setPosition] = React.useState(0);
           const [rotation, setRotation] = React.useState(0);
           const [exhaust, setExhaust] = React.useState(1);
           
           React.useEffect(() => {
               const animationInterval = setInterval(() => {
                   setPosition(prev => (prev + 2) % window.innerWidth);
                   setRotation(prev => prev + 1);
                   setExhaust(prev => Math.random() * 0.5 + 0.75);
               }, 50);
               
               return () => clearInterval(animationInterval);
           }, []);
           
           return (
               <div className="flex flex-col items-center justify-center h-screen bg-gray-900 overflow-hidden relative">
                   <h1 className="text-3xl font-bold text-blue-400 absolute top-5">Space Explorer</h1>
                   
                   {/* Stars in background */}
                   {Array.from({ length: 100 }).map((_, i) => (
                       <div 
                           key={i}
                           className="absolute bg-white rounded-full"
                           style={{
                               width: Math.random() * 3 + 1 + 'px',
                               height: Math.random() * 3 + 1 + 'px',
                               top: Math.random() * 100 + '%',
                               left: Math.random() * 100 + '%',
                               opacity: Math.random() * 0.8 + 0.2
                           }}
                       />
                   ))}
                   
                   {/* Spaceship */}
                   <div 
                       className="absolute"
                       style={{
                           left: `${position}px`,
                           top: '50%',
                           transform: `translateY(-50%) rotate(${rotation}deg)`,
                           transition: 'transform 0.1s ease'
                       }}
                   >
                       {/* Ship Body */}
                       <div className="relative">
                           {/* Exhaust */}
                           <div 
                               className="absolute -right-10 top-1/2 transform -translate-y-1/2 w-8 h-4 bg-gradient-to-r from-yellow-500 via-orange-500 to-transparent rounded-r-full"
                               style={{ opacity: exhaust }}
                           />
                           
                           {/* Main body */}
                           <div className="w-16 h-8 bg-gradient-to-r from-gray-400 to-gray-600 rounded-l-full rounded-r-lg"></div>
                           
                           {/* Cockpit */}
                           <div className="absolute left-2 top-1 w-5 h-6 bg-blue-400 rounded-full opacity-80"></div>
                           
                           {/* Wing top */}
                           <div className="absolute -top-4 left-6 w-8 h-3 bg-red-500 transform skew-x-[30deg]"></div>
                           
                           {/* Wing bottom */}
                           <div className="absolute -bottom-4 left-6 w-8 h-3 bg-red-500 transform -skew-x-[30deg]"></div>
                       </div>
                   </div>
                   
                   <div className="absolute bottom-5 flex gap-4">
                       <button className="btn bg-blue-600 text-white hover:bg-blue-700">
                           Launch Ship
                       </button>
                       <button className="btn bg-green-600 text-white hover:bg-green-700">
                           Boost Speed
                       </button>
                   </div>
               </div>
           );
       }

       const root = ReactDOM.createRoot(document.getElementById('root'));
       root.render(<App />);
   </script>
</body>
</html>