const fs = require('fs');
const path = require('path');

// Get the relative file path from command-line arguments
const relativeFilePath = process.argv[2];

if (!relativeFilePath) {
    console.error('Usage: node editFile.js <relative-file-path>');
    process.exit(1);
}

// Resolve the absolute path
const filePath = path.resolve(relativeFilePath);

// Read the file
fs.readFile(filePath, 'utf8', (err, data) => {
    if (err) {
        console.error('Error reading the file:', err);
        return;
    }

    // Modify the content (replace 'oldWord' with 'newWord')
    const updatedContent = data.replace(/oldWord/g, 'newWord');

    // Write back to the file
    fs.writeFile(filePath, updatedContent, 'utf8', (err) => {
        if (err) {
            console.error('Error writing to the file:', err);
            return;
        }
        console.log(`File updated successfully: ${filePath}`);
    });
});
// echo "This is an oldWord that will be replaced." > test.txt
// Type this command and press Enter:
// bash
// Copy
// Edit
// cat << EOF > malcMindLogin.js
// Paste your code snippet into the terminal.
// Press Enter, then Ctrl + D to save and exit.



// give me the code for a program that saves files in node. just the code snippet itself . and no explination
//made with the 01 model

//https://chatgpt.com/share/67d9abcc-6df4-8008-970b-e9d5e63a57c6