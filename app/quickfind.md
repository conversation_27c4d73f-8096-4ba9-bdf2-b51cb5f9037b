[cool](<(main site)/Navigation/page.js>)



Open the Command Palette: Press Ctrl+Shift+P (or Cmd+Shift+P on macOS).
Type "Markdown: Insert Link to File": Type "Markdown: Insert Link to File in Workspace" and select the command from the dropdown.
Choose the File: VS Code will prompt you to select a file from your workspace, and it will then insert the link into your Markdown file. 


Drag and Drop:
Open the Explorer:
In VS Code, navigate to the Explorer view (usually on the left side of the window). 
Select the File:
Find the file you want to link to in the Explorer. 
Drag and Drop:
Click and drag the file from the Explorer into your Markdown editor, holding down the Shift key while dragging. 


Yes, you can have multiple README files in a GitHub repository, and GitHub will display the README in the root directory and any READMEs in subdirectories when you navigate to them. 
Here's a breakdown of how it works:
Root README:
A README.md file in the root directory will be displayed as the main README for the repository. 
Subdirectory READMEs:
You can create README.md files within subdirectories, and GitHub will display those READMEs when you navigate to those specific directories. 
Multiple READMEs:
If you have multiple README files in the root directory, GitHub will prioritize the one in the .github directory, then the repository root, and finally the docs directory. 
No special handling:
GitHub doesn't require any special configuration or naming conventions beyond using the standard README.md file name for the main README. 
Organization:
This feature allows you to provide context-specific documentation for different parts of your project, making it easier for users to understand your code and its structur


https://github.com/ole-old/GitHub-For-Writers-Manual/blob/master/managing-multiple-pages/README.md




```jsx
// Exporting common utilities (using named exports)
export { formatDate } from './formatDate';
export { logger } from './logger';
export { constants } from './constants';

// Exporting actions (e.g., server-side actions)
export { authActions } from '../actions/authActions';
export { productActions } from '../actions/productActions';

// Exporting services (e.g., API or product service)
export { apiService } from '../services/api';
export { authService } from '../services/authService';
export { productService } from '../services/productService';

// Exporting custom hooks
export { useAuth } from '../hooks/useAuth';
export { useFetch } from '../hooks/useFetch';

export * from './utils';

export { default as apiService } from '../services/api';
export { default as authService } from '../services/authService';
export { default as productService } from '../services/productService';
vscode-remote://wsl%2Bubuntu/home/<USER>/nextjs-flask/app/quickfind.md

// Importing named exports from the centralized utils file
import { formatDate, logger, authActions, apiService, useAuth } from '@/app/commons';

// Or importing everything as an object
import * as utils from '@/utils';


├── /services             # 🌎 Global services (shared API logic, auth, etc.)
 │    ├── api.js
 │    ├── authService.js
 │    ├── productService.js
 │    ├── storage.js
 │
 ├── /actions              # ⚡ Server Actions (direct DB interactions)
 │    ├── authActions.js
 │    ├── productActions.js
 │
 ├── /utils                # 🔧 Utility functions (helpers, formatters, etc.)
 │    ├── formatDate.js
 │    ├── logger.js
 │    ├── constants.js
 │
 ├── /features             # 🏗 Feature-based structure
 │    ├── /dashboard
 │    │    ├── services/   # Local services (only for dashboard)
 │    │    │    ├── dashboardApi.js
 │    │    ├── components/ # UI components specific to dashboard
 │    │    │    ├── StatsCard.jsx
 │    │    │    ├── Chart.jsx
 │    │    ├── hooks/      # Custom hooks for dashboard logic
 │    │    │    ├── useDashboardData.js
 │    │    ├── page.jsx    # Dashboard page component (app router)
 │    │
 │    ├── /profile
 │    │    ├── services/
 │    │    │    ├── profileService.js
 │    │    ├── components/
 │    │    │    ├── Avatar.jsx
 │    │    ├── page.jsx    # Profile page component (app router)
 │    │
 ├── /hooks                # 🔄 Global reusable hooks
 │    ├── useAuth.js
 │    ├── useFetch.js
 │
 ├── /store                # 🗄️ Global state management (if using Redux, Zustand, etc.)
 │    ├── userSlice.js
 │    ├── productSlice.js
 │
 ├── /components           # 🏗️ Global reusable components
 │    ├── Button.jsx
 │    ├── Modal.jsx
 │
 ├── /app                  # 📌 Next.js App Router
 │    ├── layout.jsx       # Root layout (applies to all pages)
 │    ├── page.jsx         # Home page
 │    ├── dashboard/       # Dashboard route
 │    │    ├── page.jsx    # Dashboard page (app router)
 │    ├── profile/         # Profile route
 │    │    ├── page.jsx    # Profile page (app router)
 │
 ├── App.jsx               # 🚀 Main app entry (for CRA/Vite)
 ├── index.js              # ⚡ React root
 ├── tailwind.config.js    # 🎨 Tailwind CSS config (if using)
 ├── package.json          # 📦 Dependencies


