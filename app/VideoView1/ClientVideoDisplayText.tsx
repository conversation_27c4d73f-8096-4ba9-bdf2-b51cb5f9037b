'use client'

import PrismaServerAction from './PrismaServerAction';
import { upsertVideoArray, displayDataAction, getColumnNames } from './PrismaServerAction';
import { useState, useEffect } from 'react'
import { videoSource } from './page';
import { convertLink } from '@/app/utils/convertLink';

export default function ClientVideoDisplayText() {
    const [text, setText] = useState('');

    const handleTextChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
        setText(event.target.value);
      };

    async function showMeRendered() {
        console.log(text)
         await getColumnNames()
        await displayDataAction()
    }

    async function updatePrisma() {
        const siteName = text.match(/www\.(.*?)\.com/)![1] as videoSource;
        const regex = new RegExp(`https?:\/\/[^\\s"']*\\.${siteName}[^\\s"']*`, 'g');
        const linkArray = text.match(regex);
        await upsertVideoArray(linkArray as string[])
    }

    return (
        <div className='flex flex-col w-5/6 mx-auto p-2'>
            <button className='btn bg-slate-500 text-white' onClick = {showMeRendered}>Show Me Rendered Text</button>
            <button className='btn bg-slate-500 text-white' onClick = {updatePrisma}>Update Videos</button>
            <textarea
      className="textarea textarea-bordered"
      placeholder="Upload More Links Here"
      value={text}
      onChange={handleTextChange}
    />

        </div>
    )
}