'use client'

import type { Params, videoSource } from './page'
import { useState, useEffect, ChangeEvent } from 'react'
import { atom, useAtom } from "jotai"
import { displayedVideosAtom } from './ClientVideoDisplay'
import ClientVideoDisplayAddButton from './ClientVideoDisplayAddButton';

export const videoFiltersAtom = atom([])
export const videoFilterDontShowAtom = atom([])


type source = 'text' | 'database'
let source = 'database'
export const databaseModeAtom = atom(true)
export default function Nav({ embededUrlArray, reGenerateDisplay }: { embededUrlArray: (arg: Params) => Promise<any>, reGenerateDisplay: Function }) {
  const [displayedVideos, setDisplayedVideos] = useAtom(displayedVideosAtom);
  const [databaseMode, setDatabaseMode] = useAtom(databaseModeAtom);
  const [videoFilters, setVideoFilters] = useAtom(videoFiltersAtom);
  const [videoFilterDontShow, setVideoFilterDontShow] = useAtom(videoFilterDontShowAtom);


  console.log('hit')
  async function handleClick() {
    if (source == 'database') {
      console.log('triggered')
      source = 'text'
      setDatabaseMode(false)
      setDisplayedVideos(await embededUrlArray({ source: 'text' }))
    }
    else {
      source = 'database'
      setDatabaseMode(true)
      setDisplayedVideos(await embededUrlArray({ source: 'database' }))
    }
  }


  async function handleDelete(evt: React.MouseEvent<HTMLButtonElement, MouseEvent>) {
    console.log('hit')
    const category = evt.currentTarget.getAttribute('data-category-to-delete') as string;
    const newArray = videoFilters.filter((item) => item !== category)
    setVideoFilters((prev) => newArray)
    console.log(category);
  }

  async function handleDelete2(evt: React.MouseEvent<HTMLButtonElement, MouseEvent>) {
    console.log('hit')
    const category = evt.currentTarget.getAttribute('data-category-to-delete') as string;
    const newArray = videoFilterDontShow.filter((item) => item !== category)
    setVideoFilterDontShow((prev) => newArray)
    console.log(category);

  }
  return (
    <>
      <div className="navbar bg-base-100">
        <div className="navbar-start">
          <a className="btn btn-ghost text-xl">Video Organizer Pro</a>
        </div>
        <div className="navbar-center hidden lg:flex">

          <pre>Use Text Links </pre>
          <input type="checkbox" onClick={handleClick} className="toggle toggle-error" defaultChecked />
          <pre> Use DataBase Links</pre>

        </div>
        <div className="navbar-end">
          <a className="btn">Button</a>
        </div>
      </div>
      <div className="bg-slate-800 min-h-12 flex items-center justify-center">
        <p className='text-white'>Video Filters - includes:</p>
        <div className='bg-slate-800'>
          <div className='flex flex-row gap-1 m-2'>
            {videoFilters.map((item) =>
              <div>
                <button data-category-to-delete={item} onClick={(evt) => handleDelete(evt)} className='btn btn-sm bg-transparent text-white m-1'>X</button>
                <button className='btn btn-sm'>{item}</button>
              </div>
            )}
            <ClientVideoDisplayAddButton reGenerateDisplay={reGenerateDisplay} displayDirection='bottom' setVideoFilters={setVideoFilters} />

          </div>
        </div>
      </div>
      <div className="bg-slate-800 min-h-12 flex items-center justify-center">
        <p className='text-white'>Video Filters - does not include:</p>
        <div className='bg-slate-800'>
          <div className='flex flex-row gap-1 m-2'>
            {videoFilterDontShow.map((item) =>
              <div>
                <button data-category-to-delete={item} onClick={(evt) => handleDelete2(evt)} className='btn btn-sm bg-transparent text-white m-1'>X</button>
                <button className='btn btn-sm'>{item}</button>
              </div>
            )}
            <ClientVideoDisplayAddButton reGenerateDisplay={reGenerateDisplay} displayDirection='bottom' setVideoFilters={ setVideoFilterDontShow} />

          </div>
        </div>
      </div>
    </>
  )

}

