'use client'

import { useState, useEffect} from 'react'
import { getColumnNames, upsertVideoCategories } from './PrismaServerAction'
import type { Params, videoSource } from './page'
import { atom, useAtom } from "jotai"
import { displayedVideosAtom } from './ClientVideoDisplay'

type displayDirection = 'top' | 'bottom'
export default function ClientVideoDisplayAddButton({linkID, reGenerateDisplay, displayDirection, setVideoFilters}: {linkID?: number , reGenerateDisplay: Function, displayDirection?: displayDirection, setVideoFilters?: Function}) {
    const [addSelection, setAddSelection] = useState<string[]>([]);
    const [displayedVideos, setDisplayedVideos] = useAtom(displayedVideosAtom);
    const [addDisplayDirection, setDisplayDirection] = useState<displayDirection>('top');


   console.log('hit promo')



  async function videoCategoryUpdater(evt: React.MouseEvent<HTMLLIElement, MouseEvent>) {
        const targetElement = evt.target as HTMLLIElement;
        console.log(targetElement.textContent)
        console.log(linkID)
        //if the nav bar called add the link id wont be available
        if (!linkID && setVideoFilters) {
             setVideoFilters((prev: string[]) => [...prev, targetElement.textContent as string])
        }
        if(linkID) {
            reGenerateDisplay(await upsertVideoCategories(linkID, targetElement.textContent as string))
        }
    }

    async function getAddSelection() {
        console.log(addSelection)
        if(!addSelection || addSelection.length == 0) {
            console.log('hit promo')
        const data = await getColumnNames();
        if (data) setAddSelection(data);
        }
    }

    useEffect(() => {
        getAddSelection()
        if (displayDirection) setDisplayDirection(displayDirection)
    }, [])

    return (
        <div className={`flex flex-row justify-center items-center dropdown dropdown-${displayDirection}`}>
            {/* <button className='btn btn-sm bg-red-400'>+</button><p className='text-white'>&nbsp;add</p> */}
            <div tabIndex={0} role="button" className="btn btn-sm bg-red-400">+</div>
            <p className='text-white'>&nbsp;add</p>
            <ul tabIndex={0} className="dropdown-content menu bg-slate-800 rounded-box z-[1] w-52 p-2 shadow text-white ">
                {addSelection && addSelection.map((selection) => (
                    <li key = {selection} onClick= {(evt) =>videoCategoryUpdater(evt)}><a>{selection}</a></li>
                ))}
            </ul>
        </div>
    )
}