'use client'

import ClientVideoDisplayText from './ClientVideoDisplayText';
import ClientVideoDisplayAddButton from './ClientVideoDisplayAddButton';
import { useState, useEffect, ChangeEvent , FC} from 'react'
import type { Params, videoSource } from './page'
import { atom, useAtom } from "jotai"
import { databaseModeAtom } from './ClientVideoDisplayNav';
import { deleteVideoCategories } from './PrismaServerAction';
import Nav from './ClientVideoDisplayNav';
import { videoFiltersAtom, videoFilterDontShowAtom } from './ClientVideoDisplayNav';
import {usePagination} from '../utils/Pagination'; 

// import type { Params} from './types';
export const displayedVideosAtom = atom([])
type topicVideo = 'youtube' | 'porn'
export const topicVideoAtom = atom<topicVideo>('youtube')


let randomArray = ['best']
export default function ClientVideoDisplay({ embededUrlArray }: { embededUrlArray: (arg: Params) => Promise<any> }) {
    const [selectedOption, setSelectedOption] = useState('');
    const [displayedVideos, setDisplayedVideos] = useAtom(displayedVideosAtom);
    const [isDatabaseMode, setDatabaseMode] = useAtom(databaseModeAtom);
    const [videoFilters, setVideoFilters] = useAtom(videoFiltersAtom);


    const [videoFilterDontShow, setVideoFilterDontShow] = useAtom(videoFilterDontShowAtom);


    const [topicVideo, setTopicVideo] = useAtom(topicVideoAtom);


    const [sliceTopNumber, setSliceTopNumber] = useState(10)

    const [paginatedData, PaginationFooter, PaginationHeader] = usePagination(displayedVideos,10)
    console.log(displayedVideos.length)



    console.log(videoFilters)


    console.log(isDatabaseMode)

    console.log(displayedVideos)

    useEffect(() => {

        handleChange()
    }, [])

    /**
     * This function can be called by any function to reGenerateDisplay, args are included to prevent typescript screaming from functions with different arg types
     */
    async function reGenerateDisplay(...arg: any) {
        let temp = await embededUrlArray({ links: topicVideo, source: 'database' })
        setDisplayedVideos(temp)
    }



    async function handleChange(evt?: ChangeEvent<HTMLSelectElement>) {

        if (!evt) {
            console.log('hit')
            // let temp = await embededUrlArray({ links: 'youtube', source: 'database' })
            
            let temp = await embededUrlArray({ links: 'porn', source: 'database' })
            setDisplayedVideos(temp)
        }
        else {
            if (evt) {
                const targetElement = evt.target as unknown as HTMLLIElement;
                setSelectedOption(targetElement.value as unknown as string);
                if(isDatabaseMode) {
                   let temp = await embededUrlArray({ links: targetElement.value as unknown as videoSource, source: 'database' }) 
                   setDisplayedVideos(temp) 
                   setTopicVideo(targetElement.value as unknown as topicVideo )  
                }
                else{
                    let temp = await embededUrlArray({ links: targetElement.value as unknown as videoSource, source: 'text' })
                    setDisplayedVideos(temp) 
                }
               
                
            }

        }
    }

   


    return (
        <>
            <Nav embededUrlArray={embededUrlArray} reGenerateDisplay={reGenerateDisplay} />
            <div className='flex flex-col items-center gap-10  '>
                <ClientVideoDisplayText />

                <div className='p-5'>
                    <p className='text-2xl '>Current Selected Option: {selectedOption}</p>
                    <select className="select select-bordered w-full max-w-xs" onChange={(evt: ChangeEvent<HTMLSelectElement>) => handleChange(evt)}>
                        <option disabled selected >Browse By Video Type</option>
                        <option value='youtube'>Youtube</option>
                        <option value='porn'>Porn</option>
                    </select>
                    <PaginationHeader />
                </div>
            </div>
            <div className='flex flex-row flex-wrap justify-evenly'>
                {!isDatabaseMode && displayedVideos && displayedVideos.map((link, index) => (
                    <div key={link} className='flex flex-col'>
                        <iframe src={`${link}`} frameBorder='0' width='510' height='400' scrolling='no' allowFullScreen loading="lazy" />
                        <div className='bg-slate-800'>
                            <p className='text-white bg-black p-2'>Categories</p>
                            <div className='flex flex-row gap-1 m-2'>
                                <button className='btn btn-sm bg-transparent text-white'>X</button>
                                <button className='btn btn-sm'>programming</button>
                                <button className='btn btn-sm bg-transparent text-white'>X</button>
                                <button className='btn btn-sm'>cool</button>
                            </div>
                        </div>
                    </div>
                ))}

                <>
                    {console.log(videoFilters)}
                    {console.log(videoFilterDontShow) }
                </>
                {isDatabaseMode && displayedVideos && paginatedData
                // initial pagination
                    
                    .filter(link => {
                        if (videoFilters.length < 1) {
                            return link;
                        }
                        // Check if any property in the link object passes the checkerArray criteria
                        return Object.keys(link).some(key => link[key] === true && videoFilters.includes(key));
                    })
                    .filter(link => {
                        if (videoFilterDontShow.length < 1) {
                            return link;
                        }
                        // Check if any property in the link object passes the checkerArray criteria
                        return Object.keys(link).some(key => link[key] === false  && (videoFilterDontShow.includes(key)));
                    })
                    .map((link, index) => (


                        <div className='flex flex-col'>
                            <p>insert x here</p>
                            <iframe src={`${link.link}`} frameBorder='0' width='510' height='400' scrolling='no' allowFullScreen loading="lazy" />
                            <div className='bg-slate-800 max-w-[510px]'>
                                <p className='text-white bg-black p-2'>Categories</p>
                                <div className='flex flex-row gap-1 m-2 flex-wrap'>

                                    {Object.entries(link).map(([key, value]) => {
                                        if (value === true && key != 'id' && key != 'link' && key != 'authorId' && key != 'author') {
                                            console.log(randomArray)
                                            console.log(randomArray.includes(key))
                                            console.log(key, link.id, link.best)
                                            console.log(value === true && key != 'id' && key != 'link' && key != 'authorId' && key != 'author' && randomArray.includes(key), link.id)
                                            return (

                                                <div key={key}>
                                                    <button data-handleDelete={link.id} data-category-to-delete={key} onClick={(evt) => reGenerateDisplay(handleDelete(evt))} className='btn btn-sm bg-transparent text-white m-1'>X</button>
                                                    <button className='btn btn-sm'>{key}</button>
                                                </div>
                                            );
                                        }
                                        return null;
                                    })}
                                    <ClientVideoDisplayAddButton linkID={link.id} reGenerateDisplay={reGenerateDisplay} />
                                </div>
                            </div>
                        </div>
                    ))}
            </div>
            <PaginationFooter />
        </>
    )
}

async function handleDelete(evt: React.MouseEvent<HTMLButtonElement, MouseEvent>) {
    console.log('hit')
    //const key = (evt.currentTarget.parentElement as HTMLElement).key
    //const key = evt.currentTarget.parentElement?.getAttribute('key');
    const id = evt.currentTarget.getAttribute('data-handleDelete');
    const category = evt.currentTarget.getAttribute('data-category-to-delete') as string;
    await deleteVideoCategories(parseInt(id), category)
    console.log(id, category);

}