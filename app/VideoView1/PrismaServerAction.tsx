'use server'

import { Prisma } from '@prisma/client';
import { prisma } from '../lib/prisma'

console.log('hit')


export async function getColumnNames() {
    console.log('hit promo')
    const video = await prisma.videos.findFirst();
    if (video) {
        const columnNames = Object.keys(video);
        const filteredNames = columnNames.filter((name) => name !== 'id' && name !== 'link' && name !== 'authorId');
        console.log(filteredNames);
        return filteredNames
    }
    
  console.log(video);
}

export async function displayDataAction() {
    console.log('hit promo')
    const user = await prisma.user.findUnique({
        where: {
            userName: 'apophis51'
        }
    })
    console.log(user!.id)

    const videos = await prisma.videos.findMany({
    where: {
      authorId: user!.id,
//       blond: true,
//       new: true
     }
   });
   console.log(videos)
   return videos
}

export async function upsertVideoArray(userTextArray: Array<string>) {
    const user = await prisma.user.findUnique({
        where: {
            userName: 'apophis51'
        }
    });
    if (user) {
        const upsertSingleVideo = async (userLink: string) => {
            const video = await prisma.videos.upsert({
                where: {
                    link_authorId: {
                        link: userLink,
                        authorId: user!.id,
                    },
                },
                update: {
                    link: userLink,
                    // best: true
                },
                create: {
                    link: userLink,
                    author: {
                        connect: {
                            id: user!.id
                        }
                    }
                }
            });
        }
        const upsertPromises = userTextArray.map(video =>
            upsertSingleVideo(video)
        );
        const results = await Promise.all(upsertPromises);
    }
}


export default async function upsertData(userText: string) {
    let manta = await prisma.user.upsert({
        where: { userName: 'apophis51' },
        update: {
            password: 'test',
            videos: {
                create: {
                    link: userText
                }
            }
        },
        create: {
            password: 'test',
            videos: {
                create: {
                    link: userText
                }
            }
        },

    });
}


export async function upsertVideoCategories(videoId: number, category: string) {
    const user = await prisma.user.findUnique({
        where: {
            userName: 'apophis51'
        }
    });
    if (user) {
            const video = await prisma.videos.update({
                where: {
                    id: videoId
                },
                data: {
                    [category]: true
                }
            });
        }
        console.log('function executed sucessful')
}


export async function deleteVideoCategories(videoId: number, category: string) {
    console.log('hit')
    const user = await prisma.user.findUnique({
        where: {
            userName: 'apophis51'
        }
    });
    if (user) {
            const video = await prisma.videos.update({
                where: {
                    id: videoId
                },
                data: {
                    [category]: false
                }
            });
        }
        console.log('function executed sucessful')
}