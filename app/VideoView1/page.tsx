
//https://www.litecode.dev/article/ditching-regex-in-favor-of-typescripts-type-pattern-checks

import fs from 'fs';
import path from 'path';
 import ClientVideoDisplay from './ClientVideoDisplay';
 import { displayDataAction } from './PrismaServerAction';

export type videoSource = 'xvideos' | 'pornhub' | 'youtube' | 'porn';

const txt_link_extractor = (fileName: `${string}.txt`, videoType: videoSource) => {
    const filePath = path.join(process.cwd(),`app/VideoView1/${fileName}`); // Adjust the path as needed or const textContent = fs.readFileSync('app/PornView1/pornlinks.txt', 'utf-8');
    const fileContent = fs.readFileSync(filePath, 'utf-8');
    console.log(fileContent)
        //const regex = /https?:\/\/[^\s]+/g;
    // const regex = new RegExp(`https?:\/\/[^\s"]*\.${videoType}[^\s"]*`,'g');
    const regex = new RegExp(`https?:\/\/[^\\s"']*\\.${videoType}[^\\s"']*`, 'g');

    const links = fileContent.match(regex);
    console.log(links)
    return links || []; // Return an empty array if no matches are found  
};


const pornlinks = txt_link_extractor('pornlinks.txt', 'xvideos');  
const messyLinksArray = txt_link_extractor('messylinks.txt', 'xvideos');
const pornhubLinks = txt_link_extractor('pornhub.txt', 'pornhub');
const youtubeLinks = txt_link_extractor('youtube.txt', 'youtube');


export const convertLink = async (link: string, convertSource: videoSource) => {
    'use server'
    //****Experimental */
    let sitename  = link.match(/www\.(.*?)\.com/)![1] 
    console.log(sitename)
    convertSource = sitename as videoSource //delete my convertSource prop if successful
    //**  End Experimental */
    if(convertSource == 'xvideos') {
    // Regular expression to match the original format and capture the unique part of the URL
    const regex = /https:\/\/www\.xvideos\.com\/video\.([a-z0-9]+)\/.*/;
    // Use the replace method to create the new URL format
    const newLink = link.replace(regex, 'https://www.xvideos.com/embedframe/$1');
    console.log(newLink)
    return newLink;
    }
    if(convertSource == 'pornhub') {
        //  if (link == 'https://www.pornhub.com/view_video.php?viewkey=ph6293e3968a4b4&utm_medium=embed&utm_campaign=embed-related-html5')
        //  {
            console.log('hit')
        
        
        // Regular expression to match the original format and capture the unique part of the URL
        const regex = /https:\/\/www\.pornhub\.com\/view_video\.php\?viewkey=([^&]+).*/;
        // Use the replace method to create the new URL format
        console.log(link.match(regex))
         let captureGroup = (link.match(regex))[1]
         console.log(captureGroup)
         console.log(link.replace(regex, 'https://www.pornhub.com/embed/$1'))
        const newLink = `https://www.pornhub.com/embed/${captureGroup}`;
        console.log(newLink)
        return newLink;
    //  }
        }
    if(convertSource == 'youtube') {
            // Regular expression to match the original format and capture the unique part of the URL
            const regex = /https:\/\/www\.youtube\.com\/watch\?v=([^&]+).*/;
            // Use the replace method to create the new URL format
            console.log(link.match(regex))
            const newLink = link.replace(regex, 'https://www.youtube.com/embed/$1');          
              console.log(newLink)
            return newLink;
            }
    else{
        
        throw new Error('Invalid video source');
    }
};
let embededdURLS: any = null;
const youtubeURLS = (async () => {
    return await Promise.all(youtubeLinks!.map(async (link) => convertLink(link, 'youtube')));
})();


(async () => {

    console.log(await youtubeURLS)
    // const embededURLS = pornlinks.map((link) => convertLink(link, 'xvideos'));
 //const embededdURLS = pornlinks.map((link) => convertLink(link, 'xvideos'));
embededdURLS =  await Promise.all(pornhubLinks!.map(async (link) => convertLink(link, 'pornhub')));  // map function returns an array of promises, and you want to wait for all those promises to resolve before continuing.
// const embededURLS = [...(pornhubLinks!.map((link) => convertLink(link, 'pornhub'))), ...(pornlinks.map((link) => convertLink(link, 'xvideos')))];
    
})();

export type Params = {
    links?: videoSource;
    source?: 'database' | 'text'
}
async function linksToDisplay({links = 'youtube', source }: Params = {}): Promise<string[] | any> {
    'use server'


    console.log('hit')
    console.log(links)
    console.log(source)
    if((links == 'pornhub' || links == 'porn')  && source != 'database') {
        return embededdURLS
    }
    else if(links == 'xvideos' && source != 'database') {
        return youtubeURLS
    }
    else if(links == 'youtube' && source != 'database') {
        return youtubeURLS
    }
    else if (links == 'youtube' && source == 'database'
    ) {
        console.log('hit')
        let videos = await displayDataAction()
        videos = videos.filter((video) => video.link.includes('youtube'))
        console.log(videos)
        let convertedLinks = Promise.all(videos.map(async (video) => {
            return {
                ...video,
                link: await convertLink(video.link, 'youtube'),
            }
        }))
        console.log(await convertedLinks)
       // let filteredVideos = videos.map((video) => (video.link))
       // console.log(filteredVideos)
        //return await Promise.all(filteredVideos.map(async (link) => convertLink(link, 'youtube')))
        return (await convertedLinks)
    }
    else if (links == 'porn' && source == 'database'
    ) {
        console.log('hit')
        let videos = await displayDataAction()
        videos = videos.filter((video) => video.link.includes('xvideos') || video.link.includes('pornhub'))
        console.log(videos)
        let convertedLinks = Promise.all(videos.map(async (video) => {
            return {
                ...video,
                link: await convertLink(video.link, 'xvideos'),
            }
        }))
        console.log(await convertedLinks)
        return (await convertedLinks)
    }
    else {
         // throw new Error('Invalid video source'); 
    }
}

// const embededURLS = messyLinksArray
export default async function VideoView1() {
    return (
        <div>
            <ClientVideoDisplay embededUrlArray={linksToDisplay} />
        </div>
    );
}

