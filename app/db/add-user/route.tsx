"use server";

import { NextResponse } from "next/server";
import { mongoClient } from "@/lib/mongo";

const addData =
  (dbName: string) =>
  (collectionName: string) =>
  (filter: object) =>
  (update: object) =>
  (options: object = { upsert: true }) =>
  async () => {
    try {
      console.log(`Updating ${collectionName} in ${dbName} with`, update);

      const database = mongoClient.db(dbName);
      const collection = database.collection(collectionName);

      const result = await collection.updateOne(filter, update, options);
      console.log("Update result:", result);

      return result;
    } catch (error) {
      console.error(error);
      throw new Error("Database update failed");
    }
  };

// ✅ Define `addUser` using point-free style
const addUser = (userEmail: string, userPassword: string) =>
  addData("Next_JS_Portfolio")("Users")({})({
    $set: { email: userEmail, password: userPassword },
  })();

// ✅ API Route Handler
export async function POST(req: Request) {
  try {
    const { email, password } = await req.json();

    console.log('hitpromo')

    // Call `addUser` and execute it
    // let result = addUser(email, password);
    // result = await result()
    const result = await (addUser(email, password)());

    console.log('hitpromo')


    return NextResponse.json({ success: true, result }, { status: 200 });
  } catch (error) {
    return NextResponse.json({ error: "Invalid request" }, { status: 400 });
  }
}