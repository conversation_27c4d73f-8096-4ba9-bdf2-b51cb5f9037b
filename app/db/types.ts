import { ObjectId } from "mongodb";

export interface FAAQuestion {
  question: string;
  options: string[];
  correct_answer: string;
  fig?: string | string[];
}

export interface User {
  _id: string | ObjectId;
  email: string;
  password?: string;
  hashedPassword?: string;
  creditsRemaining: number;
  lastTrialResetAt: string;
  amtlastTrialResetAt?: string;
  fingerprint?: string;
  fingerprintLastUpdated?: string;
  ipAddress?: string;
  ipLastUpdated?: string;
  faaFavoriteQuestions?: FAAQuestion[];
}