"use server";

import { NextResponse } from "next/server";
import { mongoClient } from "@/lib/mongo";

const addData =
  (dbName: string) =>
  (collectionName: string) =>
  (filter: object) => 
  (update: object) =>
  (options: object = { upsert: true }) =>
  async () => {
    try {
      console.log(`Updating ${collectionName} in ${dbName} with`, update);

      const database = mongoClient.db(dbName);
      const collection = database.collection(collectionName);

      const result = await collection.updateOne(filter, update, options);
      console.log("Update result:", result);

      return result;
    } catch (error) {
      console.error(error);
      return NextResponse.json({ error: "Database update failed" }, { status: 500 });
    }
  };

// ✅ API Route Handler
export async function POST(req: Request) {
  try {
    const { dbName, collectionName, filter, update } = await req.json();

    // Execute the function chain
    const result = await addData(dbName)(collectionName)(filter)(update)()();

    return NextResponse.json({ success: true, result }, { status: 200 });
  } catch (error) {
    return NextResponse.json({ error: "Invalid request" }, { status: 400 });
  }
}