"use server";
import { ObjectId } from "mongodb";
import { mongoClient } from "@/lib/mongo";
import { transformResults2 } from "@/app/(main site)/Components/db_services/mongo";
import { debug } from "@/app/utils/debug";
import { hash, compare } from "bcrypt";
import { O } from "@measured/puck/dist/resolve-all-data-BoWgijLi";
import { User, FAAQuestion } from "@/app/db/types";

const SALT_ROUNDS = 10;

/**
 * A generic curried function for updating a MongoDB collection with a specified filter and update.
 * This function is designed to be flexible and reusable for various operations in different databases
 * and collections.
 *
 * @param filter - A MongoDB query object that determines which documents to update.
 * The filter will be applied to the collection to find matching documents. If no match is found and `upsert` is enabled,
 * - **Filter something**: Specify a condition like `{ name: 'MainSettings' }` to match specific documents.
 * - **Filter nothing**: Use an empty object `{}` to match all documents (or insert a new document if no match is found).
 * a new document will be created.
 * @param options - An optional object that specifies additional options for the update operation. if there is no filter
 * upsert will update the first one it finds if set to true, if false it will not insert anything
 * @example
 * // Example of using the generic `addData` function to add a category:
 * const addCategory = addData('Next_JS_Portfolio')('Settings');
 * await addCategory({ name: 'MainSettings' })({ $addToSet: { category: 'Tech' } })();
 */
const addData =
  (dbName: string) =>
  (collectionName: string) =>
  (filter: object) =>
  (update: object) =>
  (
    options: object = { upsert: true } // with upsert true it will create a new document if no match is found and if we get a match it updates it
  ) =>
  async () => {
    try {
      console.log(`Updating ${collectionName} in ${dbName} with`, update);

      const database = mongoClient.db(dbName);
      const collection = database.collection(collectionName);

      const result = await collection.updateOne(filter, update, options);
      console.log("Update result:", result);

      return result;
    } catch (error) {
      console.error(error);
      return error;
    }
  };

const addData_V0 =
  (dbName: string) =>
  async (collectionName: string) =>
  async (filter: object) =>
  async (update: object) =>
  async (options: object = { upsert: true }) => {
    try {
      console.log(`Updating ${collectionName} in ${dbName} with`, update);

      const database = mongoClient.db(dbName);
      const collection = database.collection(collectionName);

      const result = await collection.updateOne(filter, update, options);
      console.log("Update result:", result);

      return result;
    } catch (error) {
      console.error(error);
      throw new Error("Database update failed");
    }
  };

type addedData = {
  acknowledged: boolean;
  modifiedCount: number;
  upsertedId: null | string;
  upsertedCount: number;
  matchedCount: number;
};

const modifyData =
  (dbName: string) =>
  (collectionName: string) =>
  (filter: object) =>
  (update: object) =>
  (operation: "lookup" | "delete" | "add") =>
  (options: object = { upsert: true }) =>
  async () => {
    // async (operation: "lookup" | "add" = "add") => {
    try {
      console.log(`Performing ${operation} on ${collectionName} in ${dbName}`);

      const database = mongoClient.db(dbName);
      const collection = database.collection(collectionName);

      //returns null if nothing is found and returns the document if it is found
      if (operation === "lookup") {
        const existingData = await collection.findOne(filter);
        console.log("Lookup result:", existingData);
        //return existingData;
        if (existingData)
          return { ...existingData, _id: existingData._id.toString() };
        return existingData;
      }

      if (operation === "delete") {
        const result = await collection.deleteOne(filter);
        console.log("Delete result:", result);
        return result;
      }

      if (operation === "add") {
        console.log("hit promo");
        const result = await collection.updateOne(filter, update, options);
        console.log("Update/Add result:", result);
        return result;
      }

      throw new Error("Invalid operation. Use 'lookup' or 'add'.");
    } catch (error) {
      console.error(error);
      return error;
    }
  };

///original
// export const addNewCategory = (category: string) =>
//   addData('Next_JS_Portfolio')('Settings')({ name: 'MainSettings' })({ $addToSet: { category } })();

//this is the one that was working
// const addUser = async (userEmail: string, userPassword: string) =>
//     addData("Next_JS_Portfolio")("Users")({email: userEmail })({
//     $set: { email: userEmail, password: userPassword },
//   })();

////////////////////////////////////////////////////////////////////////////////////////////////////////////
// const addIpAddress = (userEmail: string, ipAddress: string) =>


  const lookUpCreditsReamaining =  (userID: ObjectId) =>
  modifyData("Next_JS_Portfolio")("Users")({ _id: userID })({})(`lookup`)();


const setTrialResetDate = (userID: ObjectId) =>
  modifyData("Next_JS_Portfolio")("Users")({ _id: userID })({
    $set: {
      lastTrialResetAt: new Date().toISOString(),
      creditsRemaining: 3,
    },
  })("add")();

const setAMTTrialResetDate = (userID: ObjectId) =>
  modifyData("Next_JS_Portfolio")("Users")({ _id: userID })({
    $set: {
      amtlastTrialResetAt: new Date().toISOString(),
    },
  })("add")();

  const setNewCreditAmmount = (userID: ObjectId,creditAmount: number) =>
  modifyData("Next_JS_Portfolio")("Users")({ _id: userID })({
    $set: {
      creditsRemaining: (creditAmount - 1),
    },
  })("add")();

const addFAAFavoriteQuestion = (userID: ObjectId, favoriteQuestion: FAAQuestion) =>
  modifyData("Next_JS_Portfolio")("Users")({ _id: userID })({
    $addToSet: {
      faaFavoriteQuestions: favoriteQuestion,
    },
  })("add")({ upsert: false });

const removeFAAFavoriteQuestion = (userID: ObjectId, favoriteQuestion: FAAQuestion) =>
  modifyData("Next_JS_Portfolio")("Users")({ _id: userID })({
    $pull: {
      faaFavoriteQuestions: favoriteQuestion,
    },
  })("add")({ upsert: false });

export async function getFAAFavoriteQuestions(userID: string): Promise<FAAQuestion[] | undefined> {
    const objectId = new ObjectId(userID);
    const user = await modifyData("Next_JS_Portfolio")("Users")({ _id: objectId })({})("lookup")()() as User | null;
    return user?.faaFavoriteQuestions;
}

export async function isCreditsRemaining(userID: string) {
  // this needs to be modified
  const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
  const $24HoursAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
  console.log("hit promo");

  const lookupUserID = (userID: ObjectId) =>
    modifyData("Next_JS_Portfolio")("Users")({
      _id: userID,
      lastTrialResetAt: {
        $gte: $24HoursAgo.toISOString(),
      },
    })({})("lookup")();
  const objectId = new ObjectId(userID);
  let result = await lookupUserID(objectId)() as User | null;
  console.log(result);
  if (result == null) {
    setTrialResetDate(objectId)();
    return true;
  }
  console.log(result);
  if (result ){
    await setNewCreditAmmount(objectId, result.creditsRemaining)();
    if (result.creditsRemaining > 0) {
      return true;
    }
    return false
  }
}

export async function isAMTCreditsRemaining(userID: string) {
  const $1WeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000); // 7 days ago
  const objectId = new ObjectId(userID);

  const findUser = modifyData("Next_JS_Portfolio")("Users")({ _id: objectId })({})("lookup")();
  const user = await findUser() as User | null;

  if (!user) {
    console.error(`User with ID ${userID} not found.`);
    return false;
  }

  if (!user.amtlastTrialResetAt) {
    await setAMTTrialResetDate(objectId)();
    return true;
  }

  return new Date(user.amtlastTrialResetAt) >= $1WeekAgo;
}
////////////////////////////////////////////////////////////////////
export async function addFAAQuestions(userID: string, question: FAAQuestion) {
  const objectId = new ObjectId(userID);
  let faaResult = await addFAAFavoriteQuestion(objectId, question)();
  console.log(faaResult);
}

export async function removeFAAQuestion(userID: string, question: FAAQuestion) {
  const objectId = new ObjectId(userID);
  let faaResult = await removeFAAFavoriteQuestion(objectId, question)();
  console.log(faaResult);
}

const addUser = (
  userEmail: string,
  userPassword: string,
  hashedPassword: string
) =>
  modifyData("Next_JS_Portfolio")("Users")({ email: userEmail })({
    $set: {
      email: userEmail,
      password: userPassword,
      hashedPassword: hashedPassword,
    },
  })("add")();

export async function lookupUserHashed(email: string, userPassword: string) {
  try {
    const lookupUserData = (userEmail: string) =>
      modifyData("Next_JS_Portfolio")("Users")({ email: userEmail })({})(
        "lookup"
      )();

    const user = await lookupUserData(email)();

    if (!user) return null;

    const passwordMatch = await compare(userPassword, user.password);
    if (!passwordMatch) return null;

    return { ...user, _id: user._id.toString() };
  } catch (error) {
    console.error("User lookup failed:", error);
    throw new Error("Authentication failed");
  }
}

export async function upsertUser(...args: Parameters<typeof addUser>) {
  try {
    const hashedPassword = await hash(args[1], SALT_ROUNDS);
    args.push(hashedPassword);
    const newuser = await addUser(...args)();
    console.log("User creation result:", newuser);
    return { ...newuser, upsertedId: newuser.upsertedId?.toString() };
  } catch (error) {
    console.error("User creation failed:", error);
    throw new Error("Failed to create user");
  }
}

// ... (keep other existing functions)

const addFingerprint = (userEmail: string, fingerprint: string) =>
  modifyData("Next_JS_Portfolio")("Users")({ email: userEmail })({
    $set: {
      email: userEmail,
      fingerprint: fingerprint,
      fingerprintLastUpdated: new Date().toISOString(),
    },
  })("add")();

export async function lookupRecentFingerprint(fingerprint: string) {
  const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);

  const lookupFingerprint = (fingerprint: string) =>
    modifyData("Next_JS_Portfolio")("Users")({
      fingerprint: fingerprint,
      fingerprintLastUpdated: {
        $gte: fiveMinutesAgo.toISOString(),
      },
    })({})("lookup")();

  let result = await lookupFingerprint(fingerprint)();
  console.log("Recent fingerprint lookup result:", result);
  if (result)
    throw new Error(
      "Your trying to make too many accounts, please try again later"
    );
  return result;
}

// export const addUser = (userEmail: string, userPassword: string) => {
//   console.log("Adding user:", { email: userEmail, password: userPassword });

//   return  addData("Next_JS_Portfolio")("Users")({})({
//     $set: { email: userEmail, password: userPassword },
//   })();
// };

/**
 *checks if a useremail exits. If no email is found it will create a new user
 *
 */
export async function lookupUser(email: string, userPassword: string) {
  console.log(email, userPassword);
  const lookupUser = (userEmail: string, userPassword: string) =>
    modifyData("Next_JS_Portfolio")("Users")({
      email: userEmail,
      password: userPassword,
    })({})(`lookup`)();
  let newuser = await lookupUser(email, userPassword)();
  console.log(newuser);
  // if (newuser) return {...newuser, _id: newuser._id.toString()};
  // else return (newuser)
  return newuser;
}

export async function lookupEmailOnly(email: string) {
  const lookupUser = (userEmail: string) =>
    modifyData("Next_JS_Portfolio")("Users")({ email: userEmail })({})(
      `lookup`
    )();
  let newuser = await lookupUser(email)();
  console.log(newuser);
  // if (newuser) return {...newuser, _id: newuser._id.toString()};
  // else return (newuser)
  return newuser;
}

export async function deleteUser(...args: Parameters<typeof addUser>) {
  let emailArg = args[0];
  if (emailArg.toLowerCase() == "<EMAIL>") {
    return "you cannot delete a test account";
  }
  console.log(emailArg);
  const deleteUser = async (userEmail: string, userPassword: string) =>
    modifyData("Next_JS_Portfolio")("Users")({
      email: userEmail,
      password: userPassword,
    })({})("delete")();
  let newuser = (await deleteUser(...args))();
  return newuser;
}

export async function upsertFingerprint(
  ...args: Parameters<typeof addFingerprint>
) {
  console.log("running");
  let newuser = addFingerprint(...args)();
  let result = (await newuser) as addedData;
  console.log(result);
  if (result.upsertedId)
    return { ...result, upsertedId: result.upsertedId.toString() };
  if (!result.upsertedId) return { ...result };

  //newuser()
  // const addUser = (userEmail: string, userPassword: string) =>
  //await newuser
}

const addIpAddress = (userEmail: string, ipAddress: string) =>
  modifyData("Next_JS_Portfolio")("Users")({ email: userEmail })({
    $set: {
      email: userEmail,
      ipAddress: ipAddress,
      ipLastUpdated: new Date().toISOString(),
    },
  })("add")();

export async function lookupRecentIpAddress(ipAddress: string) {
  const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);

  const lookupIp = (ipAddress: string) =>
    modifyData("Next_JS_Portfolio")("Users")({
      ipAddress: ipAddress,
      ipLastUpdated: {
        $gte: fiveMinutesAgo.toISOString(),
      },
    })({})("lookup")();

  let result = await lookupIp(ipAddress)();
  console.log("Recent IP lookup result:", result);
  if (result)
    throw new Error(
      "Too many accounts from this IP address, please try again later"
    );
  return result;
}

export async function upsertIpAddress(
  ...args: Parameters<typeof addIpAddress>
) {
  console.log("upserting IP address");
  let newuser = addIpAddress(...args)();
  let result = (await newuser) as addedData;
  console.log(result);
  if (result.upsertedId)
    return { ...result, upsertedId: result.upsertedId.toString() };
  if (!result.upsertedId) return { ...result };
}

/**
 * Server action to add a new user
 */
// export const addUser = async (userEmail: string, userPassword: string) => {
//   const addToUsers =  addData("Next_JS_Portfolio");
//   const addToUsersCollection = await addToUsers("Users");
//   const applyFilter = await addToUsersCollection({});
//   const applyUpdate = await applyFilter({
//     $set: { email: userEmail, password: userPassword },
//   });
//   return await applyUpdate(); // Execute the final step
// };
