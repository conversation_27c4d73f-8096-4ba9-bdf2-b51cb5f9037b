
import { NextResponse } from 'next/server'
import { headers } from 'next/headers'
import { prisma } from '@/lib/prisma'

export async function GET(request: Request) {
    const headersList = headers()
    const forwardedFor = headersList.get('x-forwarded-for')
    const realIP = headersList.get('x-real-ip')
    const userIP = forwardedFor?.split(',')[0] || realIP || 'Unknown'
    const last_accessed = new Date()

    try {
        return NextResponse.json({
            success: true,
            data: {
                ip: userIP,
                last_accessed
            }
        })

    } catch (error) {
        console.error('Database error:', error)
        return NextResponse.json({ 
            success: false,
            error: 'Failed to process IP request' 
        }, { 
            status: 500 
        })
    }
}

// Add POST method to manually check an IP
export async function POST(request: Request) {
    try {
        const { ip_address } = await request.json()
        
        const ipRecord = await prisma.ipRequest.findUnique({
            where: {
                ip_address: ip_address
            }
        })

        if (!ipRecord) {
            return NextResponse.json({
                success: false,
                error: 'IP address not found'
            }, {
                status: 404
            })
        }

        return NextResponse.json({
            success: true,
            data: ipRecord
        })

    } catch (error) {
        console.error('Database error:', error)
        return NextResponse.json({ 
            success: false,
            error: 'Failed to retrieve IP data' 
        }, { 
            status: 500 
        })
    }
}

