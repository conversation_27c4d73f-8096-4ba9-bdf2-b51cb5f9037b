# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.env

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts
__pycache__
/test-results/
/playwright-report/
/playwright/.cache/

# emails

/emails.txt

/scripts
/contracts
/cache
/artifacts

# /app/(main site)/MetaMask2
# /app/(main site)/MetaMask3
# package-lock.json

# Auto Generated PWA files
**/public/sw.js
**/public/workbox-*.js
