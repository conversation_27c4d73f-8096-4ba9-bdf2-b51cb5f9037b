{"name": "malcolm_ui_react", "private": false, "version": "0.0.11", "scripts": {"dev": "vite", "build": "tsc -b", "lint": "eslint .", "preview": "vite preview"}, "types": "dist/ui/index.d.ts", "main": "dist/ui/index.js", "files": ["dist/ui"], "dependencies": {"@preact-signals/safe-react": "^0.7.0", "malcolm_ui_react": "^0.0.10", "react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@eslint/js": "^9.9.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1"}}