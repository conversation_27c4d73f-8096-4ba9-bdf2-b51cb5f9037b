{"name": "next-flask", "version": "0.1.0", "private": true, "scripts": {"flask-dev": "FLASK_DEBUG=1 pip3 install -r requirements.txt && python3 -m flask --app api/python/index run -p 5328", "fast-api": "cd /home/<USER>/svelt-python/fastapi-mongo && source python_backend_project/bin/activate  && uvicorn main:app --host 0.0.0.0 --port 8000 ", "next-dev": "next dev", "dev": "concurrently \"pnpm run next-dev\" \"pnpm run flask-dev\"", "dev2": "concurrently \"pnpm run next-dev\" \"pnpm run fast-api\"", "build": "prisma generate && next build", "start": "next start", "lint": "next lint", "test": "jest __tests__", "codgen": "npx playwright codegen http://localhost:3000 --viewport-size=1500,800", "playwright": "playwright test signin.spec.ts", "test:watch": "jest --watch", "test:parallel": "playwright test signin.spec.ts & jest __tests__/curryAPI.test.jsx --testEnvironment=node", "test: concurrent windows": "start playwright test signin.spec.ts & jest __tests__/curryAPI.test.jsx --testEnvironment=node", "test: concurrent all": "concurrently \"playwright test signin.spec.ts\" \"jest __tests__/curryAPI.test.jsx --testEnvironment=node\"", "test: concurrent": "playwright test signin.spec.ts && jest __tests__/curryAPI.test.jsx --testEnvironment=node", "test-run": "./app/scripts/run_test.sh", "test:malcmindLogin": "./app/scripts/malcmindLogin.test.sh", "test:fingerprint-full": "./app/scripts/fingerPrint.test.sh", "test:lookupUser": "jest __tests__/IP.test.ts --testEnvironment=node -t 'IP Address API Tests' --forceExit", "test:fingerprint": "jest __tests__/fingerprint.test.ts --testEnvironment=jsdom -t 'Fingerprint Service' --forceExit", "test:react": "jest __tests__/page.test.jsx --testEnvironment=jsdom --forceExit", "test:savefingerprint": "jest __tests__/curryAPI.test.ts --testEnvironment=node -t 'Save Fingerprint' --forceExit", "deduplicate": "ts-node --compiler-options '{\"module\":\"CommonJS\"}' app/components/AMTQuestionStream/deduplicateScript.ts"}, "prisma": {"seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed.ts"}, "dependencies": {"@ckeditor/ckeditor5-build-classic": "^41.1.0", "@ckeditor/ckeditor5-react": "^6.2.0", "@clerk/nextjs": "^6.9.0", "@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@google/genai": "^1.27.0", "@google/generative-ai": "^0.16.0", "@heroicons/react": "^2.2.0", "@measured/puck": "^0.16.2", "@monaco-editor/react": "^4.6.0", "@mui/icons-material": "^7.0.2", "@mui/material": "^7.0.2", "@next/third-parties": "^15.2.2", "@nomiclabs/hardhat-ethers": "^2.2.3", "@nomiclabs/hardhat-waffle": "^2.0.6", "@preact-signals/safe-react": "^0.7.0", "@prisma/client": "^5.2.0", "@react-three/drei": "^9.78.2", "@react-three/fiber": "^8.13.4", "@stripe/stripe-js": "^5.3.0", "@tiptap/extension-color": "^2.2.4", "@tiptap/pm": "^2.2.4", "@tiptap/react": "^2.2.4", "@tiptap/starter-kit": "^2.2.4", "@types/jest": "^29.5.14", "@types/node": "20.1.4", "@types/react": "npm:types-react@19.0.0-rc.1", "@types/react-dom": "npm:types-react-dom@19.0.0-rc.1", "@vercel/postgres": "^0.4.1", "ai": "^2.2.9", "aos": "^2.3.4", "autoprefixer": "10.4.14", "axios": "^1.7.7", "babel-plugin-react-compiler": "^19.0.0-beta-a7bf2bd-20241110", "bcrypt": "^5.1.1", "body-parser": "^1.20.2", "chai": "^4.3.8", "concurrently": "^8.0.1", "d3": "^7.9.0", "dotenv": "^16.3.1", "easymde": "^2.18.0", "eslint": "8.40.0", "eslint-config-next": "15.0.3", "ethereum-waffle": "^4.0.10", "ethers": "^5.7.2", "firebase": "^10.1.0", "firebaseui": "^6.1.0", "font-awesome": "^4.7.0", "fp-ts": "^2.16.9", "framer-motion": "^10.12.18", "google-auth-library": "9.15.1", "groq-sdk": "^0.34.0", "install": "^0.13.0", "isomorphic-fetch": "^3.0.0", "jotai": "^2.6.1", "katex": "^0.16.22", "malcolm_ui_react": "^0.0.11", "monaco-editor": "^0.44.0", "mongodb": "^6.12.0", "next": "15.0.5", "next-auth": "^4.24.5", "next-connect": "^1.0.0", "next-mdx-remote": "^4.4.1", "next-pwa": "^5.6.0", "nodemailer": "^6.9.4", "openai": "^4.52.2", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "postcss": "8.4.23", "preact": "^10.24.0", "preact-compat": "^3.19.0", "prisma": "^5.8.1", "prismjs": "^1.29.0", "react": "19.0.0-rc-66855b96-20241106", "react-dom": "19.0.0-rc-66855b96-20241106", "react-froala-wysiwyg": "^4.1.4", "react-google-charts": "^4.0.1", "react-markdown": "^10.1.0", "react-redux": "^8.1.1", "react-simplemde-editor": "^5.2.0", "rehype-katex": "^7.0.1", "remark-gfm": "^4.0.1", "remark-math": "^6.0.0", "resend": "^4.2.0", "sass": "^1.71.1", "sequelize": "^6.32.1", "simplemde": "^1.11.2", "socket.io-client": "^4.7.2", "stripe": "^13.4.0", "swr": "^2.2.0", "tailwindcss": "3.3.2", "three": "^0.154.0", "typescript": "^5.3.2", "viem": "^1.16.0", "web3": "^4.2.2", "xterm": "^5.3.0", "zustand": "^5.0.1"}, "devDependencies": {"@playwright/test": "^1.40.1", "@tailwindcss/typography": "^0.5.10", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@types/bcrypt": "^5.0.0", "daisyui": "^4.12.14", "hardhat": "^2.17.2", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "ts-node": "^10.9.2", "@types/bun": "latest"}, "overrides": {"@types/react": "npm:types-react@19.0.0-rc.1", "@types/react-dom": "npm:types-react-dom@19.0.0-rc.1"}}