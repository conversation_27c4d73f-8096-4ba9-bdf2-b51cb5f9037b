{"name": "corn-nextjs", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "prisma generate && next build", "start": "next start", "lint": "next lint", "migrations": "npx prisma migrate dev --name init", "databaseview": "npx prisma studio"}, "prisma": {"seed": "ts-node --compiler-options {\"module\":\"CommonJS\"} prisma/seed2.ts"}, "dependencies": {"@prisma/client": "5.16.1", "babel-plugin-react-compiler": "^19.0.0-beta-a7bf2bd-20241110", "bcrypt": "^5.1.1", "jotai": "^2.9.0", "next": "15.0.3", "react": "19.0.0-rc-66855b96-20241106", "react-dom": "19.0.0-rc-66855b96-20241106"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/node": "^20", "@types/react": "npm:types-react@19.0.0-rc.1", "@types/react-dom": "npm:types-react-dom@19.0.0-rc.1", "daisyui": "^4.12.10", "eslint": "^8", "eslint-config-next": "15.0.3", "postcss": "^8", "prisma": "^5.16.1", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "typescript": "^5"}, "overrides": {"@types/react": "npm:types-react@19.0.0-rc.1", "@types/react-dom": "npm:types-react-dom@19.0.0-rc.1"}}