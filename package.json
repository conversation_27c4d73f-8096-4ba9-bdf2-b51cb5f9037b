{"name": "cryptoa<PERSON>", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "prisma generate && node app2.js"}, "author": "", "license": "ISC", "dependencies": {"@prisma/client": "^5.9.1", "axios": "^1.6.7", "brain.js": "^2.0.0-beta.23", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.18.2", "node-cache": "^5.1.2", "playwright": "^1.41.1", "ws": "^8.16.0"}, "devDependencies": {"prisma": "^5.9.1"}}