const { chromium } = require('playwright');
const fs = require('fs');


async function getDataFromResource() {
  // Launch a headless browser (you can change 'chromium' to 'firefox' or 'webkit' if needed)
  const browser = await chromium.launch();

  // Create a new browser context
  const context = await browser.newContext();

  // Create a new page
  const page = await context.newPage();

  const response = await page.goto('https://www.usinflationcalculator.com/charts/inflation/1-month-annual-inflation-rates-yoy.html');

  const headers = response.headers();
  const htmlContent = await page.content();

  const functionName = 'drawChart';
  const regexPattern = new RegExp(`function\\s+${functionName}\\([^]*?\\)\\s*{[^]*var\\s+data\\s+=\\s+([^;]+);[^]*}`, 'i');

  // Find the function content using regex
  const match = htmlContent.match(regexPattern);

  if (match) {
    // Extracted data array as a string
    let dataArrayString = match[1].trim();
    dataArrayString = dataArrayString.replace(`google.visualization.arrayToDataTable(`, '');
    dataArrayString = dataArrayString.slice(0, -1);
    // dataArrayString = JSON.parse(dataArrayString);
    // Extract just the array content using regex
    dataArrayString = eval(dataArrayString)
  

      // Parse the array content string into a JavaScript array
      // const dataArray = JSON.parse(arrayContentString);

      // Output the extracted data array to the console
      console.log(dataArrayString);

      fs.writeFileSync(`./InflationData/inflationData.js`, JSON.stringify(dataArrayString, null, 2));

  
  } 

  // Close the browser
  await browser.close();
}

// Call the function
getDataFromResource();