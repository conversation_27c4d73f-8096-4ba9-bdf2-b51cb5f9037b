const axios = require('axios');
const fs = require('fs');
// Function to fetch historical Ethereum price data from CoinGecko API
async function getHistoricalData(coin='ethereum', days=2000) {
  try {
    const response = await axios.get(`https://api.coingecko.com/api/v3/coins/${coin}/market_chart`, {
      // const response = await axios.get(`https://api.coingecko.com/api/v3/coins/internet-computer/market_chart`, {

      params: {
        vs_currency: 'usd',
        days: days,
        interval: 'daily',
      },
    });
    // const tradingVolumes = response.data.total_volumes || [];
    // console.log(tradingVolumes)
    const historicalData = response.data;
    // const historicalData = response.data.prices;

    console.log('getting historical data')
    // Convert the data to a JSON string
    const dataString = JSON.stringify(historicalData, null, 2);
    // Write the data to a JavaScript file
    fs.writeFileSync(`./RawPriceData/${coin}PriceData.js`, `const ${coin}PriceData = ${dataString};\n\nmodule.exports = ${coin}PriceData;`);
    // console.log('Historical Ethereum Price Data has been saved to ethereumPriceData.js');
  } catch (error) {
    console.error(`Error fetching historical ${coin} price data:`, error.message);
  }
}

//  getHistoricalData('bitcoin')

module.exports = { 
  getHistoricalData
};


