'use client';

import React, { useState, useEffect, useRef, useCallback } from 'react';
import handlefetch_ai_data from '@/app/(main site)/(Landing Pages)/ai-article-generator/servercontroller';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { PaperAirplaneIcon } from '@heroicons/react/24/solid';

interface Message {
    sender: 'user' | 'ai';
    text: string;
}

const InteractiveOPChat = () => {
    const [messages, setMessages] = useState<Message[]>([]);
    const [userInput, setUserInput] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const chatEndRef = useRef<HTMLDivElement>(null);

    const initialPrompt = `You are an FAA Designated Mechanic Examiner (DME). Your role is to conduct an oral and practical exam for an A&P mechanic. Ask me questions one by one from the FAA A&P General, Airframe, and Powerplant curriculums. After I provide an answer, give me feedback on my response (is it correct, what could be improved), and then ask the next question. Start now with your first question from the General section.`;

    const fetchInitialQuestion = useCallback(async () => {
        setIsLoading(true);
        try {
            const result = await handlefetch_ai_data({
                selectedOption: 'openai/gpt-oss-120b-VOICEAPP',
                textInput: initialPrompt,
            });
            setMessages([{ sender: 'ai', text: result }]);
        } catch (error) {
            console.error("Failed to fetch initial question:", error);
            setMessages([{ sender: 'ai', text: "I'm having trouble connecting right now. Please try again later." }]);
        } finally {
            setIsLoading(false);
        }
    }, []);

    useEffect(() => {
        fetchInitialQuestion();
    }, [fetchInitialQuestion]);

    useEffect(() => {
        chatEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }, [messages, isLoading]);

    const handleSendMessage = async () => {
        if (!userInput.trim() || isLoading) return;

        const newMessages: Message[] = [...messages, { sender: 'user', text: userInput }];
        setMessages(newMessages);
        setUserInput('');
        setIsLoading(true);

        const conversationHistory = newMessages.map(msg => `${msg.sender === 'user' ? 'Applicant' : 'Examiner'}: ${msg.text}`).join('\n\n');
        const prompt = `${conversationHistory}\n\nExaminer: (Provide feedback on the applicant's last answer and ask the next question.)`;

        try {
            const result = await handlefetch_ai_data({
                selectedOption: 'openai/gpt-oss-120b-VOICEAPP',
                textInput: prompt,
            });
            setMessages(prev => [...prev, { sender: 'ai', text: result }]);
        } catch (error) {
            console.error("Failed to get AI response:", error);
            setMessages(prev => [...prev, { sender: 'ai', text: "Sorry, I encountered an error. Let's try that again." }]);
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="flex flex-col h-[calc(100vh-250px)] bg-gray-100 rounded-lg shadow-inner">
            <div className="flex-1 p-4 overflow-y-auto space-y-4">
                {messages.map((msg, index) => (
                    <div key={index} className={`flex items-end gap-2 ${msg.sender === 'user' ? 'justify-end' : 'justify-start'}`}>
                        {msg.sender === 'ai' && <div className="w-8 h-8 rounded-full bg-gray-600 text-white flex items-center justify-center shrink-0">AI</div>}
                        <div className={`rounded-lg p-3 max-w-xl prose ${msg.sender === 'user' ? 'bg-blue-600 text-white' : 'bg-white text-gray-800 shadow-sm'}`}>
                            <ReactMarkdown remarkPlugins={[remarkGfm]}>{msg.text}</ReactMarkdown>
                        </div>
                    </div>
                ))}
                {isLoading && (
                    <div className="flex items-end gap-2 justify-start">
                        <div className="w-8 h-8 rounded-full bg-gray-600 text-white flex items-center justify-center shrink-0">AI</div>
                        <div className="rounded-lg p-3 bg-white text-gray-800 shadow-sm">
                            <div className="flex items-center space-x-1">
                                <span className="h-2 w-2 bg-gray-400 rounded-full animate-pulse [animation-delay:-0.3s]"></span>
                                <span className="h-2 w-2 bg-gray-400 rounded-full animate-pulse [animation-delay:-0.15s]"></span>
                                <span className="h-2 w-2 bg-gray-400 rounded-full animate-pulse"></span>
                            </div>
                        </div>
                    </div>
                )}
                <div ref={chatEndRef} />
            </div>
            <div className="p-4 bg-white border-t border-gray-200">
                <div className="flex items-center gap-2">
                    <textarea
                        value={userInput}
                        onChange={(e) => setUserInput(e.target.value)}
                        onKeyPress={(e) => {
                            if (e.key === 'Enter' && !e.shiftKey) {
                                e.preventDefault();
                                handleSendMessage();
                            }
                        }}
                        placeholder="Type your answer..."
                        className="flex-1 p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                        rows={1}
                        disabled={isLoading}
                    />
                    <button
                        onClick={handleSendMessage}
                        disabled={isLoading || !userInput.trim()}
                        className="p-2 bg-blue-600 text-white rounded-full disabled:bg-gray-400 disabled:cursor-not-allowed hover:bg-blue-700 transition-colors"
                        aria-label="Send message"
                    >
                        <PaperAirplaneIcon className="h-6 w-6" />
                    </button>
                </div>
            </div>
        </div>
    );
};

export default InteractiveOPChat;

