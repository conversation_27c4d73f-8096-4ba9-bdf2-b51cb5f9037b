class Utils {

    constructor(crypto){
        this.crypto = crypto
        this.mydata = require(`./RawPriceData/${this.crypto}PriceData`).prices

        this.myvolume = require(`./RawPriceData/${this.crypto}PriceData`).total_volumes;
        this.maxvolume = 0;
        this.minvolume = 10000;
        this.setMaxMinVolume(this.myvolume);

        this.max = 0;
        this.min = 10000;
        this.recentPrice = this.mydata[this.mydata.length - 1][1]
        this.recentDate = this.calculateDate(this.mydata[this.mydata.length - 1][0])
        this.dateUnEdited = this.mydata[this.mydata.length - 1][0]
        this.setMaxMin(this.mydata)

        

    }
    
    setMaxMinVolume(){
        for(let volume of this.myvolume)
        {
            if(volume[1] > this.maxvolume)
            {
                this.maxvolume = volume[1]
            }
            if(volume[1] < this.minvolume)
            {
                this.minvolume = volume[1]
            }
        }
        console.log('this maxvolume',this.maxvolume)
        console.log('this minvolume',this.minvolume)
    }



    setMaxMin(){
        for(let price of this.mydata)
        {
            if(price[1] > this.max)
            {
                this.max = price[1]
            }
            if(price[1] < this.min)
            {
                this.min = price[1]
            }
        }
        console.log('this max',this.max)
        console.log('this min',this.min)
        console.log('recentPrice',this.recentPrice)  
    }    
    normalizeVolume(value){
        return ((value - this.minvolume) / (this.maxvolume - this.minvolume));
      }

    denormalizeVolume(value) {
        return ((value * (this.maxvolume - this.minvolume)) + this.minvolume);
    }


    normalize(value){
        // let max = 5000;
        // let min = 100
        return ((value - this.min) / (this.max - this.min));
      }
    
    denormalize(value) {
        // let max = 5000;
        // let min = 100
        return ((value * (this.max - this.min)) + this.min);
    }
    
    calculateDate(timestamp){
        const date = new Date(timestamp);
        const year = date.getFullYear();
        const month = date.getMonth() + 1; // Months are zero-indexed, so add 1
        const day = date.getDate();
        const hours = date.getHours();
        const minutes = date.getMinutes();
        const seconds = date.getSeconds();
        const formattedDate = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        return formattedDate
    }

}



// function manipulate(){
//     console.log('old min', min)
//     min = 300
//     console.log('new min', min)
// }

// function manipulate2(){
//     let min = 100
//     console.log('old min', min)
//     min = 200
//     console.log('new min', min)
// }





module.exports = {
    Utils,

};