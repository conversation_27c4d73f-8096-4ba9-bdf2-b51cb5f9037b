// npx playwright codegen http://localhost:3000 --viewport-size=1500,800
//bunx playwrite test signin.spec.ts l

import { test, expect } from '@playwright/test';
import {text} from '@/app/components/auth/signIn.config'

console.log(text)

test.use({
  viewport: {
    height: 800,
    width: 1500
  }
});


test('normal signup test', async ({ page }) => {
  await page.goto('http://localhost:3000/malcmind-login');
  await page.evaluate(() => localStorage.clear())
  await expect(page.locator('body')).not.toContainText('Logout');
  await expect(page.locator('body')).toContainText(text.login);
  await expect(page.locator('body')).toContainText(text.signUp);
  await page.getByPlaceholder('Email').click();
  await page.getByPlaceholder('Email').fill(process.env.MY_EMAIL as string);
  await page.getByPlaceholder('Password').click();
  await page.getByPlaceholder('Password').fill(process.env.MY_PASSWORD as string);
  await page.getByRole('button', { name: text.signUp }).click();
  await expect(page.getByRole('heading', { name: 'Account Created!' })).toBeVisible();
  await page.getByTestId('accountclose').click()
  // await page.locator('.modal[open]')
  // .getByRole('button', { name: 'Close' })
  // .click();
  // await page.getByRole('button', { name: 'Close' }).click()
  // await expect(page.getByRole('heading', { name: 'Account Created!' })).toBeHidden();
  await expect(page.locator('.modal[open]')
  .getByRole('heading', { name: 'Account Created!' }))
  .toBeHidden();
  await expect(page.locator('body')).toContainText('Log Out');
  await expect(page.locator('body')).not.toContainText(/^\s*Login\s*$/);
  await expect(page.locator('body')).not.toContainText('Sign Up');
// });


// test('logout test', async ({ page }) => {
//   await page.goto('http://localhost:3000/malcmind-login');
  // await expect(page.locator('body')).toContainText('Log Out');
  // console.log(await page.locator('button').allInnerTexts());

  // await page.getByRole('button', { name: 'Log Out' }).waitFor({ state: 'visible' });
  await page.getByRole('button', { name: 'Log Out' }).click();
  await expect(page.getByRole('heading', { name: 'Log Out Successful!' })).toBeVisible();
  await expect(page.locator('body')).not.toContainText('Logout');
  await expect(page.locator('body')).toContainText('Login');
  await expect(page.locator('body')).toContainText('Sign Up');
});


test('Bad Email SignUp', async ({ page }) => {
  await page.goto('http://localhost:3000/malcmind-login');
  await page.evaluate(() => localStorage.clear())
  // i put it twice so you can see how the alternate placeholder finder works
  await expect(page.getByPlaceholder('Please enter a valid email.')).not.toBeVisible();
  await expect(page.locator('input[placeholder="Please enter a valid email."]')).not.toBeVisible();
  await expect(page.getByPlaceholder('Email')).toBeVisible();
  await expect(page.locator('body')).toContainText(text.signUp);
  await page.getByPlaceholder('Email').click();
  await page.getByPlaceholder('Email').fill("dlfjsf" as string);
  await page.getByPlaceholder('Password').click();
  await page.getByPlaceholder('Password').fill(process.env.MY_PASSWORD as string);
  await page.getByRole('button', { name: text.signUp }).click();
    // i put it twice so you can see how the alternate placeholder finder works

  await expect(page.getByPlaceholder('Please enter a valid email.')).toBeVisible();
  await expect(page.locator('input[placeholder="Please enter a valid email."]')).toBeVisible();

});


test('incorrect password test', async ({ page }) => {
  await page.goto('http://localhost:3000/malcmind-login');
  await page.getByPlaceholder('Email').click();
  await page.getByPlaceholder('Email').fill(process.env.MY_EMAIL as string);
  await page.getByPlaceholder('Password').click();
  await page.getByPlaceholder('Password').fill(process.env.INCORRECT_PASSWORD as string);
  await page.getByRole('button', { name: 'Login' }).click();
  await expect(page.getByRole('heading', { name: 'Incorrect Email or Password' })).toBeVisible();
  await page.getByRole('button', { name: 'Try Again' }).click();
  await expect(page.getByRole('heading', { name: 'Incorrect Email or Password' })).toBeHidden();
  await expect(page.locator('body')).not.toContainText('Logout');
  await expect(page.locator('body')).toContainText('Login');
  await expect(page.locator('body')).toContainText('Sign Up');
});

test('Something Went Wrong Test', async ({ page }) => {
  await page.goto('http://localhost:3000/malcmind-login');
  await page.getByPlaceholder('Email').click();
  await page.getByPlaceholder('Email').fill(process.env.MY_EMAIL as string);
  await page.getByPlaceholder('Password').click();
  await page.getByPlaceholder('Password').fill(process.env.FORCE_FAIL_PASSWORD as string);
  await page.getByRole('button', { name: 'Login' }).click();
  await expect(page.getByRole('heading', { name: 'Something Went Wrong' })).toBeVisible();
  await page.getByRole('button', { name: 'Try Again' }).click();
  await expect(page.getByRole('heading', { name: 'Something Went Wrong' })).toBeHidden();
  await expect(page.locator('body')).not.toContainText('Logout');
  await expect(page.locator('body')).toContainText('Login');
  await expect(page.locator('body')).toContainText('Sign Up');
});

test('normal login test', async ({ page }) => {
  await page.goto('http://localhost:3000/malcmind-login');
  await expect(page.locator('body')).not.toContainText('Logout');
  await expect(page.locator('body')).toContainText('Login');
  await expect(page.locator('body')).toContainText('Sign Up');
  await page.getByPlaceholder('Email').click();
  await page.getByPlaceholder('Email').fill(process.env.MY_EMAIL as string);
  await page.getByPlaceholder('Password').click();
  await page.getByPlaceholder('Password').fill(process.env.MY_PASSWORD as string);
  await page.getByRole('button', { name: 'Login' }).click();
  await expect(page.getByRole('heading', { name: 'Incorrect Email or Password' })).toBeVisible();
  await page.getByRole('button', { name: 'Try Again' }).click();
  await expect(page.getByRole('heading', { name: 'Incorrect Email or Password' })).toBeHidden();
  await expect(page.locator('body')).toContainText('Logout');
  await expect(page.locator('body')).not.toContainText('Login');
  await expect(page.locator('body')).not.toContainText('Sign Up');
});





test('account does not exist test', async ({ page }) => {
  await page.goto('http://localhost:3000/malcmind-login');
  await page.getByPlaceholder('Email').click();
  await page.getByPlaceholder('Email').fill(process.env.MY_EMAIL as string);
  await page.getByPlaceholder('Password').click();
  await page.getByPlaceholder('Password').fill(process.env.MY_PASSWORD as string);
  //Account does not exist would you like to create one Teset
  await page.getByRole('button', { name: 'Login' }).click();
  await expect(page.getByRole('heading', { name: 'Account Does Not Exist' })).toBeVisible();
  await page.getByRole('button', { name: 'Confirm' }).click();
  await expect(page.getByRole('heading', { name: 'Account Created!' })).toBeVisible();
  await page.getByRole('button', { name: 'Close' }).click()
  await expect(page.locator('body')).toContainText('Logout');
  await expect(page.locator('body')).not.toContainText('Login');
  await expect(page.locator('body')).not.toContainText('Sign Up');
  //2s timeout
  await page.waitForTimeout(2000);
  const updatedCount = await page.evaluate(() => window.__myComponentState.email);
  expect(updatedCount).toBe('<EMAIL>');
//   page.on('console', (msg) => {
//     if (msg.type() === 'info') {
//     expect(msg.text()).toBe('Signed in as j');

//     //    console.error(`Error log: ${msg.text()}`);
//      }
//   });
});


// test('Capture console logs', async ({ page }) => {
//     // Listen for console messages
//     page.on('console', (msg) => {
//       console.log(`Console log: ${msg.type()}: ${msg.text()}`);
//     });
  
//     // Go to your page
//     await page.goto('http://localhost:3000');
  
//     // Trigger a log in the browser (for testing)
//     await page.evaluate(() => console.log('This is a test log'));
  
//     // Optionally, perform actions that should trigger logs
//   });