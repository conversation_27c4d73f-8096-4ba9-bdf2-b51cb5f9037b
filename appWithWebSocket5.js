const express = require('express')
const brain = require('brain.js');
const fs = require('fs');
const { trainCrypto } = require('./trainCrypto');
const app = express();
// const { getHistoricalData } = require('./getHistoricalData');
const { getHistoricalData } = require('./getHistoricalDataFix');
const { dataManipulation } = require('./dataManipulation');
const { Utils } = require('./utils');
const { get } = require('http');
const http = require('http');
const dotenv = require('dotenv');
dotenv.config();
const cors = require('cors');
app.use(cors());




const server = http.createServer(app);

const WebSocket = require('ws');
const wss = new WebSocket.Server({ server });

wss.timeout = 1000 *60 *5

//
// async function processData() {
//   await getHistoricalData();
//   await ethDataManipulation();
//   const retrieveModel = require('./trainedEth.json')
//   const brainReconstruction = new brain.NeuralNetworkGPU();
//   brainReconstruction.fromJSON(retrieveModel);
// }
const port = process.env.PORT || 3532;
// Function to start the server
function startServer() {

    console.log('fuck')
    
    const userSocketMap = new Map();
    const userSocketTracker = {};
    const AllowedSockets ={}
    const superAdmin = process.env.Superrr
  
    function generateId() {
    return Math.random().toString(36).substr(2, 9);
}

    wss.on('connection', (ws) => {
        console.log('Client connected')
        let id = generateId();
        console.log('overwriteid', id)
        // Store the user-specific socket in the map
        userSocketMap.set(id, ws);
        ws.send(JSON.stringify({ message: 'WebSocket Connection Successful' }));
        ws.on('message', (message) => {
            console.log('we are up and running')
            try {
                const parsedMessage = JSON.parse(message);
                console.log('All Messages', parsedMessage)
                console.log(id)
                
                // console.log('this is our socket map', userSocketMap)

                // Send the message only to the specific user's socket
                let userSocket = userSocketMap.get(id);
                if (userSocket && userSocket.readyState  === WebSocket.OPEN) {
                    console.log('sending message')
                    userSocket.send(JSON.stringify({'cool': `woot: ${id}`}));
                }
                if(parsedMessage.type === 'Admin'){
                    if(parsedMessage.data.adminAuth == superAdmin){
                        console.log('we got a new admin')
                        console.log("parsedMessage.data.id is:" , parsedMessage.data.id)
                        if(parsedMessage.data.id != null){
                            console.log('we got a new admin with id')
                            AllowedSockets[parsedMessage.data.id] = {email: parsedMessage.data.emailAddress, clerkID: parsedMessage.data.clerkID}
                        }
                    }
                }

                if (parsedMessage.type === 'id') {
                    console.log('we got a new id')
                    userSocketTracker[parsedMessage.data] = ws;
                    wss.clients.forEach((client) => {
                        if (userSocket && userSocket.readyState  === WebSocket.OPEN) {
                            console.log('sending message')
                            userSocket.send(JSON.stringify({'Data': `we recieved your id: ${parsedMessage.data} `}));
                        }
                    });
                  }

                if (parsedMessage.type === 'jobmessage' || parsedMessage.type === 'ping') {
                    let output = message;
                    console.log('we got a ping')
                    wss.clients.forEach((client) => {
                      if (client.readyState === WebSocket.OPEN) {
                        client.send(JSON.stringify({ message: parsedMessage }));
                        console.log('sent to client', JSON.stringify({ message: parsedMessage }))
                      }
                    });
                  }

            } catch (error) {
                console.error('Error parsing message:', error);
            }
        });

        ws.on('close', () => {
            // Remove the closed socket from the user-specific map
            console.log('Client disconnected');
            userSocketMap.forEach((socket, key) => {
                if (socket === ws) {
                    userSocketMap.delete(key);
                }
            });
        });
    });


    app.get('/userMap', (req, res) => {
        res.json(AllowedSockets)
    }
    )
    app.get('/manipulation', (req, res) => {
        const { manipulate } = require('./utils');
        manipulate();
    }
    )

    app.get('/manipulation2', (req, res) => {
        const { manipulate2 } = require('./utils');
        manipulate2();
    }
    )



    app.get('/updateAll', async (req, res) => {

        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }
        await getHistoricalData('solana');
        await sleep(5000);
        await getHistoricalData('bitcoin');
        await sleep(5000);
        await getHistoricalData('ethereum');
        await sleep(5000);
        await getHistoricalData('cardano');
        await sleep(5000);
        await getHistoricalData('polkadot');
        await sleep(5000);
        
        dataManipulation('bitcoin', 1);
        dataManipulation('ethereum', 1);
        dataManipulation('cardano', 1);
        dataManipulation('polkadot', 1);
        dataManipulation('solana', 1);
        dataManipulation('bitcoin', 10);
        dataManipulation('ethereum', 10);
        dataManipulation('cardano', 10);
        dataManipulation('polkadot', 10);
        dataManipulation('solana', 10);

        
        res.send('updated');
        restartServer();

    })

    app.get('/chartData/:crypto/', async (req, res) => {
        console.log('route hit')
        const crypto = req.params.crypto
        let PriceData = require(`./RawPriceData/${crypto}PriceData.js`)
        res.json(PriceData);
    })

    app.get('/trainAll', (req, res) => {
        trainCrypto('bitcoin', 1);
        trainCrypto('ethereum', 1);
        console.log('training cardano 1')
        trainCrypto('cardano', 1);
        trainCrypto('polkadot', 1);
        console.log('training solana 1')
        trainCrypto('solana', 1);
        console.log('training bitcoin 10')
        trainCrypto('bitcoin', 10);
        console.log('training ethereum 10')
        trainCrypto('ethereum', 10);
        console.log('training cardano 10')
        trainCrypto('cardano', 10);
        trainCrypto('polkadot', 10);
        console.log('training solana 10')
        trainCrypto('solana', 10);
        restartServer();
    })


    app.get('/:query/:crypto/:predictionDays', async (req, res) => {
        const predictionDays = parseInt(req.params.predictionDays)
        const crypto = req.params.crypto
        const query = req.params.query
        if (query == 'train') {
            console.log('training')
            await trainCrypto(crypto, predictionDays);
        }
        if (query == 'new') {
            await getHistoricalData(crypto);
            dataManipulation(crypto, predictionDays);
        }
        if (query == 'current' || query == 'new') {
            const retrieveModel = require(`./CryptoModels/trained${crypto}_${predictionDays}DayPrediction.json`)
            const brainReconstruction = new brain.NeuralNetworkGPU();
            brainReconstruction.fromJSON(retrieveModel);
            const cryptoFunctions = new Utils(crypto)
            let trainingdata = require(`./TrainingData/${crypto}TrainingData_${predictionDays}DayPrediction.js`);
            console.log(brainReconstruction.run(trainingdata[trainingdata.length - 1].input))
            let cryptoPrediction = cryptoFunctions.denormalize((brainReconstruction.run(trainingdata[trainingdata.length - 1].input)).Result);
            // let cryptoPrediction = cryptoFunctions.denormalize(brainReconstruction.run(trainingdata[trainingdata.length - 1].input));

            // Your JSON data
            const jsonData = {
                crypto: crypto,
                predictionDays: predictionDays,
                cryptoprediction: cryptoPrediction,
                recentprice: cryptoFunctions.recentPrice,
                recentDate: cryptoFunctions.recentDate,
                dateUnEdited: cryptoFunctions.dateUnEdited
            };
            // Sending JSON response
            res.json(jsonData);
        }
    });



    app.get('/', (req, res) => {
        res.send(
            "<h1>Server is running!</h1>" +
            "<p>lets get it started in here!</p>" +
            "<h1>Endpoints</h1>" +
            "<p>/chartData/:coin/</p>" +
            "<p>/trainAll</p>" +
            "<p>/updateAll</p>" +
            "<p>/:query/:crypto/:predictionDays<p>" +
            "<h2>Query Options</h2>" +
            "<p>train</p>" +
            "<p>new</p>" +
            "<p>current</p>" +
            "<h2>Crypto Options</h2>" +
            "<p>bitcoin</p>" +
            "<p>ethereum</p>" +
            "<p>cardano</p>" +
            "<p>polkadot</p>" +
            "<p>solana</p>" +
            "<h2>Prediction Days Options</h2>" +
            "<p>1</p>" +
            "<p>10</p>" +
            "<h2>Example</h2>" +
            "<p>/train/bitcoin/1</p>" +
            "<p>/new/ethereum/10</p>" +
            "<p>/current/cardano/1</p>"


        );
    });

    // Start the Express server
    // app.listen(port, () => {
    //   console.log(`Server is listening at http://localhost:${port}`);
    // });

    server.listen(port, () => {
        console.log(`Server is listening at http://localhost:${port}`);
    });

}

// Initial server startup
startServer();

function restartServer() {
    console.log('Restarting server...');

    // Close existing connections gracefully
    server.close(() => {
        // Start the server again after a short delay
        setTimeout(() => {
            startServer();
        }, 1000);
    });
}
// Handle cleanup when the server is stopped
process.on('SIGINT', () => {
    console.log('Server shutting down');
    process.exit();
});
