generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id          Int           @id @default(autoincrement())
  userName    String?       @unique
  password    String
  // video Videos[]
    videos Videos[]

}

model Videos {
  id          Int        @id @default(autoincrement())
  link        String     
  authorId    Int        
  porn        Boolean    @default(false)
  bj          <PERSON><PERSON><PERSON>    @default(false)
  new         Boolean    @default(false)
  busty       <PERSON>    @default(false)
  blond       <PERSON><PERSON>    @default(false)
  doggy       <PERSON>    @default(false)
  best        Boolean    @default(false)
  milf        <PERSON>    @default(false)
  teaching    Boolean    @default(false)
  lingerie    <PERSON>an    @default(false)
  black       <PERSON>olean    @default(false)
  asian       <PERSON>    @default(false)
  white       <PERSON>olean    @default(false)
  mexican     Boolean    @default(false)
  youtube     Boolean    @default(false)
  programming <PERSON><PERSON>an    @default(false)
  edyta       <PERSON>    @default(false)
  girlfirend        Boolean    @default(false)
  favorite    Boolean   @default(false)
  mission<PERSON><PERSON>   @default(false)
  threesome <PERSON>olean @default(false)
  archive <PERSON><PERSON><PERSON>  @default(false)
  shoes <PERSON><PERSON><PERSON> @default(false)
  mistreat <PERSON><PERSON><PERSON> @default(false)
  piss <PERSON><PERSON><PERSON> @default(false)
  // author User[]
  author      User @relation(fields: [authorId], references: [id])

  @@unique([link, authorId])  // Add this line to create a unique constraint
}
