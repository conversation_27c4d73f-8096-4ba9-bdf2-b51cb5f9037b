// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema


datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  directUrl         = env("DIRECT_URL")
  // provider = "sqlite"
  // url      = "file:./dev.db"
  
}

generator client {
  provider = "prisma-client-js"
  
}


model TrainingData{
  id           Int      @id @default(autoincrement())
  crypto       String @unique
  fileContent10  Json?
  fileContent1  Json?
  // fileContent10  String?
  // fileContent1  String?
}

model PriceData {
  id           Int      @id @default(autoincrement())
  crypto       String @unique
  fileContent  Json
  // fileContent String
}

model TrainedModel{
  id           Int      @id @default(autoincrement())
  crypto       String @unique
  fileContent10  Json?
  fileContent1  Json?
  // fileContent10  String?
  // fileContent1  String?
}