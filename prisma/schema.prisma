// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model CryptoPredictionData {
    id Int @id @default(autoincrement())
    uniqueId String @unique
    crypto String
    predictionDays Int
    cryptoprediction Float 
    recentprice Float
    recentDate String
    dateUnEdited BigInt
}

// model EthPredictionData {
//     id Int @id @default(autoincrement())
//     ethprediction Float
//     recentprice Float
//     recentDate String
//     dateUnEdited BigInt @unique
// }

model User {
  id Int @id @default(autoincrement())
  email String @unique
  password String
  name String? //optional
}

model PhoneEmailUnsubscribe {
  id Int @id @default(autoincrement())
  email String @unique
  name String? //optional
  reason String? //optional
}

model RecievedEmails {
  id Int @id @default(autoincrement())
  email String?
  name String? //optional
  message String? //optional
}