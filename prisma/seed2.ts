
import { PrismaClient } from '@prisma/client'
import { hash } from 'bcrypt'

const prisma = new PrismaClient()

const loader = ['']

console.log('hit')
//*** This is how to get the id of a user*/
// const user = await prisma.user.findUnique({
//     where: {
//       userName: userId
//     }
//   });
  
//   if (user) {
//     const userId = user.id;
//     console.log(`The ID of the user ${userId} is: ${userId}`);
//   } else {
//     console.log(`User with username ${userId} not found`);
//   }


//*** This is how to get videos of a particular fancy*/

// const userId = 'apophis51';

// const user = await prisma.user.findUnique({
//   where: {
//     userName: userId
//   }
// });

// const videos = user.video;

// console.log(videos);

// const videos = await prisma.videos.findMany({
//     where: {
//       authorId: userId,
//       blond: true,
//       new: true
//     }
//   });

//*** This is how to delete a video*/
// const deletedVideo = await prisma.videos.delete({
//     where: {
//       link: linkToDelete
//     }
//   });

async function main() {
    // let manta = await prisma.videos.upsert({
    //     // where: { link: "maverick.com"},
    //     where: { id: 1},

    //     update: {
    //       asian: true,
    //       author: {
    //         connect: {
    //           userName: "apophis50"
    //         }
    //       }
    //     },
    //     create: {
    //       link: "maverick.com",
    //       asian: true,
    //       author: {
    //         connect: {
    //           userName: 'apophis5'
    //         }
    //       }
    //     }
    //   });


    // const videoLink = await prisma.user.findUnique({
    //     where: {
    //         userName: 'apophis51',
    //     },
    //     include: {
    //         videos: {
    //             where: {
    //                 link: 'maverickk.com',
    //             },
    //             select: {
    //                 id: true,
    //             },
    //         },
    //     },
    // });
    // const videoId = videoLink?.videos[0]?.id;


    const userr = await prisma.user.findUnique({
        where: {
            userName: 'apophis50'
        }
    });

    if (true) {
        const video = await prisma.videos.upsert({
            where: {
                link_authorId: {
                    link: 'maverick.com',
                    authorId: userr!.id,
                  },
            },
            update: {
                link: 'maverick.com',
                best: true
            },
            create: {
                link: 'maverick.com',
                author: {
                             connect: {
                           id: userr!.id
                             }
                           }
            }
        });
    }
    
    
    const password = await hash('test', 10)
    const user = await prisma.user.upsert({
        where: { userName: 'apophis50'},
        update: {},
        create: {
            userName: 'apophis50',
            password: password,
            videos: {
                create: {
                    link: 'good ass porn . com'
                }
            }
        },
    })
}

main()
    .then(() => prisma.$disconnect())
    .catch(async (e) => {
        console.error(e)
        await prisma.$disconnect()
        process.exit(1)
    })