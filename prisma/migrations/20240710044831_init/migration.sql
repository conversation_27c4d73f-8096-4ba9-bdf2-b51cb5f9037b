/*
  Warnings:

  - You are about to drop the column `authorId` on the `Videos` table. All the data in the column will be lost.

*/
-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_Videos" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "link" TEXT NOT NULL,
    "porn" BOOLEAN NOT NULL DEFAULT false,
    "bj" BOOLEAN NOT NULL DEFAULT false,
    "new" BOOLEAN NOT NULL DEFAULT false,
    "busty" BOOLEAN NOT NULL DEFAULT false,
    "blond" BOOLEAN NOT NULL DEFAULT false,
    "doggy" BOOLEAN NOT NULL DEFAULT false,
    "best" BOOLEAN NOT NULL DEFAULT false,
    "milf" BOOLEAN NOT NULL DEFAULT false,
    "teaching" BOOLEAN NOT NULL DEFAULT false,
    "lingerie" BOOLEAN NOT NULL DEFAULT false,
    "black" BOOLEAN NOT NULL DEFAULT false,
    "asian" BOOLEAN NOT NULL DEFAULT false,
    "white" BOOLEAN NOT NULL DEFAULT false,
    "mexican" BOOLEAN NOT NULL DEFAULT false,
    "youtube" BOOLEAN NOT NULL DEFAULT false,
    "programming" BOOLEAN NOT NULL DEFAULT false
);
INSERT INTO "new_Videos" ("asian", "best", "bj", "black", "blond", "busty", "doggy", "id", "lingerie", "link", "mexican", "milf", "new", "porn", "programming", "teaching", "white", "youtube") SELECT "asian", "best", "bj", "black", "blond", "busty", "doggy", "id", "lingerie", "link", "mexican", "milf", "new", "porn", "programming", "teaching", "white", "youtube" FROM "Videos";
DROP TABLE "Videos";
ALTER TABLE "new_Videos" RENAME TO "Videos";
CREATE UNIQUE INDEX "Videos_link_key" ON "Videos"("link");
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;
