-- CreateTable
CREATE TABLE "oneDayData" (
    "id" SERIAL NOT NULL,
    "Crypto" TEXT NOT NULL,
    "fileContent" BYTEA NOT NULL,

    CONSTRAINT "oneDayData_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TenDayData" (
    "id" SERIAL NOT NULL,
    "Crypto" TEXT NOT NULL,
    "fileContent" BYTEA NOT NULL,

    CONSTRAINT "TenDayData_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PriceData" (
    "id" SERIAL NOT NULL,
    "Crypto" TEXT NOT NULL,
    "fileContent" BYTEA NOT NULL,

    CONSTRAINT "PriceData_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "TrainedModel" (
    "id" SERIAL NOT NULL,
    "Crypto" TEXT NOT NULL,
    "fileContent" BYTEA NOT NULL,

    CONSTRAINT "TrainedModel_pkey" PRIMARY KEY ("id")
);
