/*
  Warnings:

  - A unique constraint covering the columns `[crypto]` on the table `PriceData` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[crypto]` on the table `TenDayData` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[crypto]` on the table `TrainedModel` will be added. If there are existing duplicate values, this will fail.

*/
-- CreateIndex
CREATE UNIQUE INDEX "PriceData_crypto_key" ON "PriceData"("crypto");

-- CreateIndex
CREATE UNIQUE INDEX "TenDayData_crypto_key" ON "TenDayData"("crypto");

-- CreateIndex
CREATE UNIQUE INDEX "TrainedModel_crypto_key" ON "TrainedModel"("crypto");
