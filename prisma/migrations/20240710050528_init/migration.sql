/*
  Warnings:

  - You are about to drop the `_UserToVideos` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `authorId` to the `Videos` table without a default value. This is not possible if the table is not empty.

*/
-- DropIndex
DROP INDEX "_UserToVideos_B_index";

-- DropIndex
DROP INDEX "_UserToVideos_AB_unique";

-- DropTable
PRAGMA foreign_keys=off;
DROP TABLE "_UserToVideos";
PRAGMA foreign_keys=on;

-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_Videos" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "link" TEXT NOT NULL,
    "authorId" INTEGER NOT NULL,
    "porn" BOOLEAN NOT NULL DEFAULT false,
    "bj" BOOLEAN NOT NULL DEFAULT false,
    "new" BOOLEAN NOT NULL DEFAULT false,
    "busty" B<PERSON><PERSON>EAN NOT NULL DEFAULT false,
    "blond" BOOLEAN NOT NULL DEFAULT false,
    "doggy" BOOLEAN NOT NULL DEFAULT false,
    "best" BOOLEAN NOT NULL DEFAULT false,
    "milf" BOOLEAN NOT NULL DEFAULT false,
    "teaching" BOOLEAN NOT NULL DEFAULT false,
    "lingerie" BOOLEAN NOT NULL DEFAULT false,
    "black" BOOLEAN NOT NULL DEFAULT false,
    "asian" BOOLEAN NOT NULL DEFAULT false,
    "white" BOOLEAN NOT NULL DEFAULT false,
    "mexican" BOOLEAN NOT NULL DEFAULT false,
    "youtube" BOOLEAN NOT NULL DEFAULT false,
    "programming" BOOLEAN NOT NULL DEFAULT false,
    CONSTRAINT "Videos_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES "User" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);
INSERT INTO "new_Videos" ("asian", "best", "bj", "black", "blond", "busty", "doggy", "id", "lingerie", "link", "mexican", "milf", "new", "porn", "programming", "teaching", "white", "youtube") SELECT "asian", "best", "bj", "black", "blond", "busty", "doggy", "id", "lingerie", "link", "mexican", "milf", "new", "porn", "programming", "teaching", "white", "youtube" FROM "Videos";
DROP TABLE "Videos";
ALTER TABLE "new_Videos" RENAME TO "Videos";
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;
