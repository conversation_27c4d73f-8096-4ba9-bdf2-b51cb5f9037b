-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_Videos" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "link" TEXT NOT NULL,
    "authorId" INTEGER NOT NULL,
    "porn" BOOLEAN NOT NULL DEFAULT false,
    "bj" BOOLEAN NOT NULL DEFAULT false,
    "new" BOOLEAN NOT NULL DEFAULT false,
    "busty" BOOLEAN NOT NULL DEFAULT false,
    "blond" BOOLEAN NOT NULL DEFAULT false,
    "doggy" BOOLEAN NOT NULL DEFAULT false,
    "best" BOOLEAN NOT NULL DEFAULT false,
    "milf" BOOLEAN NOT NULL DEFAULT false,
    "teaching" BOOLEAN NOT NULL DEFAULT false,
    "lingerie" BOOLEAN NOT NULL DEFAULT false,
    "black" BOOLEAN NOT NULL DEFAULT false,
    "asian" BOOLEAN NOT NULL DEFAULT false,
    "white" BOOLEAN NOT NULL DEFAULT false,
    "mexican" BO<PERSON>EAN NOT NULL DEFAULT false,
    "youtube" BO<PERSON>EAN NOT NULL DEFAULT false,
    "programming" BOOLEAN NOT NULL DEFAULT false,
    "edyta" BOOLEAN NOT NULL DEFAULT false,
    "girlfirend" BOOLEAN NOT NULL DEFAULT false,
    "favorite" BOOLEAN NOT NULL DEFAULT false,
    "missionairy" BOOLEAN NOT NULL DEFAULT false,
    "threesome" BOOLEAN NOT NULL DEFAULT false,
    CONSTRAINT "Videos_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES "User" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);
INSERT INTO "new_Videos" ("asian", "authorId", "best", "bj", "black", "blond", "busty", "doggy", "edyta", "favorite", "girlfirend", "id", "lingerie", "link", "mexican", "milf", "new", "porn", "programming", "teaching", "white", "youtube") SELECT "asian", "authorId", "best", "bj", "black", "blond", "busty", "doggy", "edyta", "favorite", "girlfirend", "id", "lingerie", "link", "mexican", "milf", "new", "porn", "programming", "teaching", "white", "youtube" FROM "Videos";
DROP TABLE "Videos";
ALTER TABLE "new_Videos" RENAME TO "Videos";
CREATE UNIQUE INDEX "Videos_link_authorId_key" ON "Videos"("link", "authorId");
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;
