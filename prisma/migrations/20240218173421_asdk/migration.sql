/*
  Warnings:

  - Changed the type of `fileContent` on the `TrainedModel` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `fileContent10` on the `TrainingData` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.
  - Changed the type of `fileContent1` on the `TrainingData` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.

*/
-- AlterTable
ALTER TABLE "TrainedModel" DROP COLUMN "fileContent",
ADD COLUMN     "fileContent" JSONB NOT NULL;

-- AlterTable
ALTER TABLE "TrainingData" DROP COLUMN "fileContent10",
ADD COLUMN     "fileContent10" JSONB NOT NULL,
DROP COLUMN "fileContent1",
ADD COLUMN     "fileContent1" JSONB NOT NULL;
