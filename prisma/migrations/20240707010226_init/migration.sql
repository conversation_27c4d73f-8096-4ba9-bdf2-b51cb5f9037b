/*
  Warnings:

  - You are about to drop the `UserContent` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `UserSignUp` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropTable
PRAGMA foreign_keys=off;
DROP TABLE "UserContent";
PRAGMA foreign_keys=on;

-- DropTable
PRAGMA foreign_keys=off;
DROP TABLE "UserSignUp";
PRAGMA foreign_keys=on;

-- CreateTable
CREATE TABLE "User" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "userName" TEXT,
    "password" TEXT NOT NULL
);

-- CreateTable
CREATE TABLE "Videos" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "link" TEXT NOT NULL,
    "authorId" INTEGER NOT NULL,
    "porn" BOOLEAN NOT NULL DEFAULT false,
    "bj" BOOLEAN NOT NULL DEFAULT false,
    "new" B<PERSON>OLEAN NOT NULL DEFAULT false,
    "busty" BOOLEAN NOT NULL DEFAULT false,
    "blond" BOOLEAN NOT NULL DEFAULT false,
    "doggy" BOOLEAN NOT NULL DEFAULT false,
    "best" BOOLEAN NOT NULL DEFAULT false,
    "milf" BOOLEAN NOT NULL DEFAULT false,
    "teaching" BOOLEAN NOT NULL DEFAULT false,
    "lingerie" BOOLEAN NOT NULL DEFAULT false,
    "black" BOOLEAN NOT NULL DEFAULT false,
    "asian" BOOLEAN NOT NULL DEFAULT false,
    "white" BOOLEAN NOT NULL DEFAULT false,
    "mexican" BOOLEAN NOT NULL DEFAULT false,
    "youtube" BOOLEAN NOT NULL DEFAULT false,
    "programming" BOOLEAN NOT NULL DEFAULT false,
    CONSTRAINT "Videos_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES "User" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateIndex
CREATE UNIQUE INDEX "User_userName_key" ON "User"("userName");
