-- CreateTable
CREATE TABLE "_UserToVideos" (
    "A" INTEGER NOT NULL,
    "B" INTEGER NOT NULL,
    CONSTRAINT "_UserToVideos_A_fkey" FOREIGN KEY ("A") REFERENCES "User" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "_UserToVideos_B_fkey" FOREIGN KEY ("B") REFERENCES "Videos" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);

-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_Videos" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "link" TEXT NOT NULL,
    "authorId" INTEGER NOT NULL,
    "porn" BOOLEAN NOT NULL DEFAULT false,
    "bj" BOOLEAN NOT NULL DEFAULT false,
    "new" BOOLEAN NOT NULL DEFAULT false,
    "busty" BOOLEAN NOT NULL DEFAULT false,
    "blond" BOOLEAN NOT NULL DEFAULT false,
    "doggy" BOOLEAN NOT NULL DEFAULT false,
    "best" BOOLEAN NOT NULL DEFAULT false,
    "milf" BOOLEAN NOT NULL DEFAULT false,
    "teaching" BOOLEAN NOT NULL DEFAULT false,
    "lingerie" BOOLEAN NOT NULL DEFAULT false,
    "black" BOOLEAN NOT NULL DEFAULT false,
    "asian" BOOLEAN NOT NULL DEFAULT false,
    "white" BOOLEAN NOT NULL DEFAULT false,
    "mexican" BOOLEAN NOT NULL DEFAULT false,
    "youtube" BOOLEAN NOT NULL DEFAULT false,
    "programming" BOOLEAN NOT NULL DEFAULT false
);
INSERT INTO "new_Videos" ("asian", "authorId", "best", "bj", "black", "blond", "busty", "doggy", "id", "lingerie", "link", "mexican", "milf", "new", "porn", "programming", "teaching", "white", "youtube") SELECT "asian", "authorId", "best", "bj", "black", "blond", "busty", "doggy", "id", "lingerie", "link", "mexican", "milf", "new", "porn", "programming", "teaching", "white", "youtube" FROM "Videos";
DROP TABLE "Videos";
ALTER TABLE "new_Videos" RENAME TO "Videos";
CREATE UNIQUE INDEX "Videos_link_key" ON "Videos"("link");
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;

-- CreateIndex
CREATE UNIQUE INDEX "_UserToVideos_AB_unique" ON "_UserToVideos"("A", "B");

-- CreateIndex
CREATE INDEX "_UserToVideos_B_index" ON "_UserToVideos"("B");
