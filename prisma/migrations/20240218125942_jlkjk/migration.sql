/*
  Warnings:

  - You are about to drop the column `Crypto` on the `PriceData` table. All the data in the column will be lost.
  - You are about to drop the column `Crypto` on the `TenDayData` table. All the data in the column will be lost.
  - You are about to drop the column `Crypto` on the `TrainedModel` table. All the data in the column will be lost.
  - You are about to drop the column `Crypto` on the `oneDayData` table. All the data in the column will be lost.
  - Added the required column `crypto` to the `PriceData` table without a default value. This is not possible if the table is not empty.
  - Added the required column `crypto` to the `TenDayData` table without a default value. This is not possible if the table is not empty.
  - Added the required column `crypto` to the `TrainedModel` table without a default value. This is not possible if the table is not empty.
  - Added the required column `crypto` to the `oneDayData` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "PriceData" DROP COLUMN "Crypto",
ADD COLUMN     "crypto" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "TenDayData" DROP COLUMN "Crypto",
ADD COLUMN     "crypto" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "TrainedModel" DROP COLUMN "Crypto",
ADD COLUMN     "crypto" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "oneDayData" DROP COLUMN "Crypto",
ADD COLUMN     "crypto" TEXT NOT NULL;
