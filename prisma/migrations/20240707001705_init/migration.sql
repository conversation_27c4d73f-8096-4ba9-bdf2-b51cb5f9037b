-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_UserContent" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "link" TEXT NOT NULL,
    "authorId" INTEGER NOT NULL,
    "porn" BOOLEAN NOT NULL DEFAULT false,
    "bj" BOOLEAN NOT NULL DEFAULT false,
    "new" BOOLEAN NOT NULL DEFAULT false,
    "busty" BOOLEAN NOT NULL DEFAULT false,
    "blond" BOOLEAN NOT NULL DEFAULT false,
    "doggy" BOOLEAN NOT NULL DEFAULT false,
    "best" BOOLEAN NOT NULL DEFAULT false,
    "milf" BOOLEAN NOT NULL DEFAULT false,
    "teaching" BOOLEAN NOT NULL DEFAULT false,
    "lingerie" BOOLEAN NOT NULL DEFAULT false,
    "black" BOOLEAN NOT NULL DEFAULT false,
    "asian" BOOLEAN NOT NULL DEFAULT false,
    "white" BOOLEAN NOT NULL DEFAULT false,
    "mexican" BOOLEAN NOT NULL DEFAULT false,
    "youtube" BOOLEAN NOT NULL DEFAULT false,
    "programming" BOOLEAN NOT NULL DEFAULT false,
    CONSTRAINT "UserContent_authorId_fkey" FOREIGN KEY ("authorId") REFERENCES "UserSignUp" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);
INSERT INTO "new_UserContent" ("asian", "authorId", "best", "bj", "black", "blond", "busty", "doggy", "id", "lingerie", "link", "mexican", "milf", "new", "teaching", "white") SELECT "asian", "authorId", "best", "bj", "black", "blond", "busty", "doggy", "id", "lingerie", "link", "mexican", "milf", "new", "teaching", "white" FROM "UserContent";
DROP TABLE "UserContent";
ALTER TABLE "new_UserContent" RENAME TO "UserContent";
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;
