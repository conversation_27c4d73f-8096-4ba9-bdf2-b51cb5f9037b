import { PrismaClient } from '@prisma/client'
import { hash } from 'bcrypt'

const prisma = new PrismaClient()

const loader = ['']


async function main() {
    const password = await hash('test', 12)
    const user = await prisma.user.upsert({
        where: { email: '<EMAIL>'},
        update: {},
        create: {
            email: '<EMAIL>',
            name: 'Test User',
            password: password
        },
    })


    await prisma.user.upsert({
        where: { email: '<EMAIL>'},
        update: {},
        create: {
            email: '<EMAIL>',
            name: 'adminn',
            password: await hash('admin', 12)
        },
    })
    // console.log({user})


    await prisma.phoneEmailUnsubscribe.upsert({
        where: { email: '<EMAIL>'},
        update: {},
        create: {
            email: '<EMAIL>',
            name: 'Test User',
        },
    })

    await prisma.phoneEmailUnsubscribe.upsert({
        where: { email: '<EMAIL>'},
        update: {},
        create: {
            email: '<EMAIL>',
            name: 'schawn schwier',
        },
    })
   for (let x of loader ){
    console.log("adding " + x)
    await prisma.phoneEmailUnsubscribe.upsert({
        where: { email: x},
        update: {},
        create: {
            email: x,
            name: 'noname',
        },
    })
    }
}

main()
    .then(() => prisma.$disconnect())
    .catch(async (e) => {
        console.error(e)
        await prisma.$disconnect()
        process.exit(1)
    })