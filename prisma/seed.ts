import { PrismaClient } from '@prisma/client'
import { hash } from 'bcrypt'

const prisma = new PrismaClient()

const loader = ['']


async function main() {
    const password = await hash('test', 10)
    const user = await prisma.user.upsert({
        where: { userName: 'apophis51'},
        update: {},
        create: {
            userName: 'apophis51',
            password: password,
            videos: {
                create: [{
                    link: 'good ass corn . com'
                }]
            }
        },
    })
}

main()
    .then(() => prisma.$disconnect())
    .catch(async (e) => {
        console.error(e)
        await prisma.$disconnect()
        process.exit(1)
    })