// bunx jest __tests__/curryAPI.test.jsx --testEnvironment=node

import 'isomorphic-fetch'

describe('IP Address API Tests', () => {
  test('should get IP address information', async () => {
    const res = await fetch("http://localhost:3000/api2/ip-address", {
      method: "GET",
      headers: { 
        "Content-Type": "application/json",
        // Simulate an IP address since we're in a test environment
        "x-forwarded-for": "***********"
      }
    });

    // Assert successful response
    expect(res.ok).toBe(true);
    expect(res.status).toBe(200);

    const data = await res.json();
    console.log(data)
    expect(data.success).toBe(true);
    expect(data.data).toBeDefined();
    expect(data.data.ip).toBeDefined();
    expect(data.data.last_accessed).toBeDefined();
  });

//   test('should lookup specific IP address', async () => {
//     const testIP = "***********";
    
//     const res = await fetch("http://localhost:3000/api2/ip-address", {
//       method: "POST",
//       headers: { "Content-Type": "application/json" },
//       body: JSON.stringify({
//         ip_address: testIP
//       })
//     });

//     const data = await res.json();
    
//     if (res.status === 404) {
//       expect(data.success).toBe(false);
//       expect(data.error).toBe('IP address not found');
//     } else {
//       expect(res.ok).toBe(true);
//       expect(res.status).toBe(200);
//       expect(data.success).toBe(true);
//       expect(data.data.ip_address).toBe(testIP);
//     }
//   });

//   test('should delete IP record', async () => {
//     const testIP = "***********";
    
//     const res = await fetch("http://localhost:3000/api2/ip-address", {
//       method: "DELETE",
//       headers: { "Content-Type": "application/json" },
//       body: JSON.stringify({
//         ip_address: testIP
//       })
//     });

//     expect(res.ok).toBe(true);
//     expect(res.status).toBe(200);

//     const data = await res.json();
//     expect(data.success).toBe(true);
//     expect(data.message).toBe('IP record deleted successfully');
//   });

//   test('should handle invalid IP lookup', async () => {
//     const res = await fetch("http://localhost:3000/api2/ip-address", {
//       method: "POST",
//       headers: { "Content-Type": "application/json" },
//       body: JSON.stringify({
//         ip_address: "invalid-ip"
//       })
//     });

//     expect(res.status).toBe(404);
    
//     const data = await res.json();
//     expect(data.success).toBe(false);
//     expect(data.error).toBe('IP address not found');
//   });
});
