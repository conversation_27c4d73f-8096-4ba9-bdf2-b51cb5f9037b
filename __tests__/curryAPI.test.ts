// bunx jest __tests__/curryAPI.test.jsx --testEnvironment=node

import 'isomorphic-fetch'; // For Node.js environments
import {upsertUser, lookupUser, deleteUser, lookupEmailOnly, upsertFingerprint, lookupRecentFingerprint} from '@/app/db/dbCurry'
import { getFingerprintHash } from '@/app/services/fingerprint';



describe('User API Integration Test1', () => {
  xtest('should add a user with fetch request', async () => {
    // This will make an actual network request
    const res = await fetch("http://localhost:3000/db/add-user", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({
        email: "<EMAIL>",
        password: "hhserrrword123"
      }),
    });

    // Assertions
    expect(res.ok).toBe(true);
    expect(res.status).toBe(200);

    const data = await res.json();
    expect(data.success).toBe(true);
    console.log(data.success)
    console.log(data)
    expect(data.result).toBeDefined();
  });
});

describe('User API Integration Test', () => {
    xit('should add a user with fetch request', async () => {
      // This will make an actual network request
      const res = await fetch("http://localhost:3000/db/api", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          dbName: "Next_JS_Portfolio",
          collectionName: "Users",
          filter: { email: "<EMAIL>" },
          update: { $set: { password: "hhnewparrssword123" } }
        }),
      });
      // Assertions
      expect(res.ok).toBe(true);
      expect(res.status).toBe(200);

      const data = await res.json();
      expect(data.success).toBe(true);
      console.log(data.success)
      expect(data.result).toBeDefined();
    });
  });

  describe("Save Fingerprint", () => {
    test("should add/overide existing fingerprint with a new timestamp", async () => {
      const userEmail = "d";
      const fingerPrintHash = process.env.HASH_VALUE; 
      console.log(fingerPrintHash) 
      const result = await upsertFingerprint(userEmail, fingerPrintHash);
  
      // console.log("Database result:", result);
    });
  });

  describe("Lookup Recent Fingerprint", () => {
    test("should find accounts with matching fingerprint in last 5 minutes", async () => {
      const userEmail = "<EMAIL>";
      const fingerPrintHash = process.env.HASH_VALUE;
      
      // First add/update a fingerprint
      await upsertFingerprint(userEmail, fingerPrintHash);
      
      // Then look it up
      const result = await lookupRecentFingerprint(fingerPrintHash);
      
      console.log("Lookup result:", result);
      
      // Assertions
      expect(result).toBeDefined();
      if (result) {
        expect(result.email).toBe(userEmail);
        expect(result.fingerprint).toBe(fingerPrintHash);
        expect(result.fingerprintLastUpdated).toBeDefined();
      }
    });
  
    test("should return null for old fingerprints", async () => {
      const oldFingerprint = "old-fingerprint-hash";
      const result = await lookupRecentFingerprint(oldFingerprint);
      
      console.log("Old fingerprint lookup result:", result);
      expect(result).toBeNull();
    });
  });


describe("authenticateUser", () => {
  test("should add a new user if email does not exist", async () => {
    const userEmail = "<EMAIL>";
    const userPassword = "securepassword";

    const result = await upsertUser(userEmail, userPassword);

    console.log("Database result:", result);
  });
});

describe("lookupUser", () => {
  test("should lookup a user by email", async () => {
    const userEmail = "<EMAIL>";
    const userPassword = "securepassword";

    // Define the expected type for the result
    type UserResult = {
      _id: string;
      email: string;
      password: string;
      [key: string]: any; // Allow for other properties
    };

    const result = await lookupUser(userEmail, userPassword) as UserResult;

    console.log("Database result:", result);

    // Assert that the result is defined
    expect(result).toBeDefined();

    // Assert that the _id property exists and is a string
    expect(result._id).toBeDefined();
    expect(typeof result._id).toBe('string');

    // Assert that email and password match what we provided
    expect(result.email).toBe(userEmail);
    expect(result.password).toBe(userPassword);
  });
});

describe("lookUpEmailOnly", () => {
  test("should lookup a user by email", async () => {
    const userEmail = "<EMAIL>";
    const result = await lookupEmailOnly(userEmail);
    console.log("Database result:", result);
    expect(result).not.toBeNull();
  });
});

describe("lookupNonExistentUser", () => {
  test("shuld return null for a non-existent user", async () => {
    const userEmail = "<EMAIL>";
    const userPassword = "securepassword";
    const result = await lookupUser(userEmail, userPassword);
    console.log("Database result:", result);
    expect(result).toBeNull();
  });
});

describe("delete user", () => {
  test("should delete a user if it exist", async () => {
    // const userEmail = ",<EMAIL>";
    // const userPassword = "securepassword";
    const userEmail = process.env.MY_EMAIL
    console.log(userEmail)
    const userPassword = process.env.MY_PASSWORD

    type resultType = { acknowledged: boolean, deletedCount: number }

    const result: resultType = await deleteUser(userEmail as string, userPassword as string) as resultType;

    expect(result.deletedCount).toBe(1);

    console.log("Database result:", result);
  });
});


describe("clear user", () => {
  test("running test from a clean slate", async () => {
    // const userEmail = ",<EMAIL>";
    // const userPassword = "securepassword";
    const userEmail = process.env.MY_EMAIL
    console.log(userEmail)
    const userPassword = process.env.MY_PASSWORD

    const result = await deleteUser(userEmail, userPassword);

    console.log("Database result:", result);
  });
});


