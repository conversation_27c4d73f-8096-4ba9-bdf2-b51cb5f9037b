import '@testing-library/jest-dom'

import { getStableFingerprint, getFingerprintHash, sha256 } from '@/app/services/fingerprint';

/// Mock browser globals
const mockNavigator = {
  userAgent: 'Mozilla/5.0 (Test)',
  platform: 'Test',
  language: 'en-US',
  hardwareConcurrency: 8,
  deviceMemory: 8,
  maxTouchPoints: 0,
  plugins: {
    length: 2
  }
};

const mockScreen = {
  width: 1920,
  height: 1080,
  colorDepth: 24
};

const mockWindow = {
  innerWidth: 1600,
  innerHeight: 900
};

// Mock crypto for sha256
const mockCrypto = {
  subtle: {
    digest: jest.fn().mockImplementation(async (algorithm, buffer) => {
      // Return a consistent mock hash for testing
      return new Uint8Array([1, 2, 3, 4, 5]).buffer;
    })
  }
};

describe('Fingerprint Service', () => {
  beforeAll(() => {
    // Setup global mocks
    // global.navigator = mockNavigator as any;
    // global.screen = mockScreen as any;
    // global.window = mockWindow as any;
    // global.crypto = mockCrypto as any;
  });

  test('getStableFingerprint should return consistent fingerprint object', async () => {
    const fingerprint = await getStableFingerprint();
    console.log(fingerprint)
    
    // expect(fingerprint).toEqual({
    //   userAgent: 'Mozilla/5.0 (Test)',
    //   platform: 'Test',
    //   language: 'en-US',
    //   hardwareConcurrency: 8,
    //   deviceMemory: 8,
    //   maxTouchPoints: 0,
    //   screenResolution: '1920x1080',
    //   colorDepth: 24,
    //   windowSize: '1600x900',
    //   timezoneOffset: expect.any(Number),
    //   pluginsCount: 2
    // });
  });

  test('getFingerprintHash should return consistent hash', async () => {
    const hash = await getFingerprintHash();
    // Change the log format to make it easier to parse
    console.log(`HASH_VALUE=${hash}`);  // Format: HASH_VALUE=12345
    
    // Test hash consistency
    const secondHash = await getFingerprintHash();
    expect(hash).toBe(secondHash);
  });

  test('getFingerprintHash should return different hashes for different fingerprints', async () => {
    const firstHash = await getFingerprintHash();
    console.log('first',firstHash)
    
    // Modify a property to simulate different device
    global.navigator = {
      ...mockNavigator,
      platform: 'Different Platform'
    } as any;

    let mockOne = {
      userAgent: 'Mozilla/5.0 (linux) AppleWebKit/537.36 (KHTML, like Gecko) jsdom/20.0.3',
      platform: '',
      language: 'en-US',
      hardwareConcurrency: 12,
      deviceMemory: undefined,
      maxTouchPoints: undefined,
      screenResolution: '0x0',
      colorDepth: 25,
      windowSize: '1024x768',
      timezoneOffset: 240,
      pluginsCount: 0
    }

    const secondHash = await sha256(JSON.stringify(mockOne));
    console.log('second',secondHash)
    expect(firstHash).not.toBe(secondHash);
  });

  // test('getStableFingerprint should handle missing browser APIs gracefully', async () => {
  //   // Simulate missing APIs
  //   const originalNavigator = global.navigator;
  //   global.navigator = {
  //     userAgent: 'Mozilla/5.0 (Test)',
  //     platform: 'Test',
  //     language: 'en-US'
  //   } as any;

  //   const fingerprint = await getStableFingerprint();

  //   expect(fingerprint.hardwareConcurrency).toBeUndefined();
  //   expect(fingerprint.deviceMemory).toBeUndefined();
  //   expect(fingerprint.pluginsCount).toBe(0);

  //   // Restore original navigator
  //   global.navigator = originalNavigator;
  // });
});
