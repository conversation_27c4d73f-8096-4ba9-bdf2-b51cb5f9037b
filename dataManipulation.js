
function dataManipulation(crypto, predictionDays=1){

predictionDays = predictionDays + 50
const {Utils} = require('./utils');
const data = require(`./RawPriceData/${crypto}PriceData`).prices
const volume = require(`./RawPriceData/${crypto}PriceData`).total_volumes
const cryptoFunctions = new Utils(crypto)
const fs = require('fs');


  let trainingData = []
  // for (let i = 0; i < data.length - predictionDays; i++) {
  //   trainingData.push({
  //       input: [
  //           cryptoFunctions.normalize(data[i][1]),
  //           cryptoFunctions.normalize(data[i+1][1]),
  //           cryptoFunctions.normalize(data[i+2][1]),
  //           cryptoFunctions.normalize(data[i+3][1]),
  //           cryptoFunctions.normalize(data[i+4][1]),
  //           cryptoFunctions.normalize(data[i+5][1]),
  //           cryptoFunctions.normalize(data[i+6][1]),
  //           cryptoFunctions.normalize(data[i+7][1]),
  //           cryptoFunctions.normalize(data[i+8][1]),
  //           cryptoFunctions.normalize(data[i+9][1]),
  //           cryptoFunctions.normalize(data[i+10][1]),
  //           cryptoFunctions.normalize(data[i+11][1]),
  //           cryptoFunctions.normalize(data[i+12][1]),
  //           cryptoFunctions.normalize(data[i+13][1]),
  //           cryptoFunctions.normalize(data[i+14][1]),
  //           cryptoFunctions.normalize(data[i+15][1]),
  //           cryptoFunctions.normalize(data[i+16][1]),
  //           cryptoFunctions.normalize(data[i+17][1]),
  //           cryptoFunctions.normalize(data[i+18][1]),
  //           cryptoFunctions.normalize(data[i+19][1]),
  //           cryptoFunctions.normalize(data[i+20][1]),
  //           cryptoFunctions.normalize(data[i+21][1]),
  //           cryptoFunctions.normalize(data[i+22][1]),
  //           cryptoFunctions.normalize(data[i+23][1]),
  //           cryptoFunctions.normalize(data[i+24][1]),
  //           cryptoFunctions.normalize(data[i+25][1]),
  //           cryptoFunctions.normalize(data[i+26][1]),
  //           cryptoFunctions.normalize(data[i+27][1]),
  //           cryptoFunctions.normalize(data[i+28][1]),
  //           cryptoFunctions.normalize(data[i+29][1]),
  //           cryptoFunctions.normalize(data[i+30][1]),
  //           cryptoFunctions.normalize(data[i+31][1]),
  //           cryptoFunctions.normalize(data[i+32][1]),
  //           cryptoFunctions.normalize(data[i+33][1]),
  //           cryptoFunctions.normalize(data[i+34][1]),
  //           cryptoFunctions.normalize(data[i+35][1]),
  //           cryptoFunctions.normalize(data[i+36][1]),
  //           cryptoFunctions.normalize(data[i+37][1]),
  //           cryptoFunctions.normalize(data[i+38][1]),
  //           cryptoFunctions.normalize(data[i+39][1]),
  //           cryptoFunctions.normalize(data[i+40][1]),
  //           cryptoFunctions.normalize(data[i+41][1]),
  //           cryptoFunctions.normalize(data[i+42][1]),
  //           cryptoFunctions.normalize(data[i+43][1]),
  //           cryptoFunctions.normalize(data[i+44][1]),
  //           cryptoFunctions.normalize(data[i+45][1]),
  //           cryptoFunctions.normalize(data[i+46][1]),
  //           cryptoFunctions.normalize(data[i+47][1]),
  //           cryptoFunctions.normalize(data[i+48][1]),
  //           cryptoFunctions.normalize(data[i+49][1]),
  //           cryptoFunctions.normalize(data[i+50][1]),
  //       ],  
  //       output: [cryptoFunctions.normalize(data[i+predictionDays][1])]})
  // }
  for (let i = 0; i < data.length - predictionDays; i++) {
    trainingData.push({
        input: {
            volume0: cryptoFunctions.normalizeVolume(volume[i][1]),
            volume1: cryptoFunctions.normalizeVolume(volume[i+1][1]),
            volume2: cryptoFunctions.normalizeVolume(volume[i+2][1]),
            volume3: cryptoFunctions.normalizeVolume(volume[i+3][1]),
            volume4: cryptoFunctions.normalizeVolume(volume[i+4][1]),
            volume5: cryptoFunctions.normalizeVolume(volume[i+5][1]),
            volume6: cryptoFunctions.normalizeVolume(volume[i+6][1]),
            volume7: cryptoFunctions.normalizeVolume(volume[i+7][1]),
            volume8: cryptoFunctions.normalizeVolume(volume[i+8][1]),
            volume9: cryptoFunctions.normalizeVolume(volume[i+9][1]),
            volume10: cryptoFunctions.normalizeVolume(volume[i+10][1]),
            volume11: cryptoFunctions.normalizeVolume(volume[i+11][1]),
            volume12: cryptoFunctions.normalizeVolume(volume[i+12][1]),
            volume13: cryptoFunctions.normalizeVolume(volume[i+13][1]),
            volume14: cryptoFunctions.normalizeVolume(volume[i+14][1]),
            volume15: cryptoFunctions.normalizeVolume(volume[i+15][1]),
            volume16: cryptoFunctions.normalizeVolume(volume[i+16][1]),
            volume17: cryptoFunctions.normalizeVolume(volume[i+17][1]),
            volume18: cryptoFunctions.normalizeVolume(volume[i+18][1]),
            volume19: cryptoFunctions.normalizeVolume(volume[i+19][1]),
            volume20: cryptoFunctions.normalizeVolume(volume[i+20][1]),
            volume21: cryptoFunctions.normalizeVolume(volume[i+21][1]),
            volume22: cryptoFunctions.normalizeVolume(volume[i+22][1]),
            volume23: cryptoFunctions.normalizeVolume(volume[i+23][1]),
            volume24: cryptoFunctions.normalizeVolume(volume[i+24][1]),
            volume25: cryptoFunctions.normalizeVolume(volume[i+25][1]),
            volume26: cryptoFunctions.normalizeVolume(volume[i+26][1]),
            volume27: cryptoFunctions.normalizeVolume(volume[i+27][1]),
            volume28: cryptoFunctions.normalizeVolume(volume[i+28][1]),
            volume29: cryptoFunctions.normalizeVolume(volume[i+29][1]),
            volume30: cryptoFunctions.normalizeVolume(volume[i+30][1]),
            volume31: cryptoFunctions.normalizeVolume(volume[i+31][1]),
            volume32: cryptoFunctions.normalizeVolume(volume[i+32][1]),
            volume33: cryptoFunctions.normalizeVolume(volume[i+33][1]),
            volume34: cryptoFunctions.normalizeVolume(volume[i+34][1]),
            volume35: cryptoFunctions.normalizeVolume(volume[i+35][1]),
            volume36: cryptoFunctions.normalizeVolume(volume[i+36][1]),
            volume37: cryptoFunctions.normalizeVolume(volume[i+37][1]),
            volume38: cryptoFunctions.normalizeVolume(volume[i+38][1]),
            volume39: cryptoFunctions.normalizeVolume(volume[i+39][1]),
            volume40: cryptoFunctions.normalizeVolume(volume[i+40][1]),
            volume41: cryptoFunctions.normalizeVolume(volume[i+41][1]),
            volume42: cryptoFunctions.normalizeVolume(volume[i+42][1]),
            volume43: cryptoFunctions.normalizeVolume(volume[i+43][1]),
            volume44: cryptoFunctions.normalizeVolume(volume[i+44][1]),
            volume45: cryptoFunctions.normalizeVolume(volume[i+45][1]),
            volume46: cryptoFunctions.normalizeVolume(volume[i+46][1]),
            volume47: cryptoFunctions.normalizeVolume(volume[i+47][1]),
            volume48: cryptoFunctions.normalizeVolume(volume[i+48][1]),
            volume49: cryptoFunctions.normalizeVolume(volume[i+49][1]),
            volume50: cryptoFunctions.normalizeVolume(volume[i+50][1]),
            Day0: cryptoFunctions.normalize(data[i][1]),
            Day1: cryptoFunctions.normalize(data[i+1][1]),
            Day2: cryptoFunctions.normalize(data[i+2][1]),
            Day3: cryptoFunctions.normalize(data[i+3][1]),
            Day4: cryptoFunctions.normalize(data[i+4][1]),
            Day5: cryptoFunctions.normalize(data[i+5][1]),
            Day6: cryptoFunctions.normalize(data[i+6][1]),
            Day7: cryptoFunctions.normalize(data[i+7][1]),
            Day8: cryptoFunctions.normalize(data[i+8][1]),
            Day9: cryptoFunctions.normalize(data[i+9][1]),
            Day10: cryptoFunctions.normalize(data[i+10][1]),
            Day11: cryptoFunctions.normalize(data[i+11][1]),
            Day12: cryptoFunctions.normalize(data[i+12][1]),
            Day13: cryptoFunctions.normalize(data[i+13][1]),
            Day14: cryptoFunctions.normalize(data[i+14][1]),
            Day15: cryptoFunctions.normalize(data[i+15][1]),
            Day16: cryptoFunctions.normalize(data[i+16][1]),
            Day17: cryptoFunctions.normalize(data[i+17][1]),  
            Day18: cryptoFunctions.normalize(data[i+18][1]),
            Day19: cryptoFunctions.normalize(data[i+19][1]),
            Day20: cryptoFunctions.normalize(data[i+20][1]),
            Day21: cryptoFunctions.normalize(data[i+21][1]),
            Day22: cryptoFunctions.normalize(data[i+22][1]),
            Day23: cryptoFunctions.normalize(data[i+23][1]),
            Day24: cryptoFunctions.normalize(data[i+24][1]),
            Day25: cryptoFunctions.normalize(data[i+25][1]),
            Day26: cryptoFunctions.normalize(data[i+26][1]),
            Day27: cryptoFunctions.normalize(data[i+27][1]),
            Day28: cryptoFunctions.normalize(data[i+28][1]),
            Day29: cryptoFunctions.normalize(data[i+29][1]),
            Day30: cryptoFunctions.normalize(data[i+30][1]),
            Day31: cryptoFunctions.normalize(data[i+31][1]),
            Day32: cryptoFunctions.normalize(data[i+32][1]),
            Day33: cryptoFunctions.normalize(data[i+33][1]),
            Day34: cryptoFunctions.normalize(data[i+34][1]),
            Day35: cryptoFunctions.normalize(data[i+35][1]),
            Day36: cryptoFunctions.normalize(data[i+36][1]),
            Day37: cryptoFunctions.normalize(data[i+37][1]),
            Day38: cryptoFunctions.normalize(data[i+38][1]),
            Day39: cryptoFunctions.normalize(data[i+39][1]),
            Day40: cryptoFunctions.normalize(data[i+40][1]),
            Day41: cryptoFunctions.normalize(data[i+41][1]),
            Day42: cryptoFunctions.normalize(data[i+42][1]),
            Day43: cryptoFunctions.normalize(data[i+43][1]),
            Day44: cryptoFunctions.normalize(data[i+44][1]),
            Day45: cryptoFunctions.normalize(data[i+45][1]),
            Day46: cryptoFunctions.normalize(data[i+46][1]),
            Day47: cryptoFunctions.normalize(data[i+47][1]),
            Day48: cryptoFunctions.normalize(data[i+48][1]),
            Day49: cryptoFunctions.normalize(data[i+49][1]),
            Day50: cryptoFunctions.normalize(data[i+50][1]),
        },  
        output: {Result: cryptoFunctions.normalize(data[i+predictionDays][1])}})
  }
  // console.log(trainingData)
  const dataString = JSON.stringify(trainingData, null, 2);

  // for (let i = data.length-51; i < data.length; i++) {
    // console.log(normalize(data[i][1]), ',')
  // }

  fs.writeFileSync(`./TrainingData/${crypto}TrainingData_${predictionDays-50}DayPrediction.js`, `const trainingdata = ${dataString};\n\nmodule.exports = trainingdata;`);
}
// dataManipulation('bitcoin')

// dataManipulation('solana')
module.exports = {
  dataManipulation
};