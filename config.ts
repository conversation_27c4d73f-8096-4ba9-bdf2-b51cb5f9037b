
// utils/getFaaAppUrl.ts
export function getFaaAppUrl() {
  // ――― current location pieces
  const { protocol, hostname, port } = window.location;

  // ――― handle localhost separately
  if (hostname === 'localhost' || hostname.endsWith('.localhost')) {
    // Browsers treat *.localhost as 127.0.0.1, so keep any existing sub‑part,
    // add the port if present, and keep the protocol (http/https).
    return `${protocol}//faa-app.localhost${port ? `:${port}` : ''}`;
  }

  // ――― production or any other domain
  const parts = hostname.split('.');
  const rootDomain = parts.slice(-2).join('.');        // example.com, myorg.net …
  return `${protocol}//faa-app.${rootDomain}`;
}




export const config = {
  apps: {
    aiTranslation: {
      url: "/Web-Apps/ai-translation",
      name: "AI Translation",
      privacyLastUpdated: "April 12, 2025"
    },
      "A&P App": {
      url: getFaaAppUrl(),
      name: "<PERSON>&<PERSON> Exam Prep",
      privacyLastUpdated: "July 14, 2025"
    }
  }
};

export default config;