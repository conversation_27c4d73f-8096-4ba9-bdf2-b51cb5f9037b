const express = require('express')
const brain = require('brain.js');
const fs = require('fs');
const { trainCrypto } = require('./trainCrypto2');
const app = express();
let { getHistoricalData } = require('./getHistoricalData2');
let { dataManipulation, trainingDataSave } = require('./dataManipulation2');
const { Utils } = require('./utils');
const { get } = require('http');
const http = require('http');

const NodeCache = require("node-cache");
const axios = require('axios');

const server = http.createServer(app);

function memoryUsage() {
    console.log(process.memoryUsage())
    console.log(Object.entries(process.memoryUsage()).map(([key, value]) => `${key}: ${value / 1024 / 1024} MB`))
    // global.gc()
}
const PrismaClient = require('@prisma/client').PrismaClient;
 const prisma = new PrismaClient();
// const prisma = new PrismaClient({
//     // Other configuration options...
//     datasources: {
//       db: {
//         url: 'postgres://postgres.lcmjnjdyfgxbtskezhwe:<EMAIL>:6543/postgres?schema=public&pgbouncer=true&sslmode=require&connection_limit20&pool_timeout=15',
//         max: 20, // or any other desired number
//       },
//     },
//   });
// const prisma = require('./prismaClientControl')

//
// async function processData() {
//   await getHistoricalData();
//   await ethDataManipulation();
//   const retrieveModel = require('./trainedEth.json')
//   const brainReconstruction = new brain.NeuralNetworkGPU();
//   brainReconstruction.fromJSON(retrieveModel);
// }
const port = process.env.PORT || 3532
// const port = process.env.PORT || 3533

const cache = new NodeCache();
console.log('test message #1')
// Function to start the server
function startServer() {
    app.get('/memoryusage', (req, res) => {
        memoryUsage()
        res.send(
            "<h1>Memory Usage</h1>" +
            "<p>Check the console for memory usage</p>"
        );
    })
    app.get('/cache_links', async (req, res) => {
        let CachData = null
    //     console.log('getting bitcoin 1')
    //      axios.get(req.protocol + '://' + req.get('host') + '/current/bitcoin/1').then((response) => {
    //         cache.set('bitcoin1', response.data)
    //         axios.get(req.protocol + '://' + req.get('host') + '/current/ethereum/1').then((response) => {
    //             cache.set('ethereum1', response.data)
    //             axios.get(req.protocol + '://' + req.get('host') + '/current/cardano/1').then((response) => {
    //                 cache.set('cardano1', response.data)
    //                 axios.get(req.protocol + '://' + req.get('host') + '/current/polkadot/1').then((response) => {
    //                     cache.set('polkadot1', response.data)
    //                     axios.get(req.protocol + '://' + req.get('host') + '/current/solana/1').then((response) => {
    //                         cache.set('solana1', response.data)
    //                         axios.get(req.protocol + '://' + req.get('host') + '/current/bitcoin/10').then((response) => {
    //                             cache.set('bitcoin10', response.data)
    //                             axios.get(req.protocol + '://' + req.get('host') + '/current/ethereum/10').then((response) => {
    //                                 cache.set('ethereum10', response.data)
    //                                 axios.get(req.protocol + '://' + req.get('host') + '/current/cardano/10').then((response) => {
    //                                     cache.set('cardano10', response.data)
    //                                     axios.get(req.protocol + '://' + req.get('host') + '/current/polkadot/10').then((response) => {
    //                                         cache.set('polkadot10', response.data)
    //                                         axios.get(req.protocol + '://' + req.get('host') + '/current/solana/10').then((response) => {
    //                                             cache.set('solana10', response.data)
    //                                             console.log('cache links hit')
    //                                             res.send(
    //                                                 "<h1>We Good</h1>")
    //                                             memoryUsage()
    //                                         })
    //                                     })
    //                                 })
    //                             })
    //                         })
    //                     })
    //                 })
    //             })
                
    // })
    //     } )
        cache.set('bitcoin1', CachData)
        console.log('getting ethereum 1')
        let CachData1 = (await axios.get(req.protocol + '://' + req.get('host') + '/current/ethereum/1'))
        cache.set('ethereum1', CachData1.data)
        console.log('getting cardano 1', CachData1)
        let CachData2 = (await axios.get(req.protocol + '://' + req.get('host') + '/current/cardano/1'))
        cache.set('cardano1', CachData2.data)
        console.log('getting polkadot 1')
        let CachData3 = await axios.get(req.protocol + '://' + req.get('host') + '/current/polkadot/1')
        cache.set('polkadot1', CachData3.data)
        console.log('getting solana 1')
        let CachData4 = await axios.get(req.protocol + '://' + req.get('host') + '/current/solana/1')
        cache.set('solana1', CachData4.data)
        console.log('getting bitcoin 10')
        let CachData5 = await axios.get(req.protocol + '://' + req.get('host') + '/current/bitcoin/10')
        cache.set('bitcoin10', CachData5.data)
        console.log('getting ethereum 10')
        let CachData6 = await axios.get(req.protocol + '://' + req.get('host') + '/current/ethereum/10')
        cache.set('ethereum10', CachData6.data)
        console.log('getting cardano 10')
        let CachData7 = await axios.get(req.protocol + '://' + req.get('host') + '/current/cardano/10')
        cache.set('cardano10', CachData7.data)
        console.log('getting polkadot 10')
        let CachData8 = await axios.get(req.protocol + '://' + req.get('host') + '/current/polkadot/10')
        cache.set('polkadot10', CachData8.data)
        console.log('getting solana 10')
        let CachData9 = await axios.get(req.protocol + '://' + req.get('host') + '/current/solana/10')
        cache.set('solana10', CachData9.data)
        console.log('cache links hit')
        res.send(
            "<h1>We Good</h1>")
        memoryUsage()
    })

    app.get('/manipulation', (req, res) => {
        const { manipulate } = require('./utils');
        manipulate();
    }
    )

    app.get('/manipulation2', (req, res) => {
        const { manipulate2 } = require('./utils');
        manipulate2();
    }
    )

    let pendingPromises = []

    function trackPromise(promise) {
        pendingPromises.push(promise);
        console.log('promises added', pendingPromises.length)

        promise.then(() => {
            // Remove the resolved Promise from the array
            pendingPromises = pendingPromises.filter(p => p !== promise);
            console.log('promises removed', pendingPromises.length)
        }).catch(() => {
            // Handle errors, if necessary
        });

        return promise;
    }

    app.get('/updateAll', async (req, res) => {
        let PrismaClient = require('@prisma/client').PrismaClient;
        let prisma = new PrismaClient();
        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }
        await getHistoricalData('bitcoin', prisma);
        await sleep(5000);
        await getHistoricalData('ethereum', prisma);
        await sleep(5000);
        await getHistoricalData('cardano', prisma);
        await sleep(5000);
        await getHistoricalData('polkadot', prisma);
        await sleep(5000);
        await getHistoricalData('solana', prisma);
        await sleep(5000);
        let result1 = dataManipulation('bitcoin', 1, prisma);
        await trackPromise(trainingDataSave('bitcoin', result1, 1, prisma))
        cache.del('bitcoin1')
        let result2 = dataManipulation('ethereum', 1, prisma);
        await trainingDataSave('ethereum', result2, 1, prisma)
        cache.del('ethereum1')
        let result3 = dataManipulation('cardano', 1, prisma);
        await trainingDataSave('cardano', result3, 1, prisma)
        cache.del('cardano1')
        let result4 = dataManipulation('polkadot', 1, prisma);
        await trainingDataSave('polkadot', result4, 1, prisma)
        cache.del('polkadot1')
        let result5 = dataManipulation('solana', 1, prisma);
        await trainingDataSave('solana', result5, 1, prisma)
        cache.del('solana1')
        let result6 = dataManipulation('bitcoin', 10, prisma);
        await trainingDataSave('bitcoin', result6, 10, prisma)
        cache.del('bitcoin10')
        let result7 = dataManipulation('ethereum', 10, prisma);
        await trainingDataSave('ethereum', result7, 10, prisma)
        cache.del('ethereum10')
        let result8 = dataManipulation('cardano', 10, prisma);
        await trainingDataSave('cardano', result8, 10, prisma)
        cache.del('cardano10')
        let result9 = dataManipulation('polkadot', 10, prisma);
        await trainingDataSave('polkadot', result9, 10, prisma)
        cache.del('polkadot10')
        let result10 = dataManipulation('solana', 10, prisma);
        await trainingDataSave('solana', result10, 10, prisma)
        cache.del('solana10')
        console.log('all data updated')
        await Promise.all(pendingPromises).then(() => {
            console.log('all promises resolved')
        }
        ).catch((error) => {
            console.log('error', error)
        })


        await prisma.$disconnect();
        res.send('updated');
        // restartServer();
        // return null
        return (executecache(req))
    })
    function executecache(req) {
        axios.get(req.protocol + '://' + req.get('host') + '/cache_links')
    }

    app.get('/chartData/:crypto/', async (req, res) => {
        console.log('route hit')
        const crypto = req.params.crypto
        let PriceData = require(`./RawPriceData/${crypto}PriceData.js`)
        res.json(PriceData);
    })

    app.get('/trainAll', async (req, res) => {
        trainCrypto('bitcoin', 1);
        trainCrypto('ethereum', 1);
        console.log('training cardano 1')
        trainCrypto('cardano', 1);
        trainCrypto('polkadot', 1);
        console.log('training solana 1')
        trainCrypto('solana', 1);
        console.log('training bitcoin 10')
        trainCrypto('bitcoin', 10);
        console.log('training ethereum 10')
        trainCrypto('ethereum', 10);
        console.log('training cardano 10')
        trainCrypto('cardano', 10);
        trainCrypto('polkadot', 10);
        console.log('training solana 10')
        trainCrypto('solana', 10);
        restartServer();
    })

    async function retrieveTrainedModel(name,prisma) {
        const file = await prisma.trainedModel.findMany({
            where: { crypto: name },
        });
        if (file) {
            return file;
        }
        else {
            return new error('no file found')
        }

    }

    async function retrieveTrainingData(name,prisma) {
        const file = await prisma.trainingData.findMany({
            where: { crypto: name },
        });
        if (file) {
            return file;
        }
        else {
            return new error('no file found')
        }

    }

    let bufferSize = []
    let generation = 0
let bufferCount = 0
    function prismaBuffer(prismaCall)
    {
        console.log('prisma buffer hit', bufferCount)
        bufferSize.push(prismaCall)
        bufferCount = bufferCount+ 1
        generation = generation + 1
        // if (generation ==0)
        // while (bufferSize.length > 0) {
            let data = bufferSize.shift()()
        //     return data
        // }
        // let data = prismaCall()
        return data
    }


    app.get('/:query/:crypto/:predictionDays', async (req, res) => {
        const predictionDays = parseInt(req.params.predictionDays)
        const crypto = req.params.crypto
        const query = req.params.query
        if (query == 'train') {
            console.log('training')
            trainCrypto(crypto, predictionDays);
        }
        if (query == 'new') {
             getHistoricalData(crypto);
            dataManipulation(crypto, predictionDays);
        }
        if (query == 'current' || query == 'new') {
            //   const retrieveModel = require(`./CryptoModels/trained${crypto}_${predictionDays}DayPrediction.json`) //
            const cachedData = cache.get(crypto + predictionDays);

            if (cachedData) {      ///
                console.log('cached data used')///
                res.json(cachedData)///
            }///

            if (predictionDays == 1 && !cachedData) {
                let trainedModel = await prismaBuffer(() => retrieveTrainedModel(crypto,prisma))
                   const brainReconstruction = new brain.NeuralNetworkGPU();
                    brainReconstruction.fromJSON(trainedModel[0].fileContent1);
                    const cryptoFunctions = new Utils(crypto)
                    let trainedData = await prismaBuffer(()=> retrieveTrainingData(crypto,prisma))
                        let newData = trainedData[0].fileContent1
                        // console.log(newData)
                        // console.log(brainReconstruction.run(newData[newData.length - 1].input))
                        brainReconstruction.run(newData[newData.length - 1].input)
                        let cryptoPrediction = cryptoFunctions.denormalize((brainReconstruction.run(newData[newData.length - 1].input)).Result);

                        const jsonData = {
                            crypto: crypto,
                            predictionDays: predictionDays,
                            cryptoprediction: cryptoPrediction,
                            recentprice: cryptoFunctions.recentPrice,
                            recentDate: cryptoFunctions.recentDate,
                            dateUnEdited: cryptoFunctions.dateUnEdited
                        };
                        cache.set(crypto + predictionDays, jsonData) ///

                        // Sending JSON response
                        res.json(jsonData);
                    }
                
            
            if (predictionDays == 10 && !cachedData) {
                let trainedModel = await retrieveTrainedModel(crypto,prisma)
                    const brainReconstruction = new brain.NeuralNetworkGPU();
                    brainReconstruction.fromJSON(trainedModel[0].fileContent10);
                    const cryptoFunctions = new Utils(crypto)
                    let trainedData = await retrieveTrainingData(crypto,prisma)
                        let newData = trainedData[0].fileContent10
                        // console.log(newData)
                        // console.log(brainReconstruction.run(newData[newData.length - 1].input))
                        brainReconstruction.run(newData[newData.length - 1].input)
                        let cryptoPrediction = cryptoFunctions.denormalize((brainReconstruction.run(newData[newData.length - 1].input)).Result);


                        const jsonData = {
                            crypto: crypto,
                            predictionDays: predictionDays,
                            cryptoprediction: cryptoPrediction,
                            recentprice: cryptoFunctions.recentPrice,
                            recentDate: cryptoFunctions.recentDate,
                            dateUnEdited: cryptoFunctions.dateUnEdited
                        };
                        cache.set(crypto + predictionDays, jsonData) ///
                        // Sending JSON response
                        res.json(jsonData);
                    }
                
            
            // const brainReconstruction = new brain.NeuralNetworkGPU();
            // brainReconstruction.fromJSON(retrieveModel);
            // const cryptoFunctions = new Utils(crypto)
            // let trainingdata = require(`./TrainingData/${crypto}TrainingData_${predictionDays}DayPrediction.js`);//
            // console.log(brainReconstruction.run(trainingdata[trainingdata.length - 1].input))
            // let cryptoPrediction = cryptoFunctions.denormalize((brainReconstruction.run(trainingdata[trainingdata.length - 1].input)).Result);
            // let cryptoPrediction = cryptoFunctions.denormalize(brainReconstruction.run(trainingdata[trainingdata.length - 1].input));

            // Your JSON data
            // const jsonData = {
            //     crypto: crypto,
            //     predictionDays: predictionDays,
            //     cryptoprediction: cryptoPrediction,
            //     recentprice: cryptoFunctions.recentPrice,
            //     recentDate: cryptoFunctions.recentDate,
            //     dateUnEdited: cryptoFunctions.dateUnEdited
            // };
            // Sending JSON response
            // res.json(jsonData);
        }
    });



    app.get('/', (req, res) => {
        res.send(
            "<h1>Server is running!</h1>" +
            "<p>lets get it started in here!</p>" +
            "<h1>Endpoints</h1>" +
            "<p>/trainAll</p>" +
            "<p>/updateAll**</p>" +
            "<p>/:query/:crypto/:predictionDays<p>" +
            "<h2>Query Options</h2>" +
            "<p>train</p>" +
            "<p>new</p>" +
            "<p>current</p>" +
            "<h2>Crypto Options</h2>" +
            "<p>bitcoin</p>" +
            "<p>ethereum</p>" +
            "<p>cardano</p>" +
            "<p>polkadot</p>" +
            "<p>solana</p>" +
            "<h2>Prediction Days Options</h2>" +
            "<p>1</p>" +
            "<p>10</p>" +
            "<h2>Example</h2>" +
            "<p>/train/bitcoin/1</p>" +
            "<p>/new/ethereum/10</p>" +
            "<p>/current/cardano/1</p>"


        );
    });

    // Start the Express server
    // app.listen(port, () => {
    //   console.log(`Server is listening at http://localhost:${port}`);
    // });

    server.listen(port, () => {
        console.log(`Server is listening at http://localhost:${port}`);
    });

}

// Initial server startup
startServer();

function restartServer() {
    console.log('Restarting server...');

    // Close existing connections gracefully
    server.close(() => {
        // Start the server again after a short delay
        setTimeout(() => {
            startServer();
        }, 1000);
    });
}
// Handle cleanup when the server is stopped
process.on('SIGINT', () => {
    console.log('Server shutting down');
    process.exit();
});
