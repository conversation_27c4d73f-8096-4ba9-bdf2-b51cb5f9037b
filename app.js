const express = require('express')
const brain = require('brain.js');
const fs = require('fs');
const { trainCrypto } = require('./trainCrypto');
const app = express();
const { getHistoricalData } = require('./getHistoricalData');
const { dataManipulation } = require('./dataManipulation');
const {Utils} = require('./utils');
const { get } = require('http');
const http = require('http');

const server = http.createServer(app);

 //
// async function processData() {
//   await getHistoricalData();
//   await ethDataManipulation();
//   const retrieveModel = require('./trainedEth.json')
//   const brainReconstruction = new brain.NeuralNetworkGPU();
//   brainReconstruction.fromJSON(retrieveModel);
// }

const port = process.env.PORT || 3532;

// Function to start the server
function startServer() {

app.get('/manipulation', (req,res) => {
  const { manipulate } = require('./utils');
  manipulate();
}
)

app.get('/manipulation2', (req,res) => {
  const { manipulate2 } = require('./utils');
  manipulate2();
}
)



app.get('/updateAll', async (req,res) => {

  function sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
  await getHistoricalData('bitcoin');
  await sleep(5000);
  await getHistoricalData('ethereum');
  await sleep(5000);
  await getHistoricalData('cardano');
  await sleep(5000);
  await getHistoricalData('polkadot');
  await sleep(5000);
  await getHistoricalData('solana');
  await sleep(5000);
  dataManipulation('bitcoin', 1);
  dataManipulation('ethereum', 1);
  dataManipulation('cardano', 1);
  dataManipulation('polkadot', 1);
  dataManipulation('solana', 1);
  dataManipulation('bitcoin', 10);
  dataManipulation('ethereum', 10);
  dataManipulation('cardano', 10);
  dataManipulation('polkadot', 10);
  dataManipulation('solana', 10);

  restartServer();
  res.send('updated');

  
})

app.get('/chartData/:crypto/', async (req,res) => {
  console.log('route hit')
  const crypto = req.params.crypto
  let PriceData = require(`./RawPriceData/${crypto}PriceData.js`)
  res.json(PriceData);})

app.get('/trainAll',  (req,res) => {
  trainCrypto('bitcoin', 1);
  trainCrypto('ethereum', 1);
  console.log('training cardano 1')
  trainCrypto('cardano', 1);
  trainCrypto('polkadot', 1);
  console.log('training solana 1')
  trainCrypto('solana', 1);
  console.log('training bitcoin 10')
  trainCrypto('bitcoin', 10);
  console.log('training ethereum 10') 
  trainCrypto('ethereum', 10);
  console.log('training cardano 10')
  trainCrypto('cardano', 10);
  trainCrypto('polkadot', 10);
  console.log('training solana 10')
  trainCrypto('solana', 10);
  restartServer();
})


app.get('/:query/:crypto/:predictionDays', async (req, res) => {
  const predictionDays = parseInt(req.params.predictionDays)
  const crypto = req.params.crypto
  const query = req.params.query
  if (query == 'train') {
    console.log('training')
    await trainCrypto(crypto,predictionDays);
  }
  if (query == 'new') {
    await getHistoricalData(crypto);
    dataManipulation(crypto,predictionDays);
  }
  if (query == 'current' || query == 'new') {
  const retrieveModel = require(`./CryptoModels/trained${crypto}_${predictionDays}DayPrediction.json`)
  const brainReconstruction = new brain.NeuralNetworkGPU();
  brainReconstruction.fromJSON(retrieveModel);
  const cryptoFunctions = new Utils(crypto)
  let trainingdata = require(`./TrainingData/${crypto}TrainingData_${predictionDays}DayPrediction.js`);
  console.log(brainReconstruction.run(trainingdata[trainingdata.length - 1].input))
  let cryptoPrediction = cryptoFunctions.denormalize((brainReconstruction.run(trainingdata[trainingdata.length - 1].input)).Result);
  // let cryptoPrediction = cryptoFunctions.denormalize(brainReconstruction.run(trainingdata[trainingdata.length - 1].input));

  // Your JSON data
  const jsonData = {
    crypto: crypto,
    predictionDays: predictionDays,
    cryptoprediction: cryptoPrediction,
    recentprice: cryptoFunctions.recentPrice,
    recentDate: cryptoFunctions.recentDate,
    dateUnEdited: cryptoFunctions.dateUnEdited
  };
  // Sending JSON response
  res.json(jsonData);
}
});



app.get('/', (req, res) => {
  res.send(
    "<h1>Server is running!</h1>" +
    "<p>lets get it started in here!</p>"+
    "<h1>Endpoints</h1>"+
    "<p>/trainAll</p>"+
    "<p>/updateAll</p>"+
    "<p>/:query/:crypto/:predictionDays<p>"+
    "<h2>Query Options</h2>"+
    "<p>train</p>"+
    "<p>new</p>"+
    "<p>current</p>"+
    "<h2>Crypto Options</h2>"+
    "<p>bitcoin</p>"+
    "<p>ethereum</p>"+
    "<p>cardano</p>"+
    "<p>polkadot</p>"+
    "<p>solana</p>"+
    "<h2>Prediction Days Options</h2>"+
    "<p>1</p>"+
    "<p>10</p>"+
    "<h2>Example</h2>"+
    "<p>/train/bitcoin/1</p>"+
    "<p>/new/ethereum/10</p>"+
    "<p>/current/cardano/1</p>"

    
    );
});

// Start the Express server
// app.listen(port, () => {
//   console.log(`Server is listening at http://localhost:${port}`);
// });

server.listen(port, () => {
  console.log(`Server is listening at http://localhost:${port}`);
});

}

// Initial server startup
startServer();

function restartServer() {
  console.log('Restarting server...');

  // Close existing connections gracefully
  server.close(() => {
    // Start the server again after a short delay
    setTimeout(() => {
      startServer();
    }, 1000);
  });
}
// Handle cleanup when the server is stopped
process.on('SIGINT', () => {
  console.log('Server shutting down');
  process.exit();
});
