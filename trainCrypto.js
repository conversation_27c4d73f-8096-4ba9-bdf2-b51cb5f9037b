function trainCrypto(crypto,predictionDays) {

const brain = require('brain.js');
const {Utils} = require('./utils');
// const { getHistoricalData } = require('./getHistoricalData');
const { getHistoricalData } = require('./getHistoricalDataFix');

const { dataManipulation } = require('./dataManipulation');

const fs = require('fs');
console.log(typeof predictionDays)
trainNeuralnet(crypto,predictionDays)

async function trainNeuralnet(crypto,predictionDays) {
    await getHistoricalData(crypto);
    dataManipulation(crypto,predictionDays);
    let trainingdata = require(`./TrainingData/${crypto}TrainingData_${predictionDays}DayPrediction.js`);
    const cryptoFunctions = new Utils(crypto)

    const net = new brain.NeuralNetworkGPU({
        hiddenLayers: [200, 200],
        activation: 'leaky-relu'
    });

    net.train(trainingdata, {
        // learningRate: 0.05,
        iterations: 200,
        errorThresh: 0.0000000000000000001,
        log: true,
        logPeriod: 1,
    });
    const json = net.toJSON();
    fs.writeFileSync(`./CryptoModels/trained${crypto}_${predictionDays}DayPrediction.json`, JSON.stringify(json));
    console.log(trainingdata[trainingdata.length - 1].input)
    // Test the trained neural network
    const output1 = net.run(
        trainingdata[trainingdata.length - 1].input
    );
    // Display the results
    console.log('First test', output1);
    console.log('First test denormalized', cryptoFunctions.denormalize(output1));
}
}
//    trainCrypto('solana');

module.exports = {
    trainCrypto
};
