[debug] [2023-08-02T14:54:50.953Z] ----------------------------------------------------------------------
[debug] [2023-08-02T14:54:50.955Z] Command:       /home/<USER>/.nvm/versions/node/v19.9.0/bin/node /home/<USER>/.nvm/versions/node/v19.9.0/bin/firebase deploy
[debug] [2023-08-02T14:54:50.956Z] CLI Version:   12.4.7
[debug] [2023-08-02T14:54:50.956Z] Platform:      linux
[debug] [2023-08-02T14:54:50.956Z] Node Version:  v19.9.0
[debug] [2023-08-02T14:54:50.956Z] Time:          Wed Aug 02 2023 10:54:50 GMT-0400 (Eastern Daylight Time)
[debug] [2023-08-02T14:54:50.956Z] ----------------------------------------------------------------------
[debug] 
[debug] [2023-08-02T14:54:51.030Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2023-08-02T14:54:51.031Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2023-08-02T14:54:51.031Z] [iam] checking project malcolmbase for permissions ["firebase.projects.get","firebasehosting.sites.update"]
[debug] [2023-08-02T14:54:51.033Z] >>> [apiv2][query] POST https://cloudresourcemanager.googleapis.com/v1/projects/malcolmbase:testIamPermissions [none]
[debug] [2023-08-02T14:54:51.033Z] >>> [apiv2][(partial)header] POST https://cloudresourcemanager.googleapis.com/v1/projects/malcolmbase:testIamPermissions x-goog-quota-user=projects/malcolmbase
[debug] [2023-08-02T14:54:51.034Z] >>> [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/malcolmbase:testIamPermissions {"permissions":["firebase.projects.get","firebasehosting.sites.update"]}
[debug] [2023-08-02T14:54:51.341Z] <<< [apiv2][status] POST https://cloudresourcemanager.googleapis.com/v1/projects/malcolmbase:testIamPermissions 200
[debug] [2023-08-02T14:54:51.341Z] <<< [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/malcolmbase:testIamPermissions {"permissions":["firebase.projects.get","firebasehosting.sites.update"]}
[debug] [2023-08-02T14:54:51.343Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/malcolmbase [none]
[debug] [2023-08-02T14:54:51.653Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/malcolmbase 200
[debug] [2023-08-02T14:54:51.654Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/malcolmbase {"projectId":"malcolmbase","projectNumber":"498365935094","displayName":"MalcolmBase","name":"projects/malcolmbase","resources":{"hostingSite":"malcolmbase"},"state":"ACTIVE","etag":"1_0829f491-2e50-41dc-9e36-5972890e21bc"}
[debug] [2023-08-02T14:54:56.248Z] >>> [apiv2][query] GET https://firebasehosting.googleapis.com/v1beta1/projects/malcolmbase/sites pageToken=&pageSize=10
[debug] [2023-08-02T14:54:56.769Z] <<< [apiv2][status] GET https://firebasehosting.googleapis.com/v1beta1/projects/malcolmbase/sites 200
[debug] [2023-08-02T14:54:56.770Z] <<< [apiv2][body] GET https://firebasehosting.googleapis.com/v1beta1/projects/malcolmbase/sites {"sites":[{"name":"projects/malcolmbase/sites/malcolmbase","defaultUrl":"https://malcolmbase.web.app","type":"DEFAULT_SITE"}]}
[debug] [2023-08-02T14:54:56.771Z] >>> [apiv2][query] GET https://firebasehosting.googleapis.com/v1beta1/projects/malcolmbase/sites 
[debug] [2023-08-02T14:54:56.967Z] <<< [apiv2][status] GET https://firebasehosting.googleapis.com/v1beta1/projects/malcolmbase/sites 200
[debug] [2023-08-02T14:54:56.968Z] <<< [apiv2][body] GET https://firebasehosting.googleapis.com/v1beta1/projects/malcolmbase/sites {"sites":[{"name":"projects/malcolmbase/sites/malcolmbase","defaultUrl":"https://malcolmbase.web.app","type":"DEFAULT_SITE"}]}
[debug] [2023-08-02T14:54:56.969Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/malcolmbase/webApps/-/config [none]
[debug] [2023-08-02T14:54:57.428Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/malcolmbase/webApps/-/config 200
[debug] [2023-08-02T14:54:57.428Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/malcolmbase/webApps/-/config {"projectId":"malcolmbase","appId":"1:498365935094:web:5f1bf245dc6f41fefd2bd7","storageBucket":"malcolmbase.appspot.com","apiKey":"AIzaSyDBe0TWbDmfNie1j5b4B5E4t7XlZUDKnRA","authDomain":"malcolmbase.firebaseapp.com","messagingSenderId":"498365935094","measurementId":"G-Q1ETH20DTQ"}
[info] 
   Thank you for trying our early preview of Next.js support on Firebase Hosting.
   During the preview, support is best-effort and breaking changes can be expected. Proceed with caution.

   Documentation: https://firebase.google.com/docs/hosting/frameworks/nextjs
   File a bug: https://github.com/firebase/firebase-tools/issues/new?template=bug_report.md
   Submit a feature request: https://github.com/firebase/firebase-tools/issues/new?template=feature_request.md

   We'd love to learn from you. Express your interest in helping us shape the future of Firebase Hosting: https://goo.gle/41enW5X

[info] - info Creating an optimized production build...

[info] 

[info] 🌼 daisyUI 3.2.1 https://daisyui.com

[info] ╰╮
 ╰─ ✔︎ [ 2 ] themes are enabled. You can add more themes or make your own theme:
      https://daisyui.com/docs/themes

    ❤︎ Support daisyUI: https://opencollective.com/daisyui

[info] 

[info] 
🌼 daisyUI 3.2.1 https://daisyui.com

[info] ╰╮

[info]  ╰─ ✔︎ [ 2 ] themes are enabled. You can add more themes or make your own theme:
      https://daisyui.com/docs/themes

    ❤︎ Support daisyUI: https://opencollective.com/daisyui


[info] 
🌼 daisyUI 3.2.1 https://daisyui.com

[info] ╰╮

[info]  ╰─ ✔︎ [ 2 ] themes are enabled. You can add more themes or make your own theme:
      https://daisyui.com/docs/themes

    ❤︎ Support daisyUI: https://opencollective.com/daisyui


[info] - info Compiled successfully

[info] - info Linting and checking validity of types...

[info] - warn No ESLint configuration detected. Run next lint to begin setup

[info] - info Collecting page data...

[info] - info Generating static pages (0/14)

[info] - info Generating static pages (3/14)

[info] - info Generating static pages (6/14)

[info] - info Generating static pages (10/14)

[info] Document written with ID:  6ju4V40zRJqpMfSgNMj4

[info] Ml4sMGwtV5iP9Tf62ILq => [object Object]

[info] aYLZz3S8zN1c5SaMRY31 => [object Object]

[info] 
Error occurred prerendering page "/firestore". Read more: https://nextjs.org/docs/messages/prerender-error
ReferenceError: getDoc is not defined
    at GET (/home/<USER>/school/nextjs-flask/.next/server/app/firestore/route.js:241:21)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async /home/<USER>/school/nextjs-flask/.next/server/chunks/501.js:5757:37

[info] - info Generating static pages (14/14)

[info] 
> Export encountered errors on following paths:
	/firestore/route: /firestore

[debug] [2023-08-02T14:56:18.095Z] Error: ENOENT: no such file or directory, open '/home/<USER>/school/nextjs-flask/.next/export-marker.json'
[error] 
[error] Error: An unexpected error has occurred.
