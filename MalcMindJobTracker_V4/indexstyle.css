/* Basic Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #9febe9;
    padding: 20px;
}

h1, h2, h3 {
    color: #333;
}

/* Layout and Alignment */
.container {
    width: 90%;
    margin: auto;
    overflow: hidden;
}

/* Header */
.job_header {
    background: #ffffff;
    padding: 20px;
    margin-bottom: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.company_logo img {
    height: 60px; /* Adjusts the logo size */
    margin-right: 20px;
    vertical-align: middle;
}

.job_title {
    display: inline-block;
    vertical-align: middle;
    font-size: 24px;
    color: #0a0a0a;
}

.hiring_company, .hiring_location {
    display: block;
    font-size: 18px;
    color: #666;
    text-decoration: none;
    margin: 5px 0;
}

.hiring_location {
    font-style: italic;
}

/* Job Details */
.job_details {
    background: #fff;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.job_content ul {
    list-style-type: none;
}

.job_characteristics_item {
    background: #e9e9e9;
    margin: 10px 0;
    padding: 10px;
    border-radius: 5px;
}

.job_charateristics_item_text, .job_characteristics_data {
    font-weight: bold;
}

/* Job Description */
.job_description p {
    margin-bottom: 10px;
}

/* Additional Styling as needed for other sections */
.qualification_grade_badge {
    display: inline-block;
    background: #007bff;
    color: white;
    padding: 5px 10px;
    border-radius: 5px;
    cursor: pointer;
}

.qualification_grade_badge:hover {
    background: #0056b3;
}

/* Footer Styles */
.job_more_section p {
    background: #ddd;
    padding: 10px;
    border-radius: 5px;
}

/* Similar Jobs Card */
.SimilarJobCard {
    background: #fff;
    margin-bottom: 20px;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.job_card {
    text-decoration: none;
    color: inherit;
}

.job_card:hover {
    color: #0056b3;
}
