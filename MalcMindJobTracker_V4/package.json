{"name": "malcmindjobtracker", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest --environment='happy-dom'"}, "dependencies": {"interactjs": "^1.10.26", "jotai": "^2.7.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-select": "^5.8.0"}, "devDependencies": {"@types/react": "^18.2.56", "@types/react-dom": "^18.2.19", "@typescript-eslint/eslint-plugin": "^7.0.2", "@typescript-eslint/parser": "^7.0.2", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.18", "daisyui": "^4.7.2", "eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "happy-dom": "^14.12.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.2.2", "vite": "^5.1.4", "vitest": "^1.6.0"}}