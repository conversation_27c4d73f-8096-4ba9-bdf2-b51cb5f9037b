




async function getit() {
    try {
      let result = await fetch("https://0aba00cf04d16ed7824310dc00c0003f.web-security-academy.net/login", {
        "headers": {
          "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
          "accept-language": "en-US,en;q=0.9",
          "cache-control": "max-age=0",
          "content-type": "application/x-www-form-urlencoded",
          "priority": "u=0, i",
          "sec-ch-ua": "\"Chromium\";v=\"121\", \"Not A(Brand\";v=\"99\"",
          "sec-ch-ua-mobile": "?0",
          "sec-ch-ua-platform": "\"Windows\"",
          "sec-fetch-dest": "document",
          "sec-fetch-mode": "navigate",
          "sec-fetch-site": "same-origin",
          "sec-fetch-user": "?1",
          "upgrade-insecure-requests": "1",
          "cookie": "session=2wQrG4ODTu84oJ45dp0yLgE4lFaUZEhM",
          "Referer": "https://0aba00cf04d16ed7824310dc00c0003f.web-security-academy.net/login",
          "Referrer-Policy": "strict-origin-when-cross-origin"
        },
        "body": "username=athexdna&password=re",
        "method": "POST"
      });
  
      // Ensure the HTTP request was successful (status code 200)
      if (!result.ok) {
        throw new Error(`HTTP error! Status: ${result.status}`);
      }
  
      // Parse the response as text or JSON, depending on your needs
      return await result.text(); // or result.json() if the response is JSON
    } catch (error) {
      console.error("Error:", error);
      throw error; // You may want to handle the error more gracefully
    }
  }
  
  async function main() {
    try {
      const response = await getit();
      console.log(response.length);
    } catch (error) {
      console.error("Error:", error);
    }
  }
  
  // Call the main function
  let cool =  main();
  console.log(cool)