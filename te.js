


const brain = require('brain.js');


const config = {
    hiddenLayers: [100,100], // array of ints for the sizes of the hidden layers in the network
    activation: 'leaky-relu', // supported activation types: ['sigmoid', 'relu', 'leaky-relu', 'tanh'],
    leakyReluAlpha: 0.01, // supported for activation type 'leaky-relu'
  };
  
  // create a simple feed-forward neural network with backpropagation
  const net = new brain.NeuralNetwork(config);
  
//# = 1
//. = 2
//* = 3
  net.train([
    { input: {shape: [
        "*#..#",
        ".#*#.",
        "*...*", ]
       ,
    K:  [2/100]}, output: [3.8333333333333353/100] },
  ]);
  
  const output = net.run([    	
    "###################",
    "#*###############*#",
    "#.....#######.....#",
    "#*###*.#.*.#.*###*#",
    "#*####*.*#*.*####*#",
    "#*#####*###*#####*#",
    "###################"]
   
   
); 

  console.log(output*100)