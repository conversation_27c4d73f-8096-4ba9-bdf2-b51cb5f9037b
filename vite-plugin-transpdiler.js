import { createFilter } from '@rollup/pluginutils'
import { createTransformer } from 'vite-plugin-transpiler'
import transformPythonicExpressions from '@babel/plugin-transform-pythonic-expressions'

export default function vitePluginTranspiler(options = {}) {
  const filter = createFilter(options.include, options.exclude)

  const transformer = createTransformer(
    {
      plugins: [
        transformPythonicExpressions,
      ],
    },
    {
      ...options,
      sourceMaps: 'inline',
    }
  )

  return {
    name: 'vite-plugin-transpiler',
    transform(code, id) {
      if (!filter(id)) {
        return null
      }

      return transformer.transform(code, id)
    },
  }
}